---
sidebar_position: -1900
sidebar_label: v1.9.0
title: Bika.ai v1.9.0 Release Notes
keywords: theme optimization, icon customization, keyboard shortcuts, recycle bin improvements, timezone adaptation, automation layout, chart calculation, dashboard fixes, security permissions, user experience
---

# v1.9.0 Release Notes

Release Date: 2025-08-04

## 🚀 New Features & Template Improvements

- Launched the brand new Database v2 APIs, offering more stable and efficient data interfaces. Advanced filtering is now supported for batch record reading, further improving data processing capability and flexibility.
- The AI field now supports integration with custom models, enabling users to configure AI models tailored to their business needs.
- Added tutorial videos for AI Pages to help users quickly get started and better understand how to use the AI Page feature.
- The organization chart in Contacts will now auto-center on load, optimizing the overall layout and display.

## 🔧 Fixes

- Pie chart values on dashboard cards now display correctly as percentages, making it easier for users to understand data distribution.
- Fixed an issue where dashboard chart widgets would refresh twice after saving or resizing.
- Resolved a page crash when creating a new node from a template.
- Fixed an issue where selected linked records in forms could not be deselected, improving flexibility in managing related data.
- The issue where variables entered in Automation Actions’ variable mode would not save after selecting the variable picker has been resolved.
- Node icons on folder pages now automatically update, ensuring node information is displayed accurately.
- Fixed an error that could occur when publishing folders to the template center under certain circumstances, increasing the stability of template publishing.