/**
 * Application runtime environment
 */
export type AppEnv =
  // Self-hosted, middleware needs to intercept and remove the license
  | 'SELF-HOSTED'
  // localhost, local development environment
  // Admin management backend environment, such as a standalone editor
  | 'LOCAL'
  // dev.xxx
  | 'INTEGRATION'
  // staging.xxx
  | 'STAGING'
  // Production environment, corresponding to the release version
  | 'PRODUCTION'
  | 'DESKTOP';

export function isInUnitTest() {
  if (process.env.NODE_ENV === 'test') {
    return true;
  }
  return false;
}

export function isInCI() {
  if (process.env.CI) {
    return true;
  }
  return false;
}

export const BIKA_PRODUCTION_URL = 'https://bika.ai';

/**
 * Get the app runtime environment. Note: This method can only be used on the server side, not on the client side.
 *
 * @returns
 */
export function getAppEnv(): AppEnv {
  if (typeof process === 'undefined') {
    // Not next.js or node.js, possibly storybook/client
    return 'LOCAL';
  }

  if (process.env.SITE_ADMIN) {
    return 'LOCAL';
  }
  const hostname = process.env.APP_HOSTNAME;

  if (!hostname) {
    return 'SELF-HOSTED';
  }

  if (hostname.includes('local') || hostname.includes('127.0.0.1') || hostname.includes('localhost')) {
    return 'LOCAL';
  }

  if (hostname === 'https://dev.bika.ai' || hostname === 'https://dev.toolsdk.ai') {
    return 'INTEGRATION';
  }
  if (hostname === 'https://staging.bika.ai' || hostname === 'https://staging.toolsdk.ai') {
    return 'STAGING';
  }
  if (hostname === BIKA_PRODUCTION_URL || hostname === 'https://toolsdk.ai') {
    return 'PRODUCTION';
  }

  // All other cases return as a self-hosted version
  return 'SELF-HOSTED';
}

export type FrameworkType = 'NEXT' | 'VITE' | 'STORYBOOK';
export function getFrameworkType(): FrameworkType {
  if (process.env.STORYBOOK) return 'STORYBOOK';
  if (process.env.VITE) {
    return 'VITE';
  }
  return 'NEXT';
}

/**
 * Get the release date by reading the environment variable
 * @returns
 */
export function getReleaseDate() {
  if (!process.env.RELEASE_DATE && getAppEnv() === 'PRODUCTION') {
    console.warn('env RELEASE_DATE is not set');
  }
  return process.env.RELEASE_DATE ? new Date(process.env.RELEASE_DATE) : new Date(2025, 0, 1, 0, 0, 0);
}

export function getVersion() {
  // If client component, next.config.js
  return process.env.VERSION as string;
}
/**
 * Get the version number and determine the version type in semver: alpha, beta, rc, release
 */
// export function isReleaseVersiVjjpe() {
//   const version = getVersion();
//   if (version && version.includes('-release')) return true;

//   return false;
// }

/**
 * Check if performance monitoring is enabled, printing some performance debugging information such as database request performance
 *
 * @returns
 */
export function isPerfHook() {
  if (process.env.PERF_HOOK === 'true') {
    return true;
  }
  return false;
}

export function appHostName() {
  if (process.env.APP_HOSTNAME) {
    return process.env.APP_HOSTNAME;
  }
  return '';
}

export function storagePublicUrl() {
  return process.env.STORAGE_PUBLIC_URL;
}

export function ignoreAICreditEnough(): boolean {
  return process.env.IGNORE_AI_CREDIT_ENOUGH === 'true';
}
