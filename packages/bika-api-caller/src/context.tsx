'use client';

import { QueryClientProvider } from '@tanstack/react-query';
import React, { useMemo, createContext, useContext } from 'react';
import toastFunc from 'react-hot-toast';
import { getVersion } from 'sharelib/app-env';
import { useLocale } from '@bika/contents/i18n/context';
import { LOCALE_HEADER, TIMEZONE_HEADER } from '@bika/types/shared';
import { APICaller } from './api-caller';
import { TRPCReact, type TRPCOriginClient, type TRPCReactType } from './consts';

export interface IApiCallerContext {
  // apiCaller: APICaller;
  pingLattency: () => Promise<number>;
  newSse(): { eventSource: EventSource; onlineSessionId: string };

  /**
   * @deprecated: use `trpcQuery` instead
   */
  trpc: TRPCOriginClient;
  trpcQuery: TRPCReactType;
}
export const ApiCallerContext = createContext<IApiCallerContext>(null!);

/**
 * 虚构一个API Caller，用于story环境，不走真实的API
 */
export function MockAPICallerProvider({
  value,
  children,
}: {
  value: Partial<IApiCallerContext>;
  children: React.ReactNode;
}) {
  return <ApiCallerContext.Provider value={value as IApiCallerContext}>{children}</ApiCallerContext.Provider>;
}

/**
 * 想使用tRPC？引用这个context provider！
 *
 * 这是会New 一个API Caller
 *
 * 如果你想做mock API，那么使用MockAPICallerProvider
 *
 * @param param0
 * @returns
 */
export function APICallerProvider({
  headers,
  timezone,
  children,
  bikaBasePath,
  toast,
}: {
  headers?: Headers;
  timezone?: string;
  children: React.ReactNode;
  bikaBasePath?: string;
  toast?: typeof toastFunc;
}) {
  const localeContext = useLocale();

  const theTimezone = useMemo(() => {
    if (timezone) {
      return timezone;
    }
    const { timeZone } = Intl.DateTimeFormat().resolvedOptions();
    return timeZone;
  }, [timezone]);

  const theHeaders = useMemo(() => {
    const newHeaders = headers ? new Headers(headers) : new Headers();
    // ypu can set any http headers you want here
    newHeaders.set('x-trpc-source', 'nextjs');
    newHeaders.set('x-bika-version', getVersion());
    newHeaders.set(LOCALE_HEADER, localeContext ? localeContext.lang : 'en');
    newHeaders.set(TIMEZONE_HEADER, theTimezone);
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('auth-token');
      if (token) {
        newHeaders.set('Authorization', `Bearer ${token}`);
      }
    }
    return Object.fromEntries(newHeaders);
  }, [headers, localeContext?.lang, theTimezone]);

  const apiCaller = useMemo<APICaller>(
    () => new APICaller({ bikaBasePath, headers: theHeaders, toast }),
    [theHeaders, localeContext?.lang, theTimezone, bikaBasePath],
  );
  const reactQueryClient = useMemo(() => apiCaller.reactQueryClient, [apiCaller]);
  const trpcOriginClient = useMemo(() => apiCaller.trpcClient, [apiCaller]);
  const trpcReactClient = useMemo(() => apiCaller.trpcReactClient, [apiCaller]);

  return (
    <ApiCallerContext.Provider
      value={{
        newSse: apiCaller.newSse.bind(apiCaller),
        pingLattency: apiCaller.pingLattency,
        trpc: trpcOriginClient,
        trpcQuery: TRPCReact,
      }}
    >
      <TRPCReact.Provider client={trpcReactClient} queryClient={reactQueryClient}>
        <QueryClientProvider client={reactQueryClient}>
          {/* temporarily disabled for warning */}
          {/* <ReactQueryDevtools initialIsOpen={true} client={trpcReactQueryClient} /> */}
          {children}
        </QueryClientProvider>
      </TRPCReact.Provider>
    </ApiCallerContext.Provider>
  );
}
export function useApiCaller() {
  const context = useContext(ApiCallerContext);
  return context;
}

export const useTRPCQuery: () => TRPCReactType = () => {
  const { trpcQuery } = useApiCaller();
  return trpcQuery;
};

/**
 * This is the tRPC instance used by the client
 */
export const useTRPC: () => TRPCOriginClient = () => {
  const { trpc } = useApiCaller();
  return trpc;
};
