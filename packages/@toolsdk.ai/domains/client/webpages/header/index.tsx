'use client';

import { SignInButton, SignedIn, SignedOut, UserButton } from '@clerk/nextjs';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';
// import { useLocale } from '@bika/contents/i18n/context';
import { DesktopHeader } from '@bika/domains/website/client/header/header';
import _ from 'lodash';
import { getAppEnv } from 'sharelib/app-env';
import { useUIFrameworkContext } from '@bika/ui/framework/context';
import { MobileHeader } from './mobile-header';
import DropdownMenu from './dropdown-menu';
import { Button } from '@bika/ui/button';

export function Header() {
  const frameworkCtx = useUIFrameworkContext();
  const env = getAppEnv();

  if (frameworkCtx.isMobile) {
    return (
      <MobileHeader>
        <DropdownMenu />
      </MobileHeader>
    );
  }

  return (
    <DesktopHeader
      menu={_.compact([
        {
          label: 'Marketplace',
          href: `/marketplace`,
          exact: true,
        },
        {
          label: 'My Favorites',
          href: `/me/favorites`,
        },
        env !== 'PRODUCTION' && {
          // 生产环境先隐藏
          label: 'Help',
          href: `/help`,
        },
        {
          label: 'Submit',
          // href: `/submit`,
          href: `https://github.com/toolsdk-ai/awesome-mcp-registry`,
          target: '_blank',
        },
      ])}
      logo={
        <Link href={`/`}>
          <Image alt="logo" src="/toolsdk/logo.png" width={180} height={32} />
        </Link>
      }
      renderUserButton={
        <>
          <SignedOut>
            <SignInButton>
              <Button size="lg">Sign In</Button>
            </SignInButton>
          </SignedOut>
          <SignedIn>
            <UserButton>
              <UserButton.MenuItems>
                <UserButton.Action
                  label="Developer Tokens"
                  labelIcon={<>&lt;/&gt;</>}
                  onClick={() => window.open('/me/developer')}
                />
              </UserButton.MenuItems>
            </UserButton>
          </SignedIn>
        </>
      }
    />
  );
}
