import { trpcServer } from '@hono/trpc-server';
import type { FetchCreateContextFnOptions } from '@trpc/server/adapters/fetch';
import { Hono } from 'hono';
import { compress } from 'hono/compress';
import { logger } from 'hono/logger';
import systemAPIs from './api-system';
import callbacksAPIs from './api-callbacks';
import openAPIs from './openapi';
import edgeAPIs from './edge';
import { createFetchRequestContext } from './trpc/trpc-context';
import { appRouter } from './trpc/trpc-router';
import { cors } from 'hono/cors';

const app = new Hono();

app.use(logger());

// app.use(compress());

app.use('*', async (c, next) => {
  if (c.req.path.startsWith('/api/sse')) {
    await next(); // 跳过压缩中间件
  } else {
    await compress()(c, next); // 应用压缩中间件
  }
});

// tRPC
app.use(
  '/api/trpc/*',
  trpcServer({
    endpoint: '/api/trpc',
    router: appRouter,
    createContext: async (opts: FetchCreateContextFnOptions) => {
      const context = await createFetchRequestContext(opts);

      return context as unknown as Record<string, unknown>;
    },
  }),
);

// 其余的所有api应用这个middleware， trpc不被应用
app.use('/*', async (_c, next) =>
  // sendAuditLog(c.req.raw, undefined, undefined, 'http', c.req.url, c.req.raw.body);
  next(),
);

app.use(
  '/*',
  cors({
    origin: '*',
    allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  }),
);

export const apiRoutes = app
  // Home, System
  // .route('/', systemAPIs)
  .route('/api', systemAPIs)
  .route('/api/callbacks', callbacksAPIs)
  .route('/api/openapi', openAPIs)
  .route('/api/edge', edgeAPIs);

export default app;
