import { Hono } from 'hono';
import { AuthSO } from '@toolsdk.ai/domain/server/auth-so';
import { getAppEnv } from 'sharelib/app-env';

// System / Health Check

const app = new Hono();

app.get('/meta', async (c) =>
  c.json({
    appEnv: getAppEnv(),
    version: process.env.VERSION,
    hostname: process.env.APP_HOSTNAME,
  }),
);

app.get('/me', async (c) => {
  const currentUser = await AuthSO.currentUser();
  return c.json(currentUser);
});

export default app;
