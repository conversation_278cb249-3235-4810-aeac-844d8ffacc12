import {
  convertMCPServerConfiguration,
  getToolAppFromMCPServer,
  mockMCPServerEnv,
} from '@toolsdk.ai/orm/prisma/dao/mcp-helper';
import { InputFieldBO, IntegrationTemplate, McpServerPackageDataBO, ToolApp } from '@toolsdk.ai/sdk-ts/types/bo';
import { PackageDataAdapter } from './abstract-package-data-adapter';
import { IPackageToolRunFullBody } from '../types';
import assert from 'assert';

export class MCPServerAdapter extends PackageDataAdapter<McpServerPackageDataBO> {
  override async getToolApp(): Promise<ToolApp> {
    if (!this.packageData.validated) {
      return {
        key: this.packageData.key,
        display: {
          label: this.packageData.name,
        },
        authentication: convertMCPServerConfiguration(this.packageData.env),
        tools: {},
      };
    }

    const mockEnv = mockMCPServerEnv(this.packageData);
    const { closeConnection, toolApp } = await getToolAppFromMCPServer('plugin', this.packageData, mockEnv);
    await closeConnection();

    return toolApp;
  }

  override async getUrl(): Promise<string | undefined> {
    assert(this.packageData.type, 'MCP_SERVER');
    return this.packageData.url;
  }

  override getReadme(): string {
    return this.packageData.readme || '';
  }

  override async findConfiguration(): Promise<IntegrationTemplate | undefined> {
    const packageData = this.packageData;
    const env = packageData.env;
    if (!env || Object.keys(env).length === 0) {
      return;
    }

    return convertMCPServerConfiguration(env);
  }

  /**
   * 整体逻辑与父类一致，区别是统一获取一次 client 连接，避免获取 listTools、callTool 时多次连接
   */
  override async runTool(body: IPackageToolRunFullBody, authData?: Record<string, unknown>): Promise<unknown> {
    const toolKey = body.toolKey;

    if (!this.packageData.validated) {
      throw new Error(`MCP Server package ${this._packageData.key} is not validated.`);
    }

    const configuration = await this.findConfiguration();
    if (configuration && !authData) {
      throw new Error('Configuration is required to run tool.');
    }

    const env = authData ? (authData as Record<string, string>) : undefined;
    const { client, closeConnection, tools } = await getToolAppFromMCPServer('plugin', this._packageData, env);

    const tool = tools?.[toolKey];
    if (!tool) {
      throw new Error(`Tool ${toolKey} not found`);
    }
    const inputFields = tool.operation.inputFields as InputFieldBO[];
    const inputData = super.buildInputData(inputFields, body);

    const output = await client.callTool({
      name: toolKey,
      arguments: inputData,
    });

    await closeConnection();

    return output;
  }
}
