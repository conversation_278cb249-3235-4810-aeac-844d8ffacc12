import { generateNanoID } from 'basenext/utils/nano-id';
import { <PERSON><PERSON><PERSON><PERSON>, db } from '@toolsdk.ai/orm';
import { DeveloperSO } from './developer-so';

export class ApiKeySO {
  private _model: Api<PERSON>ey;

  private _developer?: DeveloperSO;

  constructor(model: <PERSON><PERSON><PERSON><PERSON>, developer?: DeveloperSO) {
    this._model = model;
    this._developer = developer;
  }

  get secretKey() {
    return this._model.secretKey;
  }

  async getDeveloper(): Promise<DeveloperSO> {
    if (this._developer) {
      return this._developer;
    }
    const developer = await DeveloperSO.findByDeveloperId(this._model.developerId);
    this._developer = developer;
    return developer;
  }

  static async findByKey(key: string): Promise<ApiKeySO | undefined> {
    const po = await db.prisma.apiKey.findUnique({
      where: {
        secretKey: key,
      },
      include: {
        developer: true,
      },
    });
    if (!po) {
      return undefined;
    }
    return new ApiKeySO(po, new DeveloperSO(po.developer));
  }

  static async findByDeveloperId(developerId: string): Promise<ApiKeySO[]> {
    const pos = await db.prisma.apiKey.findMany({
      where: {
        developerId,
      },
    });
    return pos.map((po) => new ApiKeySO(po));
  }

  static async create(developerId: string) {
    const po = await db.prisma.apiKey.create({
      data: {
        developerId,
        secretKey: generateNanoID('sk', 32),
      },
    });
    return new ApiKeySO(po);
  }
}
