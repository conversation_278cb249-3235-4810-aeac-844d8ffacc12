import { type Prisma, Account, db } from '@toolsdk.ai/orm';
import { generateNanoID } from 'basenext/utils/nano-id';
import type { AccountVO } from '@toolsdk.ai/sdk-ts/types/vo';
import { AccountTokenSO } from './account-token-so';
import { ConfigurationInstanceSO } from './configuration-instance-so';
import { DeveloperSO } from './developer-so';
import { PackageSO } from './package-so';

type AccountModel =
  | Account
  | Prisma.AccountGetPayload<{
      include: { tokens: true };
    }>;

export class AccountSO {
  private _model: AccountModel;

  private _tokens?: AccountTokenSO[];

  constructor(model: AccountModel, tokens?: AccountTokenSO[]) {
    this._model = model;
    this._tokens = tokens;
  }

  get id() {
    return this._model.id;
  }

  get model() {
    return this._model;
  }

  get key() {
    return this._model.key;
  }

  get developerId() {
    return this._model.developerId;
  }

  toVO(): AccountVO {
    return {
      id: this.id,
    };
  }

  async getDeveloper() {
    return DeveloperSO.findByDeveloperId(this._model.developerId);
  }

  async getTokens(): Promise<AccountTokenSO[]> {
    if (this._tokens) {
      return this._tokens;
    }
    const tokens = await AccountTokenSO.findByAccountId(this.id);
    this._tokens = tokens;
    return tokens;
  }

  async getToken(externalId?: string): Promise<AccountTokenSO> {
    const tokens = await this.getTokens();

    const token = tokens.find((t) => {
      // 指定了 externalId，只返回指定用户创建的 token
      if (externalId) {
        return externalId === t.metadata.externalId;
      }
      // 没有指定 externalId，返回默认 token（也不是其他用户创建的 token）
      return !t.metadata.externalId;
    });
    if (token) {
      return token;
    }
    // 没有匹配的 token，创建一个
    const newToken = await AccountTokenSO.create(this.id, externalId);
    this._tokens = [...tokens, newToken];
    return newToken;
  }

  async findCredentials(packageId: string): Promise<ConfigurationInstanceSO[]> {
    const credentialsPOs = await db.prisma.configurationInstance.findMany({
      where: {
        accountId: this.id,
        packageId,
      },
    });
    return credentialsPOs.map((po) => new ConfigurationInstanceSO(po, { account: this }));
  }

  async createCredential(pkg: PackageSO, inputData: Record<string, unknown>, externalId?: string) {
    return ConfigurationInstanceSO.create({ account: this, pkg, inputData, externalId });
  }

  static async findById(id: string) {
    const accountPO = await db.prisma.account.findUnique({
      where: { id },
    });
    if (!accountPO) {
      throw new Error('Account not found');
    }
    return new AccountSO(accountPO);
  }

  static async findByKey(developerId: string, key: string) {
    const accountPO = await db.prisma.account.findUnique({
      where: {
        developerId_key: {
          developerId,
          key,
        },
      },
    });
    if (!accountPO) {
      throw new Error('Account not found');
    }
    return new AccountSO(accountPO);
  }

  static async findManyByDeveloperId(developerId: string) {
    const accountPOs = await db.prisma.account.findMany({
      where: {
        developerId,
      },
    });
    return accountPOs.map((po) => new AccountSO(po));
  }

  static async getOrCreate(developerId: string, accountKey?: string, externalId?: string) {
    const accountPO = await db.prisma.account.findUnique({
      where: {
        developerId_key: {
          developerId,
          key: accountKey || '',
        },
      },
      include: {
        tokens: true,
      },
    });
    if (accountPO) {
      const tokens = accountPO.tokens.map((token) => new AccountTokenSO(token));
      return new AccountSO(accountPO, tokens);
    }
    const newAccountPO = await db.prisma.account.create({
      data: {
        id: generateNanoID('acc'),
        key: accountKey || '',
        developerId,
        tokens: {
          create: {
            token: generateNanoID('atk'),
            metadata: externalId ? { externalId } : {},
          },
        },
      },
      include: {
        tokens: true,
      },
    });
    const tokens = newAccountPO.tokens.map((token) => new AccountTokenSO(token));
    return new AccountSO(newAccountPO, tokens);
  }
}
