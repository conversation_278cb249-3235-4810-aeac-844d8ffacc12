import _ from 'lodash';
import { generateNanoID } from 'basenext/utils/nano-id';
import { db, PackageInstance as PackageInstancePO, Prisma } from '@toolsdk.ai/orm';
import {
  PackageInstanceCreateBody,
  PackageInstanceRunBody,
  PackageInstanceUpdateBody,
} from '@toolsdk.ai/sdk-ts/types/dto';
import { PackageInstanceVO } from '@toolsdk.ai/sdk-ts/types/vo';
import { AccountSO } from './account-so';
import { ConfigurationInstanceSO } from './configuration-instance-so';
import { PackageSO } from './package-so';
import { IInstanceMetadata } from './types';

export class PackageInstanceSO {
  private _model: PackageInstancePO;

  private _package?: PackageSO;

  private constructor(model: PackageInstancePO, pkg?: PackageSO) {
    this._model = model;
    this._package = pkg;
  }

  get model() {
    return this._model;
  }

  get id() {
    return this._model.id;
  }

  get developerId() {
    return this._model.developerId || undefined;
  }

  get packageId() {
    return this._model.packageId || undefined;
  }

  get configurationInstanceId() {
    return this._model.configurationInstanceId || undefined;
  }

  get inputData() {
    return this._model.inputData as Record<string, unknown>;
  }

  get metadata() {
    return (this._model.metadata || {}) as IInstanceMetadata;
  }

  async getPackage(): Promise<PackageSO | undefined> {
    if (this._package) {
      return this._package;
    }
    if (!this._model.packageId) {
      return undefined;
    }
    const pkg = await PackageSO.getById(this._model.packageId);
    this._package = pkg;
    return pkg;
  }

  async toVO(): Promise<PackageInstanceVO> {
    const pkg = await this.getPackage();
    const packageVO = await pkg?.toDetailVO();
    return {
      instanceId: this._model.id,
      accountKey: this._model.accountKey,
      consumerKey: this._model.consumerKey || undefined,
      package: packageVO,
      toolKey: this._model.toolKey || undefined,
      inputData: this._model.inputData as Record<string, unknown>,
      configurationInstanceId: this.configurationInstanceId,
    };
  }

  async run(body: PackageInstanceRunBody, externalId?: string) {
    const toolKey = this._model.toolKey;
    if (!toolKey) {
      throw new Error('Tool not selected');
    }
    const pkg = await this.getPackage();
    if (!pkg) {
      throw new Error('Package not selected');
    }
    return pkg.runTool(
      { ...body, toolKey, inputData: this.inputData, configurationInstanceId: this.configurationInstanceId },
      { instanceId: this.id, externalId },
    );
  }

  async update(body: PackageInstanceUpdateBody, externalId?: string): Promise<PackageInstanceSO> {
    const { packageKey, packageVersion, toolKey, configurationInstanceId, inputData } = body;

    const data: Prisma.PackageInstanceUncheckedUpdateInput = {};
    const pkg = packageKey ? await PackageSO.getByKey(packageKey, packageVersion) : undefined;
    if (pkg && pkg.id !== this._model.packageId) {
      data.packageId = pkg.id;
    }

    const between = (input: string | undefined, original: string | null) => {
      if (input === undefined) {
        return { isModified: false };
      }
      if (input.length > 0) {
        return { isModified: input !== original, value: input };
      }
      // 支持传入空值，若果原值不为空，则修改为 null
      return { isModified: original !== null, value: null };
    };

    const { isModified: isToolKeyModified, value: toolKeyValue } = between(toolKey, this._model.toolKey);
    if (isToolKeyModified) {
      data.toolKey = toolKeyValue;
    }
    const { isModified: isConfigurationInstanceIdModified, value: configurationInstanceIdValue } = between(
      configurationInstanceId,
      this._model.configurationInstanceId,
    );

    if (isConfigurationInstanceIdModified) {
      const effectivePackageId = pkg?.id || this.packageId; // Use the new or existing packageId
      // Call the helper method for check
      await PackageInstanceSO.checkConfigurationInstance(
        this._model.accountId,
        effectivePackageId,
        configurationInstanceId,
      );
      data.configurationInstanceId = configurationInstanceIdValue;
    }
    if (inputData && !_.isEqual(inputData, this._model.inputData)) {
      data.inputData = inputData as Prisma.InputJsonValue;
    }

    // 未传入值不更新
    if (Object.keys(data).length === 0) {
      return this;
    }
    const po = await db.prisma.packageInstance.update({
      where: { id: this.id },
      data: externalId
        ? {
            ...data,
            metadata: { ...this.metadata, updatedBy: externalId },
          }
        : data,
    });
    return new PackageInstanceSO(po, pkg);
  }

  async delete() {
    await db.prisma.packageInstance.delete({
      where: { id: this.id },
    });
  }

  static async init(instanceId: string): Promise<PackageInstanceSO> {
    const po = await db.prisma.packageInstance.findUnique({
      where: {
        id: instanceId,
      },
    });
    if (!po) {
      throw new Error(`PackageInstance not found: ${instanceId}`);
    }
    return new PackageInstanceSO(po);
  }

  static async checkExists(accountId: string, consumerKey: string): Promise<boolean> {
    const count = await db.prisma.packageInstance.count({
      where: {
        accountId,
        consumerKey,
      },
    });
    return count > 0;
  }

  static async findByAccountIdAndConsumerKey(
    accountId: string,
    consumerKey: string,
  ): Promise<PackageInstanceSO | undefined> {
    const po = await db.prisma.packageInstance.findFirst({
      where: {
        accountId,
        consumerKey,
      },
    });
    if (!po) {
      return undefined;
    }
    return new PackageInstanceSO(po);
  }

  static async create(
    account: AccountSO,
    data: PackageInstanceCreateBody & ({ packageKey?: string; packageVersion?: string } | { packageSO: PackageSO }),
    externalId?: string,
  ): Promise<PackageInstanceSO> {
    const { toolKey, consumerKey, configurationInstanceId, inputData } = data;

    const fetchPackage = async () => {
      if ('packageSO' in data) {
        return data.packageSO;
      }
      const { packageKey, packageVersion } = data;
      if (!packageKey) {
        return undefined;
      }
      return PackageSO.getByKey(packageKey, packageVersion);
    };
    const pkg = await fetchPackage();

    // check private package
    if (pkg && pkg.visibility === 'PRIVATE' && pkg.createdBy !== account.developerId) {
      throw new Error('Unauthorized');
    }

    // Call the helper method for check
    await PackageInstanceSO.checkConfigurationInstance(account.id, pkg?.id, configurationInstanceId);

    const po = await db.prisma.packageInstance.create({
      data: {
        id: generateNanoID('pki'),
        developerId: account.developerId,
        accountId: account.id,
        accountKey: account.key,
        packageId: pkg?.id,
        toolKey,
        configurationInstanceId,
        consumerKey,
        inputData: (inputData as Prisma.InputJsonValue) || {},
        createdBy: account.developerId,
        updatedBy: account.developerId,
        metadata: externalId ? { createdBy: externalId } : {},
      },
    });
    return new PackageInstanceSO(po, pkg);
  }

  private static async checkConfigurationInstance(
    accountId: string,
    packageId?: string,
    configurationInstanceId?: string,
  ) {
    // No check needed if no configurationInstanceId is provided
    if (!configurationInstanceId) {
      return;
    }
    // package 未选时，不可能关联配置实例
    if (!packageId) {
      throw new Error('Package not selected but configuration instance is set');
    }
    const cfgIns = await ConfigurationInstanceSO.findById(configurationInstanceId);
    if (cfgIns.accountId !== accountId || cfgIns.packageId !== packageId) {
      throw new Error('Configuration instance exceptions');
    }
  }
}
