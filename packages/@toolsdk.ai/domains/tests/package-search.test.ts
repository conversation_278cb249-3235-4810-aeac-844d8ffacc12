import { expect, test } from 'vitest';
// import { TestContext } from './test-context';
// import { PackageInstanceSO } from '../server/package-instance-so';
import { PackageSO } from '../server/package-so';
// import { generateNanoID } from 'basenext/utils/nano-id';
// import { defaultActionCreateDTO } from '@toolsdk.ai/sdk-ts/types/default';
// import { TestContext } from './test-context';
// import { ActionInstanceSO } from '../server/action-instance-so';
// import { ActionTemplateSO } from '../server/components/action-template-so';
// import { IntegrationTemplateSO } from '../server/components/integration-template-so';
// import { CredentialSO } from '../server/instances/credential-so';

// const ResendPlugin = plugins[0];
// const openAIPlugin = plugins[2];

test('package template test', async () => {
  // const { developer } = await TestContext.initMockDeveloper();
  const pkgs = await PackageSO.search({
    query: 'Github',
  });
  expect(pkgs.data.length).toBeGreaterThan(0);

  const pkg = pkgs.data[0];
  expect(pkg.stars).toBeGreaterThanOrEqual(0);
});
