// import { ComponentDAO } from '@toolsdk.ai/orm/prisma/dao/component-dao';
// import plugins from '@toolsdk.ai/plugin-core';
import { expect, test } from 'vitest';
import { TestContext } from './test-context';
import { PackageInstanceSO } from '../server/package-instance-so';
// import { generateNanoID } from 'basenext/utils/nano-id';
// import { defaultActionCreateDTO } from '@toolsdk.ai/sdk-ts/types/default';
// import { TestContext } from './test-context';
// import { ActionInstanceSO } from '../server/action-instance-so';
// import { ActionTemplateSO } from '../server/components/action-template-so';
// import { IntegrationTemplateSO } from '../server/components/integration-template-so';
// import { CredentialSO } from '../server/instances/credential-so';

// const ResendPlugin = plugins[0];
// const openAIPlugin = plugins[2];

test('package template test', async () => {
  const { developer } = await TestContext.initMockDeveloper();
  const pkg = await TestContext.getTestMCPServer(developer.id);

  await pkg.refreshCache();

  // cache time
  expect(pkg.cacheTime).toBeDefined();
  expect(pkg.isCacheExpired()).toBe(false);

  // Tools
  const tools = await pkg.listTools();
  const toolsCount = Object.keys(tools!).length;
  // console.log('Fetch MCP Tools Directly', tools, toolsCount);
  expect(toolsCount).toBeGreaterThan(0);

  // Configuration
  const conf = await pkg.getConfiguration();
  expect(conf).toBeDefined();
  expect(conf?.inputFields?.length).toBeGreaterThan(0);

  // const pkgIns = await PackageInstanceSO.create(developer.id, {
  //   packageKey: pkg.key,
  //   packageVersion: pkg.version,
  // });
});
test('package instance test', async () => {
  const { developer } = await TestContext.initMockDeveloper();
  const account = await developer.getMainAccount();

  const pkg = await TestContext.getTestMCPServer(developer.id);
  const pkgIns = await PackageInstanceSO.create(account, {
    packageSO: pkg,
  });
  expect(pkgIns).toBeDefined();

  const pkgInsVO = await pkgIns.toVO();
  expect(pkgInsVO.consumerKey).toBe(undefined);

  const pkgIns2 = await PackageInstanceSO.create(account, {
    packageSO: pkg,
    consumerKey: 'testConsumerKey',
  });

  const pkgInsVO2 = await pkgIns2.toVO();
  expect(pkgInsVO2.consumerKey).toBe('testConsumerKey');

  const pkgIns3 = await PackageInstanceSO.init(pkgIns2.id);
  expect(pkgIns3.id).toBe(pkgIns2.id);

  const pkgTools = await (await pkgIns3.getPackage())?.listTools();
  expect(Object.keys(pkgTools!).length).toBeGreaterThan(0);

  // set states
});
