import {
  // ComponentsCreateDTO,
  PackageCreateDTO,
  PackageUpdateDTO,
} from '@toolsdk.ai/sdk-ts/types/dto';
import { db } from './db';
import { generateNanoID } from 'basenext/utils/nano-id';
import { InputJsonObject } from '../prisma-client/runtime/library';
import { Package, Prisma } from '@toolsdk.ai/orm';
// import { ComponentDAO } from './component-dao';
import { ToolApp } from '@toolsdk.ai/sdk-ts/types/bo';

export type PackageDetailPO = Prisma.PackageGetPayload<{
  include: {
    _count: {
      select: {
        favorites: true;
      };
    };
  };
}>;
export type PackagePO = Package;

export class PackageDAO {
  /**
   *
   * @param packageId
   * @param cacheTime if undefined then set as dirty
   * @returns
   */
  static async cache({
    packageId,
    toolapp,
    cacheTime,
    cacheStar,
  }: {
    packageId: string;
    toolapp: ToolApp;
    cacheTime?: Date;
    cacheStar?: number;
  }) {
    const updateCacheTime = cacheTime || new Date();
    const toolsCount = toolapp.tools ? Object.keys(toolapp.tools).length : 0;

    return db.prisma.package.update({
      where: {
        id: packageId,
      },
      data: {
        cacheTime: updateCacheTime,
        // 先 string 再 parse，去掉function 们
        cacheAppData: JSON.parse(JSON.stringify(toolapp)) as object,
        cacheCount: toolsCount,
        ...(cacheStar !== undefined && { cacheStar }),
      },
    });
  }

  static async create(developerId: string, dto: PackageCreateDTO) {
    return db.prisma.package.create({
      data: {
        id: generateNanoID('pkg'),
        packageType: dto.bo.type,
        key: dto.bo.key,
        packageData: dto.bo as InputJsonObject,
        name: dto.bo.name,
        description: dto.bo.description,
        visibility: dto.visibility,
        createdBy: developerId,
        updatedBy: developerId,
      },
    });
  }

  /**
   *  统计所有 tools，一共多少个 tools
   */
  static async calcTotalToolsCount() {
    const r = await db.prisma.package.aggregate({
      _sum: {
        cacheCount: true,
      },
    });
    return r._sum.cacheCount || 0;
  }

  static async upsert(developerId: string, dto: PackageCreateDTO & PackageUpdateDTO, validated: boolean = true) {
    const logo = dto.bo.logo || undefined;
    return db.prisma.package.upsert({
      where: {
        key_version: {
          key: dto.bo.key,
          version: dto.bo.version,
        },
      },
      update: {
        packageType: dto.bo.type,
        name: dto.bo.name,
        categoryId: dto.category,
        description: dto.bo.description,
        logo,
        validated,
        packageData: dto.bo as InputJsonObject,
        visibility: dto.visibility,
        updatedBy: developerId,
      },
      create: {
        id: generateNanoID('srv'),
        packageType: dto.bo.type,
        categoryId: dto.category,
        key: dto.bo.key,
        name: dto.bo.name,
        description: dto.bo.description,
        logo,
        validated,
        packageData: dto.bo as InputJsonObject,
        visibility: dto.visibility,
        createdBy: developerId,
        updatedBy: developerId,
      },
    });
  }
}
