import { generateNanoID } from 'basenext/utils/nano-id';
import { db } from './db';

export class DeveloperDAO {
  static async upsertSuper() {
    // super user
    const superAdmin = await db.prisma.developer.upsert({
      where: { id: 'admin' },
      update: {
        externalId: 'admin',
        info: {},
      },
      create: {
        id: 'admin',
        externalId: 'admin',
        info: {},
        apiKeys: {
          create: {
            secretKey: generateNanoID('sk', 32),
            // pubKey: generateNanoID('pk', 64),
          },
        },
      },
      include: { apiKeys: true },
    });
    return superAdmin;
  }
}
