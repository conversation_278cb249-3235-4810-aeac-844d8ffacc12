import fs from 'fs';
import path from 'path';
import { $Enums } from '../prisma/prisma-client';
import matter from 'gray-matter';
import { i18n, Locale } from '@bika/contents/i18n/config';
import { getAppEnv } from 'sharelib/app-env';
import { HELP_PATH, PAGE_PATH } from './path';
import { MarkdownRenderer, PageProps } from './types';
import { mapDirectoriesToFiles } from 'sharelib/fs-utils';
import { db } from '../db';
import assert from 'assert';

export class LocalContentHelpLoader {
  public async fetchLocalMDX(lang: string, slug: string[]): Promise<MarkdownRenderer> {
    const content = await fetch(`http://localhost:4006/docs/help/${lang}/${slug.join('/')}`);
    const data = await content.json();
    return data as MarkdownRenderer;
  }

  public async autoHelp(lang: string, slugs: string[]): Promise<MarkdownRenderer> {
    if (getAppEnv() === 'LOCAL' && fs.existsSync(PAGE_PATH)) {
      return this.fetchLocalMDX(lang, slugs);
    }
    return this.dbHelp(lang, slugs);
  }

  public async autoHelpsList(lang?: string): Promise<PageProps[]> {
    let result;
    if (getAppEnv() === 'LOCAL' && fs.existsSync(HELP_PATH)) {
      result = this.fsHelpsList(lang);
    } else {
      result = this.dbHelpsList(lang);
    }
    return result;
  }

  public async dbHelpsList(lang?: string): Promise<PageProps[]> {
    const blogContents = await db.prisma.content.findMany({
      where: {
        type: $Enums.ContentType.HELP,
        source: $Enums.ContentSource.LOCAL,
      },
    });
    const ret: PageProps[] = [];
    for (const blog of blogContents) {
      const spilts = blog.slug.split('/');
      const sLang = spilts[0];
      // const sHelp = spilts[1];
      const slugs = spilts.slice(2);

      const data = blog?.data as unknown as MarkdownRenderer;
      const metadata = (data.meta as Record<string, string>) || {};

      if (!lang || lang === sLang) {
        ret.push({
          lang: sLang,
          slugs,
          metadata: metadata,
          date: metadata.date ? new Date(metadata.date) : blog.updatedAt,
        });
      }
    }
    return ret;
  }

  public async dbHelp(lang: string, slugs: string[]): Promise<MarkdownRenderer> {
    const slug = `${lang}/help/${slugs.join('/')}`;
    const helpPO = await db.prisma.content.findUnique({
      where: {
        type_slug: {
          slug,
          type: $Enums.ContentType.HELP,
        },
      },
    });
    const md = helpPO?.data as unknown as MarkdownRenderer;
    md.date = helpPO?.updatedAt;
    return md;
  }

  public async fsHelpsList(lang?: string): Promise<PageProps[]> {
    const helps = await mapDirectoriesToFiles(HELP_PATH);
    const ret: PageProps[] = [];
    for (const pg of helps) {
      const urlSegs = pg.split('/');
      const lang = urlSegs[0];
      const slugs = urlSegs.slice(1);
      if (i18n.locales.includes(lang as Locale) && !slugs.some((slug) => slug.startsWith('_'))) {
        let filePath = path.join(HELP_PATH, `${pg}.mdx`);
        if (!fs.existsSync(filePath)) {
          filePath = path.join(HELP_PATH, `${pg}.md`);
        }
        assert(fs.existsSync(filePath), `File not found: ${filePath}`);

        console.warn(`Reading local file...${filePath}`);
        const content = fs.readFileSync(filePath, 'utf8');
        const rPage: PageProps = {
          lang,
          slugs,
          metadata: matter(content).data as Record<string, string>,
        };
        ret.push(rPage);
      }
    }
    if (lang) return ret.filter((help) => help.lang === lang);
    return ret;
  }
}
