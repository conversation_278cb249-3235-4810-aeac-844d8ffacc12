import assert from 'assert';
import fs from 'fs';
import path from 'path';
import { $Enums } from '../prisma/prisma-client';
import _ from 'lodash';
import { TemplateSmartHomeConfig } from '@bika/contents/config/server/template/template-smart-home';
import { TemplatesVerified } from '@bika/contents/config/server/template/templates-init-data';
import { i18n } from '@bika/contents/i18n';
import type { Database } from '@bika/types/database/bo';
import { iString, type Locale } from '@bika/types/i18n/bo';
import { AvatarLogo } from '@bika/types/system';
import { getAppEnv } from 'sharelib/app-env';
import {
  CustomTemplate,
  CustomTemplateSchema,
  TemplateRepoScoreWarning,
  TemplateRepo,
  TemplateRepoRelease,
  TemplateCategory,
  TemplateDataSchema,
  type TemplateCategoryEnum,
} from '@bika/types/template/bo';
import type { FeatureType } from '@bika/types/website/bo';
import { getLocalTemplatePath } from './path';
import { templateFeatureMatch } from './template-feature-matcher';
import { mapDirectoriesPaths } from './utils';
import { Bikafile } from '../bikafile/bikafile';
import { tsCode } from '../bikafile/ts-code-gen';
import { db } from '../db';

// const jsonSchemaUrl = 'https://dev.bika.ai/api/schema/custom-template.json';

const { concat } = _;
/**
 * 对本地工程的templates进行加载处理。
 * 注意，如果那些「用户发布」的模板，不要在这里处理
 */
export class LocalContentTemplateLoader {
  // 本地文件保存
  public async fsSaveLocalTemplateBO(template: CustomTemplate) {
    const templateId = template.templateId;
    const templateFullDir = this.getLocalTemplateFullDir(templateId);
    if (templateFullDir === null) {
      throw new Error(`模板: ${templateId}，文件夹路径错误`);
    }
    if (!fs.existsSync(templateFullDir)) {
      // 有字符串
      console.warn(`文件夹不存在？新建模板：${templateId}，创建目录：${templateFullDir}`);
      fs.mkdirSync(templateFullDir, { recursive: true });
    }

    const templatePath = path.join(templateFullDir, 'template.ts');
    const templateJsonPath = path.join(templateFullDir, 'template.json');
    // 保存当前版本
    fs.writeFileSync(templatePath, await tsCode(template));
    fs.writeFileSync(templateJsonPath, JSON.stringify(template, null, 2));
    return true;
  }

  public async autoLoadTemplateRepo(templateId: string): Promise<TemplateRepo> {
    if (getAppEnv() === 'LOCAL' && this.existLocalTemplate(templateId)) {
      try {
        const tpl = await this.importLocalTemplateRepo(templateId);
        return tpl;
      } catch (_) {
        return this.dbLoadTemplateRepo(templateId);
      }
    }
    return this.dbLoadTemplateRepo(templateId);
  }

  /**
   * 检查Custom Template填写情况，返回警告文字⚠️
   * @param template
   */
  public checkTemplateRepoScore(template: TemplateRepo) {
    const warningStatus: TemplateRepoScoreWarning[] = [];
    if (!template.author) {
      warningStatus.push('NO_AUTHOR');
    }
    if (!template.keywords) {
      warningStatus.push('NO_KEYWORDS');
    }
    if (!template.personas) {
      warningStatus.push('NO_PERSONAS');
    } else if (typeof template.personas === 'string') {
      warningStatus.push('PERSONAS_NO_I18N');
    } else {
      _.each(template.personas, (personaStr) => {
        const useCases = personaStr!.split(',').map((uc: string) => uc.trim());
        if (useCases.length < 4) {
          warningStatus.push('PERSONAS_LT_4');
        }
      });
    }

    if (!template.readme) {
      warningStatus.push('NO_README');
    }

    if (!template.useCases) {
      warningStatus.push('NO_USE_CASES');
    } else if (typeof template.useCases === 'string') {
      warningStatus.push('USE_CASES_NO_I18N');
    } else {
      _.each(template.useCases, (useCasesStr) => {
        const useCases = useCasesStr!.split(',').map((uc: string) => uc.trim());
        if (useCases.length < 24) {
          warningStatus.push('USE_CASES_LT_24');
        }
      });
    }

    if (!template.cover) {
      warningStatus.push('NO_COVER');
    } else if (
      typeof template.cover === 'string' &&
      (!template.cover.includes('/assets/template') ||
        template.cover.includes('common-cover') ||
        template.cover.includes('cover-no-image'))
    ) {
      warningStatus.push('NO_COVER');
    }
    if (!template.current.data.initMissions || template.current.data.initMissions.length === 0) {
      warningStatus.push('NO_INIT_MISSIONS');
    }
    // 检查资源Resources
    if (!template.current.data.resources || template.current.data.resources.length === 0) {
      warningStatus.push('NO_RESOURCES');
    }
    for (const resource of template.current.data.resources) {
      // 检查database
      if (resource.resourceType === 'DATABASE') {
        const dbResource = resource as Database;
        // 确保有初始化示例数据，给用户看
        if (!dbResource.records || dbResource.records.length < 3) {
          warningStatus.push('DATABASE_NO_INIT_RECORDS');
        }
      }
    }

    // 检查Changelog，
    // 检查，是否包含TODO、XXXXX或YYYYY，都视为没有写changelog
    for (const release of template.releases) {
      const releaseNotes = release.releaseNotes;
      if (
        !releaseNotes ||
        releaseNotes.includes('TODO') ||
        releaseNotes.includes('XXXXX') ||
        releaseNotes.includes('YYYYY')
      ) {
        warningStatus.push('ERROR_RELEASE_NOTES');
        break;
      }
    }

    if (template.visibility === 'PUBLIC') {
      // 官方的public模板，检查是否已经verified！测试验证
      const verifiedTemplate = TemplatesVerified[template.templateId] || false;
      if (!verifiedTemplate) {
        warningStatus.push('NOT_VERIFIED');
      }
    }
    return {
      score: 100 - warningStatus.length * 10,
      warnings: warningStatus,
    };
  }

  /**
   * 将custom template的warning status，转成文字
   */
  public getTemplateRepoWarningText(warningStatus: TemplateRepoScoreWarning[]) {
    const warningText: string[] = [];
    for (const warning of warningStatus) {
      switch (warning) {
        case 'NO_AUTHOR':
          warningText.push('作者信息未填写');
          break;
        case 'NOT_VERIFIED':
          warningText.push('未通过测试功能认证');
          break;
        case 'NO_KEYWORDS':
          warningText.push('keywords未填写');
          break;
        case 'NO_COVER':
          warningText.push('封面错误或未填写');
          break;
        case 'NO_README':
          warningText.push('没有写README');
          break;
        case 'NO_INIT_MISSIONS':
          warningText.push('没有初始化任务让用户更好入门');
          break;
        case 'PERSONAS_LT_4':
          warningText.push('Personas不得少于4个');
          break;
        case 'DATABASE_NO_INIT_RECORDS':
          warningText.push('数据表没有初始化数据，database.records字段添加大于3行示例数据避免用户懵逼');
          break;
        case 'USE_CASES_LT_24':
          warningText.push('Use Cases不得少于24个，要注意，每6个匹配1个Persona，共4个Persona');
          break;
        case 'ERROR_RELEASE_NOTES':
          warningText.push('没有仔细写release notes，请详细描述每个版本的变化，可合并版本呢');
          break;
        default:
          warningText.push(warning);
          break;
      }
    }
    return warningText.join(',');
  }

  /**
   * 导入本地文件，如果fs.exsit，就读json，如果没有，就读ts(import)
   *
   * @param templateId
   * @returns
   */
  public async importLocalCustomTemplate(templateId: string): Promise<CustomTemplate> {
    const templateLocalFullDir = this.getLocalTemplateFullDir(templateId);

    const bikafilePath = `${templateLocalFullDir}.bika`;

    if (fs.existsSync(bikafilePath)) {
      // .bika读取
      const bikafile = new Bikafile(bikafilePath);
      assert(bikafile.data.format === 'TEMPLATE', 'bikafile format must be TEMPLATE');
      return bikafile.data.template;
    }

    const templateJSONFile = path.join(templateLocalFullDir!, 'template.json');
    const templateTSFile = path.join(templateLocalFullDir!, 'template.ts');

    console.warn(`Reading local file...${templateJSONFile}`);
    // 如果有ts文件，那么读ts；vite环境下不读ts文件，vite不支持"动态"import跨包ts
    console.log(templateTSFile);
    if (getAppEnv() === 'LOCAL' && fs.existsSync(templateTSFile)) {
      try {
        const page = await fetch(`http://localhost:4006/template/${templateId}`).then((res) => res.json());
        return CustomTemplateSchema.parse(page.default);
      } catch (_) {
        if (!fs.existsSync(templateJSONFile)) {
          throw new Error(`can not find template: ${templateId}`);
        }
      }
    }

    const tplJSONStr = fs.readFileSync(templateJSONFile, 'utf8');
    const tplObj = JSON.parse(tplJSONStr);
    // unset(tplObj, '$schema');
    return CustomTemplateSchema.parse(tplObj);
  }

  private async fsLocalReleases(templateId: string): Promise<TemplateRepoRelease[]> {
    const templateLocalFullDir = this.getLocalTemplateFullDir(templateId);

    const releases: TemplateRepoRelease[] = [];

    // .bika文件
    const bikafilePath = `${templateLocalFullDir}/${templateId}.bika`;
    if (fs.existsSync(bikafilePath)) {
      // .bika读取
      const bikafile = new Bikafile(bikafilePath);
      assert(bikafile.data.format === 'TEMPLATE', 'bikafile format must be TEMPLATE');
      return bikafile.data.releases;
    }

    // 目录
    const templateLocalReleasesFullDir = path.join(templateLocalFullDir!, 'release');
    // 开始遍历寻找所有release里的template.json, change-log.md, release-notes.md
    if (fs.existsSync(templateLocalReleasesFullDir)) {
      // 读取目录中的所有文件和文件夹
      const entries = fs.readdirSync(templateLocalReleasesFullDir, { withFileTypes: true });
      // 过滤出文件夹
      const directories = entries.filter((entry) => entry.isDirectory());
      // 提取文件夹的名称
      const directoryNames = directories.map((directory) => directory.name);
      for (const releaseDirName of directoryNames) {
        const fullReleaseDirPath = path.join(templateLocalReleasesFullDir, releaseDirName);
        const fChangelogPath = path.join(fullReleaseDirPath, 'change-log.md');
        const fReleaseNotesPath = path.join(fullReleaseDirPath, 'release-notes.md');
        const fSnapshotPath = path.join(fullReleaseDirPath, 'template.json');
        const newRelease: TemplateRepoRelease = {
          version: releaseDirName,
          changelog: fs.readFileSync(fChangelogPath, 'utf8'),
          releaseNotes: fs.readFileSync(fReleaseNotesPath, 'utf8'),
          data: CustomTemplateSchema.parse(JSON.parse(fs.readFileSync(fSnapshotPath, 'utf8'))),
        };
        releases.push(newRelease);
      }
    }

    return releases;
  }

  public fsTemplateReadme(templateId: string): iString | null | undefined {
    const templateLocalFullDir = this.getLocalTemplateFullDir(templateId);

    // 读.bikafile
    const bikafilePath = `${templateLocalFullDir}/${templateId}.bika`;
    if (fs.existsSync(bikafilePath)) {
      // .bika读取
      const bikafile = new Bikafile(bikafilePath);
      assert(bikafile.data.format === 'TEMPLATE', 'bikafile format must be TEMPLATE');
      return bikafile.data.readme;
    }

    // 读取目录，多语言支持
    let readme: iString | null | undefined = null;
    for (const locale of i18n.locales) {
      const fullREADMEPath = path.join(templateLocalFullDir!, `README.${locale}.md`);
      if (fs.existsSync(fullREADMEPath)) {
        if (!readme) {
          readme = {};
        }
        readme[locale] = fs.readFileSync(fullREADMEPath, 'utf8');
      }
    }
    return readme;
  }

  public async parseCustomTemplate(
    customTemplate: CustomTemplate,
    ignoreReleases: boolean = false,
  ): Promise<TemplateRepo> {
    // Template Data是custom template，去掉metadata部分
    const templateData = TemplateDataSchema.parse(customTemplate);

    const readme = this.fsTemplateReadme(customTemplate.templateId);

    const releases = ignoreReleases ? [] : await this.fsLocalReleases(customTemplate.templateId);

    const repo: TemplateRepo = {
      // develop 的ts文件
      readme,
      current: {
        data: templateData,
        version: customTemplate.version,
        changelog: null,
        releaseNotes: null,
      },
      releases,
      visibility: customTemplate.visibility,
      templateId: customTemplate.templateId,
      name: customTemplate.name,
      description: customTemplate.description,
      cover: customTemplate.cover,
      category: customTemplate.category,
      author: customTemplate.author,
      keywords: customTemplate.keywords,
      personas: customTemplate.personas,
      useCases: customTemplate.useCases,
      installOnce: customTemplate.installOnce,
      detach: customTemplate.detach,
      homepage: customTemplate.homepage,
      copyright: customTemplate.copyright,
    };

    return repo;
  }

  /**
   * 读取本地的CustomTemplate，并转换成TemplateRepo
   *
   * @param templateId
   * @returns
   */
  public async importLocalTemplateRepo(templateId: string): Promise<TemplateRepo> {
    const customTemplate = await this.importLocalCustomTemplate(templateId);
    return this.parseCustomTemplate(customTemplate);
  }

  /**
   * 数据库读取TemplateRepo,  PO 组合成-> Template Repo
   *
   * @param templateId
   * @returns
   */
  public async dbLoadTemplateRepo(templateId: string): Promise<TemplateRepo> {
    const tplPO = await db.prisma.storeTemplate.findUnique({
      where: {
        templateId,
      },
    });

    if (!tplPO) {
      throw new Error(`can not find template: ${templateId}`);
    }

    const tplReleasesPOs = await db.prisma.storeTemplateRelease.findMany({
      where: {
        templateId,
      },
    });
    const currentReleasePO = tplReleasesPOs.find((tplRelPO) => tplRelPO.version === tplPO.currentVersion);
    if (!currentReleasePO) {
      throw new Error(`can not find current version template: ${templateId}, ${tplPO.currentVersion}`);
    }

    const releases: TemplateRepoRelease[] = tplReleasesPOs.map((tplRelPO) => ({
      data: tplRelPO.data as unknown as CustomTemplate,
      version: tplRelPO.version,
      changelog: tplRelPO.changelog,
      releaseNotes: tplRelPO.releaseNotes,
    }));

    const current = {
      data: currentReleasePO.data as unknown as CustomTemplate,
      version: currentReleasePO.version,
      changelog: currentReleasePO.changelog,
      releaseNotes: currentReleasePO.releaseNotes,
    };

    const templateRepo: TemplateRepo = {
      templateId: tplPO.templateId,
      name: tplPO.name as iString,
      description: tplPO.description as iString | undefined,
      cover: tplPO.cover as AvatarLogo,
      category: tplPO.category as TemplateCategory,
      readme: tplPO.readme as iString,
      visibility: tplPO.visibility,
      installOnce: tplPO.installOnce,
      detach: tplPO.detachable,
      keywords: tplPO.keywords as iString | undefined,
      personas: tplPO.personas as iString | undefined,
      useCases: tplPO.useCases as iString | undefined,
      current,
      releases,
    };
    return templateRepo;
  }

  /**
   * 遍历找模板目录的名字
   *
   * @param localTemplatesDirPath
   * @returns
   */
  // public async getLocalTemplates(localTemplatesDirPath: string) {
  //   const directories: string[] = [];

  //   // 读取目录内容
  //   const files = fs.readdirSync(localTemplatesDirPath);

  //   for (const file of files) {
  //     // 拼接完整路径
  //     const filePath = `${localTemplatesDirPath}/${file}`;

  //     if (file.startsWith('@')) {
  //       const subDirectories = await this.getLocalTemplates(filePath);
  //       directories.push(...subDirectories.map((subDirectory: string) => `${file}/${subDirectory}`));
  //     } else {
  //       // 检查路径状态
  //       const stats = fs.statSync(filePath);
  //       if (stats.isDirectory()) {
  //         directories.push(file);
  //       }
  //     }
  //   }

  //   return directories;
  // }

  /**
   * 读template.json，这个文件是模板文件夹下的template.json
   * 由于template.ts必须要通过import才能加载，某些场合不方便读取，如blog文章，所以这里提供一个读取template.json的方法
   *
   * 这个仅仅用于一些特殊场合，如blog文章，需要读取模板的信息，但不方便import
   * sync的，尽可能不使用
   *
   * @param templateId
   * @returns
   *
   */
  public fsLocalTemplateCurrentJSON(templateId: string): CustomTemplate | null {
    const templateDir = getLocalTemplatePath();

    // .bika文件
    const bikafilePath = `${templateDir}/${templateId}.bika`;
    if (fs.existsSync(bikafilePath)) {
      // .bika读取
      const bikafile = new Bikafile(bikafilePath);
      assert(bikafile.data.format === 'TEMPLATE', 'bikafile format must be TEMPLATE');
      return bikafile.data.template;
    }

    // .json 文件模式
    const tplJSONFile = path.join(templateDir!, templateId, 'template.json');
    if (fs.existsSync(tplJSONFile)) {
      return CustomTemplateSchema.parse(JSON.parse(fs.readFileSync(tplJSONFile, 'utf8')));
    }

    console.warn('Template template.json file not found: ', tplJSONFile);
    return null;
  }

  /**
   * 根据功能，随机获取模板，限制上限
   *
   * @param feature
   * @param plimit
   * @returns
   */
  public async autoRandomTemplates(
    feature?: {
      type: FeatureType;
      name: string;
    },
    plimit?: number,
  ) {
    const tpls = await this.autoTemplatesList();
    // 筛选，isVerified
    const verifiedTpls = tpls.filter((tpl) => {
      const verifiedTemplate = TemplatesVerified[tpl.templateId] || false;
      return verifiedTemplate;
    });

    const shuffleTpls = _.shuffle(verifiedTpls);

    const limit = plimit || 10;
    let count = 0;
    const ret = [];
    for (const tpl of shuffleTpls) {
      const templateRepo = await this.autoLoadTemplateRepo(tpl.templateId);
      if (feature === undefined || templateFeatureMatch(feature.type, feature.name, templateRepo)) {
        ret.push(templateRepo);
        count++;
      }

      if (count >= limit) break;
    }
    return ret;
  }

  /**
   * 获取”推荐模板“
   *
   * @param locale
   */
  public getRecommendTemplateIds(locale?: Locale): string[] {
    // 找到推荐的配置，第一个就是
    const recommendSection = TemplateSmartHomeConfig[0];
    // 不同语言，增量匹配不同的模板(同storeTemplateSO.ts)
    const localeTemplateIds = recommendSection.localeTemplateIdsOverride
      ? recommendSection.localeTemplateIdsOverride[locale || 'en']
      : [];
    const mergeTemplateIds = concat(recommendSection.templateIds, localeTemplateIds) as string[];
    return mergeTemplateIds;
  }

  /**
   * 根据分类，本地随机抓取模板
   *
   * 这个给docs/component用的，非异步，类似的异步函数有StoreTemplateSO.autoRandomTemplates
   *
   * @param category 如果不传，只取推荐分类
   */
  public fsLocalRandomTemplateTemplates(
    category?: TemplateCategoryEnum,
    plimit?: number,
    locale?: Locale,
  ): CustomTemplate[] {
    const templates: CustomTemplate[] = [];

    let allLocalTemplatesIds: string[];
    if (!category) {
      allLocalTemplatesIds = this.getRecommendTemplateIds(locale);
    } else {
      allLocalTemplatesIds = this.fsTemplatesList().map((tpl) => tpl.templateId);
    }

    // console.log('寻找', category, '分类的模板');
    // 随机打乱，上限为10
    const limit = plimit || 10;
    let count = 0;
    for (const templateId of _.shuffle(allLocalTemplatesIds)) {
      const tplJSON = this.fsLocalTemplateCurrentJSON(templateId);
      if (tplJSON) {
        if (!tplJSON.visibility || tplJSON.visibility === 'PUBLIC' || tplJSON.visibility === 'WAITING_LIST') {
          if (tplJSON.category && category) {
            if (typeof tplJSON.category === 'string') {
              if (tplJSON.category === category) {
                templates.push(tplJSON);
                count += 1;
              }
            }
            if (Array.isArray(tplJSON.category) && tplJSON.category.includes(category)) {
              templates.push(tplJSON);
              count += 1;
            }
          } else {
            // 取推荐分类
            templates.push(tplJSON);
            count += 1;
          }
        }
      }
      if (count >= limit) break;
    }
    return templates;
  }

  /**
   * Release本地的模板配置们，生成本地的json文件，用于本地进行读取，类似于本地模板编译
   */

  /**
   * 获取本地模板的完整文件夹路径
   * @param templateId
   */
  public getLocalTemplateFullDir(templateId: string) {
    const templatePath = getLocalTemplatePath();
    if (!templatePath) {
      return null;
    }
    return path.resolve(path.join(templatePath, templateId));

    // const { templateDirs, templateFullDirs } = this.collectLocalTemplateDirs();
    // for (let i = 0; i < templateDirs.length; i += 1) {
    //   const tplDir = templateDirs[i];
    //   if (tplDir === templateId) {
    //     return templateFullDirs[i];
    //   }
    // }
    // return null;
  }

  public existLocalTemplateDir() {
    const p = getLocalTemplatePath();
    if (!p) return false;
    return fs.existsSync(p);
  }

  public existLocalTemplate(templateId: string) {
    const dir = this.getLocalTemplateFullDir(templateId);
    if (!dir) {
      return false;
    }
    return fs.existsSync(dir);
  }

  /**
   * .bika模板文件收集
   */
  public collectLocalBikaTemplatesFiles() {
    const bikafiles: { templateId: string }[] = [];
    const dir = getLocalTemplatePath();
    assert(dir, 'templatePath must be exist');
    const files = fs.readdirSync(dir);

    files.forEach((file) => {
      const fullPath = path.join(dir, file);
      const stats = fs.statSync(fullPath);

      if (stats.isDirectory()) {
        // 不递归子目录，只找这个目录
        // findExeFilesSync(fullPath);
      } else if (path.extname(file) === '.bika') {
        const filenameWithoutExt = path.basename(file, path.extname(file));
        bikafiles.push({ templateId: filenameWithoutExt });
      }
    });

    return bikafiles;
  }

  /**
   * 便利本地的模板文件夹，收集模板们
   *
   * @returns
   */
  public collectLocalTemplateDirs() {
    const templateDirs = [];

    const templatePath = getLocalTemplatePath();
    assert(templatePath, 'templatePath must be exist');
    const fullDirPath = path.resolve(templatePath);
    const tplDirs = mapDirectoriesPaths(
      fullDirPath,
      (resPath) =>
        !resPath.startsWith('_') &&
        !resPath.includes('/_') &&
        !resPath.startsWith('node_modules') &&
        resPath !== 'release',
    );

    // 二次筛选，判断有template.ts文件，才算是合格的模板文件夹
    const templateFileFullPaths = [];
    const templateFullDirs = [];
    for (const tplDir of tplDirs) {
      const templateFileFullPath = path.join(fullDirPath, tplDir, 'template.ts');
      const templateFullDir = path.join(fullDirPath, tplDir);

      if (
        fs.existsSync(templateFileFullPath) // 存在模板文件
      ) {
        templateDirs.push(tplDir);
        templateFileFullPaths.push(templateFileFullPath);
        templateFullDirs.push(templateFullDir);
      }
    }
    return { templateDirs, templateFileFullPaths, templateFullDirs };
  }

  /**
   * 遍历本地，收集本地的模板们
   *
   * 这个只包含本地的模板，不包含数据库的
   *
   * @returns
   */
  fsTemplatesList(): { templateId: string }[] {
    const { templateDirs } = this.collectLocalTemplateDirs();

    const bikafileTemplates = this.collectLocalBikaTemplatesFiles();

    const templates: { templateId: string }[] = [];

    // 文件夹模板
    for (const tplId of templateDirs) {
      templates.push({ templateId: tplId });
    }

    // .bika文件
    for (const bikafileTpl of bikafileTemplates) {
      templates.push(bikafileTpl);
    }

    return templates;
  }

  async autoTemplatesList(): Promise<{ templateId: string }[]> {
    if (getAppEnv() === 'LOCAL' && getLocalTemplatePath()) {
      return this.fsTemplatesList();
    }
    return this.dbTemplatesList();
  }

  /**
   * 这个接口只获取官方模板，如果要全部模板，包括第三方开发者的，去StoreTemplateSO
   *
   * @returns
   */
  async dbTemplatesList(): Promise<{ templateId: string }[]> {
    const tplPOs = await db.prisma.storeTemplate.findMany({
      where: {
        source: $Enums.StoreTemplateSource.OFFICIAL,
      },
    });
    const templates: { templateId: string }[] = [];
    for (const tplPO of tplPOs) {
      templates.push({
        templateId: tplPO.templateId,
      });
    }
    return templates;
  }
}
