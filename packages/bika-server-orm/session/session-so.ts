import { Session } from 'lucia';
import { db } from '../db';
import { SessionVO } from '@bika/types/user/vo';
import { lucia } from './lucia';
import { SessionAttributes } from './types';

export class SessionSO {
  protected _luciaSession: Session;

  protected _invalidated: boolean = false;

  public get id() {
    return this._luciaSession.id;
  }

  public get userId() {
    return this._luciaSession.userId;
  }

  /**
   * 指新鲜度，并不是有无效，能拿到session就是有效了，除非执行过sign out
   */
  public get fresh() {
    return this._luciaSession.fresh;
  }

  protected constructor(luciaSession: Session) {
    this._luciaSession = luciaSession;
  }

  public get valid() {
    return !this._invalidated;
  }

  /**
   * Invalidate the session
   * @returns
   */
  async invalidate() {
    await lucia.invalidateSession(this._luciaSession.id);
    this._invalidated = true;
    return this;
  }

  /**
   * 从header里，判断有没有验证，自动取Bearer或Cookie
   * @param headers
   * @returns
   */
  static parseSessionIdFromHeader(headers: Headers) {
    let sessionId;
    const authorizationHeader = headers.get('Authorization');
    if (authorizationHeader) {
      sessionId = SessionSO.readBearerToken(authorizationHeader);
      if (sessionId) {
        return sessionId;
      }
      // 设置了Authorization，就不再检查Cookie
      return null;
    }

    const cookie = headers.get('Cookie');
    if (cookie) {
      const cSessionId = SessionSO.parseSessionFromCookie(cookie);
      if (cSessionId) {
        return cSessionId;
      }
    }

    return null;
  }

  /**
   *
   * Parse Session ID from header cookie string
   *
   * @param cookieString  Long Cookie string
   * @returns
   */
  static parseSessionFromCookie(cookieString: string): string | null {
    const sessionIdFromCookie = lucia.readSessionCookie(cookieString);

    if (!sessionIdFromCookie || sessionIdFromCookie === '') {
      return null;
    }
    return sessionIdFromCookie;
  }

  /**
   *
   * @param bearerString  Bearer XXXX
   * @returns
   */
  static readBearerToken(bearerString: string) {
    return lucia.readBearerToken(bearerString);
  }

  static async validate(sessionId: string, attributes: SessionAttributes = {}) {
    const { session: luciaSession } = await lucia.validateSession(sessionId);

    if (luciaSession) {
      // 1分钟更新活跃时间
      if (!luciaSession.activeAt || Date.now() - luciaSession.activeAt.getTime() > 1 * 60 * 1000) {
        await db.prisma.userSession.update({
          where: {
            id: luciaSession.id,
          },
          data: {
            ...attributes,
            activeAt: new Date(),
          },
        });
      }

      return new SessionSO(luciaSession);
    }

    return null;
  }

  public get luciaSession() {
    return this._luciaSession;
  }

  // 直接使用
  toVO(): SessionVO {
    return {
      id: this.id,
      expiresAt: this._luciaSession.expiresAt.toISOString(),
      activeAt: this._luciaSession.activeAt?.toISOString(),
      userId: this.userId,
      fresh: this.fresh,
    };
  }

  toMaskedVO(): SessionVO {
    const password = this.id;
    // 保留前5位明文，后面的打星号
    const visibleLength = 5; // 明文长度
    const maxLength = 10;
    const maskedPassword =
      password.slice(0, visibleLength) + '*'.repeat(Math.min(maxLength, password.length - visibleLength));
    return {
      id: maskedPassword, // 重要秘钥，打上星星
      expiresAt: this._luciaSession.expiresAt.toISOString(),
      activeAt: this._luciaSession.activeAt?.toISOString(),
      userId: this.userId,
      fresh: this.fresh,
      ip: this._luciaSession.ip,
      hostname: this._luciaSession.hostname,
      version: this._luciaSession.version,
    };
  }

  /**
   * 获取Lucia Cookie属性
   * @returns
   */
  toCookie() {
    // if (this.fresh) { // 新鲜状态才更新cookie，否则不更新
    // const blankSessionCookie = lucia.createBlankSessionCookie();
    const sessionCookie = lucia.createSessionCookie(this.id);
    return sessionCookie;
    // }

    // const sessionCookie = lucia.createSessionCookie(this.id);
    // return sessionCookie;
  }

  async refresh(attributes: SessionAttributes = {} as SessionAttributes) {
    return db.prisma.userSession.update({
      where: {
        id: this.id,
      },
      data: attributes,
    });
  }

  /**
   * 获取用户的sessions (DB models)
   *
   * @param userId
   */
  static async find(userId: string) {
    const sess = await lucia.getUserSessions(userId);
    return sess.map((s) => new SessionSO(s));
  }

  /**
   * 用于令cookie失效的cookie实例
   * @returns
   */
  static getBlankCookie() {
    const blankCookie = lucia.createBlankSessionCookie();
    return blankCookie;
  }

  public static async create(userId: string, attributes: SessionAttributes = {}) {
    const session = await lucia.createSession(userId, attributes);
    await lucia.validateSession(session.id);

    return new SessionSO(session);
  }
}
