import { inferAsyncReturnType } from '@trpc/server';
import { FetchCreateContextFnOptions } from '@trpc/server/adapters/fetch';
import { Locale } from '@bika/types/i18n/bo';
import { LOCALE_HEADER } from '@bika/types/shared';
import { ApiFetchRequestContext } from '@bika/types/user/vo';
import { SessionSO } from '../session/session-so';
import { parseAttributesFromRequest } from '../utils';

export async function createFetchRequestContext({
  req,
  resHeaders,
}: FetchCreateContextFnOptions): Promise<ApiFetchRequestContext> {
  const { headers } = req;
  const sessionId = SessionSO.parseSessionIdFromHeader(headers);
  const locale = (headers.get(LOCALE_HEADER) || 'en') as Locale;
  if (sessionId) {
    const attributes = parseAttributesFromRequest(headers);
    const sessionSO = await SessionSO.validate(sessionId, attributes);

    if (sessionSO) {
      if (sessionSO.fresh) {
        resHeaders.append('Set-Cookie', sessionSO.toCookie().serialize());
      }
      // 已经登录成功
      return {
        req,
        resHeaders,
        session: sessionSO.toVO(),
        locale,
      };
    }
  }

  return {
    req,
    resHeaders,
    session: null,
    locale,
  };
}

export type CreateFetchContext = inferAsyncReturnType<typeof createFetchRequestContext>;
