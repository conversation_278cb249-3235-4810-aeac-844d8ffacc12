import { getAppEnv } from 'sharelib/app-env';
import type { LogNameType, LogValue } from './models/define';
import { LogSearchPaginationProps, LogSearchProps, PaginationSearchResult, SearchResult } from './types';

/**
 * 日志客户端抽象类
 */
export abstract class LogClient {
  // index 前缀
  protected _indexPrefix: string;

  constructor() {
    const appEnv = getAppEnv();
    this._indexPrefix = `bika-${appEnv}`.toLowerCase(); // 默认prefix是appenv
  }

  getIndexName(logType: LogNameType) {
    return `${this._indexPrefix}-${logType}-index`.toLowerCase().replace(/-/g, '_');
  }

  /**
   * 指定条件分页查询, 有分页统计结果
   */
  abstract paginationSearch<T = Record<string, unknown>>(
    logType: LogNameType,
    props?: LogSearchPaginationProps,
  ): Promise<PaginationSearchResult<T>>;

  /**
   * 指定条件查询, 有分页参数, 无分页统计结果
   */
  abstract search<T = Record<string, unknown>>(
    logType: LogNameType,
    props?: LogSearchPaginationProps,
  ): Promise<SearchResult<T>>;

  /**
   * 指定条件查总数, 无分页参数
   */
  abstract count(logType: LogNameType, props?: LogSearchProps): Promise<number>;

  /**
   * 写入
   */
  abstract write(log: LogValue): Promise<boolean>;

  /**
   * 批量写入
   */
  abstract batchWrite(logs: LogValue[]): Promise<boolean>;
}
