/* eslint-disable @typescript-eslint/no-explicit-any */
import { type Schema } from 'mongoose';
import { performance } from 'perf_hooks';
import { isPerfHook } from 'sharelib/app-env';

export function GlobalMongoosePlugin(schema: Schema) {
  if (isPerfHook()) {
    schema.pre(
      [
        'find',
        'findOne',
        'save',
        'updateOne',
        'updateMany',
        'findOneAndUpdate',
        'findOneAndDelete',
        'deleteOne',
        'deleteMany',
      ],
      function (next) {
        (this as any)._startTime = performance.now(); // Store the start time for later use
        next();
      },
    );

    schema.post(
      [
        'find',
        'findOne',
        'save',
        'updateOne',
        'updateMany',
        'findOneAndUpdate',
        'findOneAndDelete',
        'deleteOne',
        'deleteMany',
      ],
      function (_docs) {
        const end = performance.now();
        const duration = end - (this as any)._startTime; // Convert to milliseconds

        const modelName = (this as any).mongooseCollection?.name;
        const op = (this as any).op;

        console.log(`[PERF]MongoDB Query ${modelName}.${op} took ${duration.toFixed(3)} ms.`);
      },
    );

    schema.pre('aggregate', function (next) {
      (this as any)._startTime = performance.now(); // Store the start time for later use
      next();
    });

    schema.post('aggregate', function (_docs) {
      const end = performance.now();
      const duration = end - (this as any)._startTime; // Convert to milliseconds

      const model = (this as any)._model;
      const modelName = model.modelName;

      console.log(`[PERF]MongoDB Query ${modelName}.aggregate took ${duration.toFixed(3)} ms.`);
    });
  }
}
