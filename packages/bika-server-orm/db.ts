import { getAppEnv } from 'sharelib/app-env';
import { MinioClient, Minio } from 'sharelib/minio-client';
import { Searcher } from 'sharelib/search';
import { Argon2id } from 'oslo/password';
import { LogClient } from './log/log-client';
import { OpenObserveLogClient } from './log/openobserve-log-client';
import { MongoClient, MongoTransactionCB } from './mongo';
import { ExtendedPrismaClient, prismaClient, PrismaTransactionClient, PrismaTransactionFn } from './prisma/client';
import { PrismaPromise } from './prisma/prisma-client';
import { RedisClient } from './redis';
import { SearchIndex } from './search/types';
import stripeClient from './stripe/stripe-client';
import { generateNanoID } from 'basenext/utils/nano-id';

// db的一些暴露的函数和对象
export type { PrismaTransactionClient, PrismaTransactionFn };
export * from './prisma/prisma-client';
export * from './mongo';
export * from './redis';
export * from './search';
export { Minio };

// 分布式跨库操作
export type DistributedOperation = {
  // Prisma操作
  prismaOperations: PrismaPromise<unknown>[];
  // Mongo操作
  mongoOperations: MongoTransactionCB[];
};

export const newDistributedOperation = (): DistributedOperation => {
  return { prismaOperations: [], mongoOperations: [] };
};

export class db {
  static get prisma(): ExtendedPrismaClient {
    return prismaClient;
  }

  private static _redisClient: RedisClient;

  static get redis() {
    if (!this._redisClient) {
      this._redisClient = new RedisClient();
      // 这里开始异步连接了...
    }
    return this._redisClient;
  }

  static get stripe(): typeof stripeClient {
    return stripeClient;
  }

  static get mongo() {
    MongoClient.initOnce();
    return MongoClient;
  }

  private static _minioClient: MinioClient;

  private static lazyInitMinio() {
    if (!this._minioClient) {
      this._minioClient = new MinioClient();
    }
  }

  /**
   * 初始化 Minio bucket，在seed.ts中调用
   */
  static async initMinioBucket() {
    this.lazyInitMinio();
    await this._minioClient.checkAndInitBucket();
  }

  static get minio(): Minio.Client {
    this.lazyInitMinio();
    return this._minioClient!.client;
  }

  static get minioBucketName() {
    this.lazyInitMinio();
    return this._minioClient.bucketName;
  }

  static get minioEndpoint() {
    this.lazyInitMinio();
    return this._minioClient.endpoint;
  }

  static get minioPublicEndpoint() {
    this.lazyInitMinio();
    return this._minioClient.publicEndpoint;
  }

  private static _logClient: LogClient;

  static get log() {
    if (!this._logClient) {
      // 可根据环境变量选择不同的log client
      this._logClient = new OpenObserveLogClient();
    }
    return this._logClient;
  }

  /**
   * Search Client
   */
  private static _searchClient: Searcher<SearchIndex>;

  static get search() {
    if (!this._searchClient) {
      const appEnv = getAppEnv();
      const indexPrefix = `bika-${appEnv}`.toLowerCase();
      this._searchClient = new Searcher<SearchIndex>(indexPrefix);
    }
    return this._searchClient;
  }

  /**
   * Helper functions，迅速获取一些常用的数据
   */
  static get helper() {
    return {
      // 创建站长管理员, 如果已存在, 则忽略
      createSiteAdmin: async () => {
        const siteAdminPO = await db.prisma.siteAdmin.findFirst();
        if (siteAdminPO) {
          // 站长是否被误删?
          const adminUser = await db.prisma.user.findUnique({ where: { id: siteAdminPO.id } });
          if (adminUser) {
            // 至少有一个站长, 说明已经初始化过了, 不需要再初始化
            return;
          } else {
            // 不存在, 删掉, 下面重新初始化
            await db.prisma.siteAdmin.delete({ where: { id: siteAdminPO.id } });
          }
        }
        const defaultPassword = process.env.SITE_ADMIN_INIT_PASSWORD || 'bikaadmin';
        const hashedPassword = await new Argon2id().hash(defaultPassword);
        const userPO = await db.prisma.user.create({
          data: {
            id: generateNanoID('usr'),
            username: 'admin',
            hashedPassword,
            name: 'Bika Admin',
            email: '<EMAIL>',
            avatar: {
              type: 'COLOR',
              color: 'BLUE',
            },
            settings: {
              locale: 'en',
            },
            metadata: {},
          },
        });
        await db.prisma.siteAdmin.create({
          data: {
            id: userPO.id,
            settings: {},
          },
        });
      },
      isSiteAdmin: async (userId: string) => {
        const appEnv = getAppEnv();
        // 本地开发环境，不进行admin校验，方便开发
        if (appEnv === 'LOCAL') {
          return true;
        }
        const userPO = await db.prisma.user.findUnique({
          where: {
            id: userId,
          },
        });

        // 非生产环境和私有化环境，允许通过公司邮箱直接通过
        if (
          appEnv !== 'PRODUCTION' &&
          appEnv !== 'SELF-HOSTED' &&
          (userPO?.email?.includes('@aitable.ai') || userPO?.email?.includes('@vikadata.com'))
        ) {
          return true;
        }

        const userAdminPO = await db.prisma.siteAdmin.findUnique({
          where: {
            id: userId,
          },
        });
        return userAdminPO !== null;
      },
    };
  }
}
