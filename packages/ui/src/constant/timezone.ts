import { getTimeZones } from '@vvo/tzdb';

// 对时区进行排序
const timeZones = getTimeZones();
const sortedTimeZones = timeZones.sort((a, b) => {
  const [continentA, cityA] = a.name.split('/');
  const [continentB, cityB] = b.name.split('/');

  // 先按洲排序，再按城市排序
  if (continentA === continentB) {
    return cityA.localeCompare(cityB);
  }
  return continentA.localeCompare(continentB);
});

export const SORT_TIMEZONES_OPTIONS = sortedTimeZones.map((tz) => {
  const offsetHour = tz.rawOffsetInMinutes / 60;
  const utc = `UTC${offsetHour >= 0 ? '+' : ''}${offsetHour}`;
  return {
    label: `${tz.name} (${utc})`,
    value: tz.name,
  };
});

export const TIMEZONE_OPTIONS = [{ label: 'AUTO', value: 'AUTO' }, ...SORT_TIMEZONES_OPTIONS];
