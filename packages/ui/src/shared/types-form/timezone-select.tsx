import Link from '@mui/joy/Link';
import ListDivider from '@mui/joy/ListDivider';
import Tooltip from '@mui/joy/Tooltip';
import Typography from '@mui/joy/Typography';
import MenuItem from '@mui/material/MenuItem';
import MuiSelect from '@mui/material/Select';
import { groupBy } from 'lodash';
import type React from 'react';
import { useState, useCallback, useMemo } from 'react';
import type { JSX } from 'react/jsx-runtime';
import { useLocale } from '@bika/contents/i18n';
import CheckOutlined from '@bika/ui/icons/components/check_outlined';
import QuestionCircleOutlined from '@bika/ui/icons/components/question_circle_outlined';
import SearchOutlined from '@bika/ui/icons/components/search_outlined';
import { TIMEZONE_OPTIONS, SORT_TIMEZONES_OPTIONS } from '../../constant/timezone';
import { Input, FormLabel } from '../../form-components';
import { Box } from '../../layout-components';

interface TimeZoneSelectProps {
  placeholder: string;
  value: string;
  onChange?: (newValue: string) => void;
  label?: string;
  required?: boolean;
  helpLink?: {
    text: string | React.ReactNode;
    url: string;
  };
  noAuto?: boolean;
}

export const TimeZoneSelect = (props: TimeZoneSelectProps) => {
  const { placeholder, value, onChange, noAuto } = props;
  const dataOptions = noAuto ? SORT_TIMEZONES_OPTIONS : TIMEZONE_OPTIONS;
  const { t } = useLocale();
  const [searchTerm, setSearchTerm] = useState('');
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  }, []);
  const groupData = useMemo(() => {
    let options = dataOptions;
    if (searchTerm) {
      const lowerSearchTerm = searchTerm.toLowerCase();
      options = dataOptions.filter(
        (option) => option.value === value || option.label.toLowerCase().includes(lowerSearchTerm),
      );
    }
    return groupBy(options, (option) => {
      if (option.value === 'AUTO') {
        return 'AUTO';
      }
      const [continent, _city] = option.label.split('/');
      return continent;
    });
  }, [searchTerm, value]);

  const renderOptions = () => {
    const options: JSX.Element[] = [];
    for (const [index, [name, group]] of Object.entries(groupData).entries()) {
      if (index !== 0) {
        options.push(<ListDivider key={`${name}-divider`} />);
      }
      options.push(
        <MenuItem key={`${name}-group`} disabled>
          <Typography level="body-xs">
            {name} ({group.length})
          </Typography>
        </MenuItem>,
      );
      for (const item of group) {
        options.push(
          <MenuItem key={item.value} value={item.value}>
            {item.label}
            {props.value === item.value && (
              <Typography sx={{ ml: 'auto' }}>
                <CheckOutlined size={13} />
              </Typography>
            )}
          </MenuItem>,
        );
      }
    }
    return options;
  };

  return (
    <Box mt={1}>
      {props.label && (
        <FormLabel required={props.required}>
          {props.label}
          {props.helpLink && (
            <Tooltip title={props.helpLink.text} variant="solid" arrow color="neutral" placement="top">
              <Link
                href={props.helpLink.url}
                target="_blank"
                rel="noreferrer"
                endDecorator={<QuestionCircleOutlined />}
              />
            </Tooltip>
          )}
        </FormLabel>
      )}
      <MuiSelect
        displayEmpty
        value={value}
        onChange={(event) => {
          const val = event.target.value;
          if (val === null) {
            return;
          }
          onChange?.(val);
        }}
        MenuProps={{
          autoFocus: false,
        }}
        onClose={() => {
          setSearchTerm('');
        }}
        renderValue={(selectedValue) => {
          if (!selectedValue) {
            return <em style={{ opacity: 0.9 }}>{placeholder}</em>;
          }
          const item = dataOptions.find((o) => o.value === selectedValue);
          return (
            <Typography level="body-lg" sx={{ flexGrow: 1, textAlign: 'left', fontSize: '1rem' }}>
              {item?.label}
            </Typography>
          );
        }}
        sx={{ width: '100%' }}
      >
        <Box
          sx={{
            p: 1,
            position: 'sticky',
            top: '0',
            backgroundColor: 'var(--bg-popup)',
            zIndex: 1,
          }}
          onMouseDown={(e) => {
            e.preventDefault();
            e.stopPropagation();
          }}
        >
          <Input
            size="sm"
            startDecorator={<SearchOutlined />}
            placeholder={t.action.search_placeholder}
            value={searchTerm}
            onChange={handleSearchChange}
            onKeyDown={(e) => e.stopPropagation()}
            onMouseDown={(e) => e.stopPropagation()}
            autoFocus
          />
        </Box>
        {renderOptions()}
      </MuiSelect>
    </Box>
  );
};
