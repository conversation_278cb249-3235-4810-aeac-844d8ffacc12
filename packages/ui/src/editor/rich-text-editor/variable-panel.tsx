import Breadcrumbs from '@mui/joy/Breadcrumbs';
import Link from '@mui/joy/Link';
import Switch from '@mui/joy/Switch';
import Typography from '@mui/joy/Typography';
import last from 'lodash/last';
import React, { useState, forwardRef, useImperativeHandle } from 'react';
import type { DatabaseFieldType } from '@bika/types/database/bo';
import { FieldTypeIconComponent } from '@bika/ui/database/record-detail';
import { Tooltip } from '@bika/ui/tooltip-components';
import type { VariablePanelProps, SlashMenuProp, VariablePanelEditorProps } from './interface';
import { LeftVariablePanel } from './left-variable-panel';
import { formatName } from './menu-utils';
import { getNames, getMenus } from './utils';
import { Button } from '../../button-component';
import { List, ListItemButton, ListItemContent } from '../../form-components';
import CheckboxOutlined from '../../icons/components/checkbox_outlined';
import ChevronRightOutlined from '../../icons/components/chevron_right_outlined';
import ArrayOutlined from '../../icons/doc_hide_components/array_outlined';
import BodyOutlined from '../../icons/doc_hide_components/body_outlined';
import JsonOutlined from '../../icons/doc_hide_components/json_outlined';
import VerificationCodeOutlined from '../../icons/doc_hide_components/verification_code_outlined';
import { Box } from '../../layout-components';

export const VariablePanel = forwardRef((props: VariablePanelProps, ref) => {
  const { slashMenu, locale, handleMenu, values, setValues, filter } = props;
  const t = locale?.t;
  const isArr = filter?.type === 'array';

  const [names, setNames] = useState<string[]>([]);
  const [currentMenu, setCurrentMenu] = useState<SlashMenuProp[]>(slashMenu);

  const [fieldList, setFieldList] = useState<string[]>([]);
  const [fieldNames, setFieldNames] = useState<string[]>([]);

  const isAuto = ['_actions', '_triggers'].includes(values[0]);
  const recordRef = React.useRef<number>(-1);
  const rightOtherRef = React.useRef<number>(-1);

  const rightMenuShowTitle = values.length === 2 && isAuto;

  const isList = last(values) === 'list';
  const isGrid = last(values) === 'grid';

  const reset = () => {
    setCurrentMenu(slashMenu);
    setValues([]);
    setNames([]);
    setFieldList([]);
    setFieldNames([]);
  };

  // 使用 useImperativeHandle 控制暴露给父组件的 ref
  useImperativeHandle(
    ref,
    (): VariablePanelEditorProps => ({
      reset,
      fieldList,
      fieldNames,
      setCurrentMenu,
      setNames,
    }),
  );

  const handleBreadcrumbs = (index: number) => {
    const newValues = values.slice(0, index + (isAuto ? 2 : 1));
    const menus = getMenus(slashMenu, newValues);
    if (menus) {
      setCurrentMenu(menus);
    }
    setValues(newValues);
    setNames(getNames(slashMenu, newValues, locale));
    recordRef.current = -1;
    rightOtherRef.current = -1;
  };

  const tips = currentMenu.find((menu) => menu.id === '_tips');

  return (
    <Box
      sx={{
        borderRadius: '8px',
        padding: '0',
        border: '1px solid var(--border-default)',
        background: 'var(--bg-popup)',
        boxShadow: 'var(--shadow-high)',
        minWidth: '540px',
        maxHeight: '500px',
      }}
      display="flex"
    >
      <LeftVariablePanel
        handleMenu={handleMenu}
        slashMenu={slashMenu}
        locale={locale}
        values={values}
        setValues={setValues}
        setCurrentMenu={setCurrentMenu}
        setNames={setNames}
        recordRef={recordRef}
        rightOtherRef={rightOtherRef}
      />
      <Box minWidth="320px" sx={{ p: 1 }} position="relative">
        <Typography level="h7" sx={{ margin: '8px 0 8px 0', padding: '0 8px' }}>
          {t?.automation.variable_select.select_data_title}
        </Typography>
        {values.length > 0 ? (
          <>
            <Breadcrumbs
              separator="›"
              sx={{
                padding: '4px 0 8px 0',
                margin: '0 8px 0 8px',
                borderBottom: '1px solid var(--border-default)',
                fontSize: '12px',
              }}
            >
              {names.slice(isAuto ? 1 : 0).map((value, index) =>
                names.slice(isAuto ? 1 : 0).length === index + 1 ? (
                  <Box key={value}>{formatName(locale, value)}</Box>
                ) : (
                  <Link key={value} onClick={() => handleBreadcrumbs(index)}>
                    {formatName(locale, value)}
                  </Link>
                ),
              )}
            </Breadcrumbs>
            {currentMenu.length > 0 ? (
              <List
                size="md"
                sx={{
                  fontSize: '14px',
                  maxHeight: isList || isGrid ? 'calc(100% - 118px)' : 'calc(100% - 92px)',
                  overflowY: 'auto',
                  paddingTop: '0',
                }}
              >
                {currentMenu
                  .filter((menu) => {
                    if (isArr && (menu.id === 'record_id_list' || menu.id === '_memberIds')) {
                      return false;
                    }
                    return menu.id !== 'description' && menu.id !== '_tips';
                  })
                  .map((menu, idx) => {
                    const currTip = tips?.children?.find((tip) => tip.id === menu.id);
                    // console.log('menu', menu, values);

                    const isRecord = ['list', 'grid'].includes(menu.id) || menu.id.includes('record');
                    const isDb = menu.id === 'database';
                    const isCellDataArray =
                      (menu.id === 'data' || menu.id === 'value') && menu.children?.[1]?.id === '[0]';
                    const isOther = !isRecord && !isDb;
                    if (isRecord && recordRef.current === -1) {
                      recordRef.current = idx;
                    }
                    if (isOther && rightOtherRef.current === -1) {
                      rightOtherRef.current = idx;
                    }
                    const isField = last(values)?.startsWith('fld') || values[values.length - 2] === 'cells';
                    const isFieldValue = isField && menu.id === 'value';
                    const isFieldData = isField && menu.id === 'data';
                    const isObjectType = menu.children && menu.children[0]?.id !== 'length';
                    const isNumberType = isFieldData && typeof menu.name === 'number';
                    const isBooleanType = isFieldData && typeof menu.name === 'boolean';
                    const isFieldType = isField && !menu.children && menu.id === 'fieldType';
                    const isArrayType =
                      (menu.children && menu.children[0]?.id === 'length') ||
                      menu.id === '_memberIds' ||
                      menu.id === 'record_id_list';

                    const menuDisabled =
                      (isArr && !isObjectType && !isArrayType) || (isArr && ['list', 'grid'].includes(menu.id));
                    const btnHidden = isArr && !isArrayType;
                    const content = (
                      <ListItemButton
                        onClick={() => {
                          if (menuDisabled || (isArr && isArrayType)) {
                            return;
                          }
                          if (!isList && !isGrid && !isCellDataArray) {
                            handleMenu(menu);
                          }
                        }}
                        sx={{
                          display: 'flex',
                          gap: '4px',
                          color: menuDisabled ? 'var(--text-disabled)' : 'var(--text-primary)',
                          cursor: menuDisabled ? 'not-allowed' : 'pointer',
                          padding: '0 8px',
                          borderRadius: '4px',
                          '& button': {
                            display: 'none',
                          },
                          '&:not(.Mui-selected):hover': {
                            backgroundColor: menuDisabled ? 'unset' : 'var(--hover)',
                            color: menuDisabled ? 'var(--text-disabled)' : 'var(--text-primary)',
                          },
                          '&:not(.Mui-selected):hover button': {
                            display: 'block',
                          },
                        }}
                      >
                        {menu.fieldType ? (
                          <FieldTypeIconComponent type={menu.fieldType as DatabaseFieldType} />
                        ) : (
                          <Box flexShrink={1}>
                            {isArrayType && <ArrayOutlined color="var(--text-secondary)" />}
                            {isObjectType && <JsonOutlined color="var(--text-secondary)" />}
                            {!menu.children && !isNumberType && !isBooleanType && !isArrayType && (
                              <BodyOutlined color="var(--text-secondary)" />
                            )}
                            {isNumberType && <VerificationCodeOutlined color="var(--text-secondary)" />}
                            {isBooleanType && <CheckboxOutlined color="var(--text-secondary)" />}
                          </Box>
                        )}
                        {isFieldData && <ListItemContent>{t?.automation.variable_select.field.data}</ListItemContent>}
                        {isFieldValue && <ListItemContent>{t?.automation.variable_select.field.value}</ListItemContent>}
                        {isFieldType && <ListItemContent>{t?.automation.variable_select.field.type}</ListItemContent>}
                        {!isFieldType && !isFieldData && !isFieldValue && (
                          <ListItemContent>{formatName(locale, menu.name)}</ListItemContent>
                        )}
                        {(isList || isGrid) && (
                          <Switch
                            size="sm"
                            checked={fieldList.some((fl) => fl === menu.id)}
                            onChange={(e) => {
                              e.stopPropagation();
                              const checked = e.target.checked;
                              if (checked) {
                                const newFieldList = fieldList.concat(menu.id);
                                const newFieldNames = fieldNames.concat(menu.name);
                                setFieldList(newFieldList);
                                setFieldNames(newFieldNames);
                              } else {
                                const newFieldList = fieldList.filter((field) => field !== menu.id);
                                const newFieldNames = fieldNames.filter((field) => field !== menu.name);
                                setFieldList(newFieldList);
                                setFieldNames(newFieldNames);
                              }
                            }}
                          />
                        )}
                        {menu.children && !['list', 'grid'].includes(menu.id) && !isList && !isGrid && !btnHidden && (
                          <Button
                            size="sm"
                            variant="plain"
                            color="neutral"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleMenu(menu, { isEnd: true });
                            }}
                          >
                            {t?.automation.variable_select.select_variable}
                          </Button>
                        )}
                        {menu.children && !isCellDataArray && !isList && !isGrid && (isArr ? isObjectType : true) && (
                          <ChevronRightOutlined color="var(--text-secondary)" />
                        )}
                      </ListItemButton>
                    );
                    return (
                      <React.Fragment key={menu.id}>
                        {rightMenuShowTitle && isRecord && recordRef.current === idx && (
                          <Typography
                            level="b4"
                            sx={{ mt: 2, mb: 0.5, padding: '0 8px' }}
                            textColor="var(--text-secondary)"
                          >
                            {t?.automation.variable_select.record_title}
                          </Typography>
                        )}
                        {rightMenuShowTitle && isOther && rightOtherRef.current === idx && (
                          <Typography
                            level="b4"
                            sx={{ mt: 2, mb: 0.5, padding: '0 8px' }}
                            textColor="var(--text-secondary)"
                          >
                            {t?.automation.variable_select.other_title}
                          </Typography>
                        )}
                        {rightMenuShowTitle && isDb && (
                          <Typography
                            level="b4"
                            sx={{ mt: 2, mb: 0.5, padding: '0 8px' }}
                            textColor="var(--text-secondary)"
                          >
                            {t?.automation.variable_select.database_title}
                          </Typography>
                        )}
                        {currTip ? (
                          <Tooltip title={currTip.name} placement="right" arrow sx={{ maxWidth: '200px' }}>
                            {content}
                          </Tooltip>
                        ) : (
                          content
                        )}
                      </React.Fragment>
                    );
                  })}
              </List>
            ) : (
              <Typography
                level="b3"
                sx={{ p: 2, width: '100%', textAlign: 'center' }}
                textColor="var(--text-secondary)"
              >
                {t?.automation.variable_select.data_empty}
              </Typography>
            )}

            {(isList || isGrid) && (
              <Box
                position="absolute"
                bottom="16px"
                width="calc(100% - 24px)"
                display="flex"
                justifyContent="space-between"
              >
                <Box>
                  <Button
                    size="sm"
                    variant="plain"
                    color="neutral"
                    onClick={() => {
                      const newFieldList = currentMenu
                        .filter((menu) => menu.id !== 'description')
                        .map((menu) => menu.id);
                      const newFieldNames = currentMenu
                        .filter((menu) => menu.id !== 'description')
                        .map((menu) => menu.name);
                      setFieldList(newFieldList);
                      setFieldNames(newFieldNames);
                    }}
                    sx={{
                      mr: 1,
                    }}
                  >
                    {t?.automation.variable_select.field.select_all}
                  </Button>
                  <Button
                    size="sm"
                    variant="plain"
                    color="neutral"
                    onClick={() => {
                      setFieldList([]);
                      setFieldNames([]);
                    }}
                  >
                    {t?.automation.variable_select.field.clear_all}
                  </Button>
                </Box>
                <Button
                  size="sm"
                  disabled={fieldList.length === 0}
                  onClick={() => {
                    if (fieldList.length === 0) {
                      return;
                    }
                    handleMenu(slashMenu[0], { listOrGridType: isList ? 'list' : 'grid' });
                  }}
                >
                  {t?.automation.variable_select.insert}
                </Button>
              </Box>
            )}
          </>
        ) : (
          <Typography level="b3" sx={{ p: 2, width: '100%', textAlign: 'center' }} textColor="var(--text-secondary)">
            {t?.automation.variable_select.select_data_placeholder}
          </Typography>
        )}
      </Box>
    </Box>
  );
});
