'use client';

// import type { PopperPlacementType } from '@mui/base/Popper';
import type { MenuButtonOwnerState } from '@mui/joy/MenuButton';
import type { ColorPaletteProp } from '@mui/joy/styles/types';
import Tooltip from '@mui/joy/Tooltip';
import type { SlotProps } from '@mui/joy/utils/types';
import type { Placement as PopperPlacementType } from '@popperjs/core';
import type React from 'react';
import { useState, useRef, useEffect } from 'react';
import { IconButton } from '@bika/ui/button';
import { Divider } from '@bika/ui/divider';
import { Dropdown } from '@bika/ui/dropdown';
import ChevronRightOutlined from '@bika/ui/icons/components/chevron_right_outlined';
import MoreOutlined from '@bika/ui/icons/components/more_outlined';
import { ListItemDecorator } from '@bika/ui/list';
import { MenuButton, MenuItem, Menu } from '@bika/ui/menu';
import { Typography } from '@bika/ui/texts';
import { Box } from '../layout-components';

const setActiveMenuElement = (element: HTMLElement | null) => {
  menuSubscribers.forEach((callback) => callback(element));
};

const menuSubscribers: ((element: HTMLElement | null) => void)[] = [];

export interface IBMenuItem {
  icon?: React.ReactNode;
  label: string;
  onClick?: (_e: React.MouseEvent<HTMLElement>) => void;
  sx?: React.CSSProperties;
  disabled?: boolean;
  disabledTooltip?: string;
  showBadge?: boolean;
  color?: ColorPaletteProp;
  subItems?: IBMenuItem[];
}

interface IBMenuProps {
  items: IBMenuItem[][];
  buttonSlotProps?: SlotProps<'button', object, MenuButtonOwnerState>;
  className?: string;
  placement?: PopperPlacementType;
  icon?: React.ReactNode;
}

const ItemNode = ({ item }: { item: IBMenuItem }) => (
  <>
    {item.icon && (
      <ListItemDecorator sx={{ minInlineSize: '16px', marginInlineEnd: '0', color: 'inherit' }}>
        {item.icon}
      </ListItemDecorator>
    )}
    <Typography sx={{ flexGrow: 1, color: 'inherit' }}>{item.label}</Typography>
    {item.subItems && (
      <ListItemDecorator sx={{ minInlineSize: '16px', marginInlineEnd: '0', color: 'inherit' }}>
        <ChevronRightOutlined color={'var(--text-secondary)'} />
      </ListItemDecorator>
    )}
  </>
);

/**
 * Bika内部的各种菜单
 *
 * @param props
 * @returns
 */
export const BMenu = (props: IBMenuProps) => {
  const {
    items,
    buttonSlotProps = {
      variant: 'plain',
      color: 'neutral',
    },
    className,
    placement = 'right-start',
    icon,
  } = props;

  const mainMenuRef = useRef<HTMLDivElement>(null);
  const subMenuRef = useRef<HTMLDivElement>(null);
  const [activeElementId] = useState(() => Math.random().toString(36).substr(2, 9));

  const [isActive, setIsActive] = useState(false);
  const [mainMenuAnchorEl, setMainMenuAnchorEl] = useState<HTMLElement | null>(null);
  const [subMenuAnchorEl, setSubMenuAnchorEl] = useState<HTMLElement | null>(null);
  const [activeSubMenu, setActiveSubMenu] = useState<IBMenuItem | null>(null);

  useEffect(() => {
    const handleActiveMenuChange = (element: HTMLElement | null) => {
      const isCurrentlyActive = element?.dataset.menuId === activeElementId;
      setIsActive(isCurrentlyActive);
      if (!isCurrentlyActive) {
        setMainMenuAnchorEl(null);
        setSubMenuAnchorEl(null);
        setActiveSubMenu(null);
      }
    };

    menuSubscribers.push(handleActiveMenuChange);
    return () => {
      const index = menuSubscribers.indexOf(handleActiveMenuChange);
      if (index > -1) {
        menuSubscribers.splice(index, 1);
      }
    };
  }, [activeElementId]);

  const handleOpenMainMenu = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();

    const currentTarget = event.currentTarget;
    currentTarget.dataset.menuId = activeElementId;

    setActiveMenuElement(currentTarget);

    setMainMenuAnchorEl(currentTarget);
  };

  const handleOpenSubMenu = (event: React.MouseEvent<HTMLElement>, item: IBMenuItem) => {
    if (!mainMenuAnchorEl) return;

    event.stopPropagation();

    setSubMenuAnchorEl(event.currentTarget);

    setActiveSubMenu(item);
  };

  const handleCloseSubMenu = () => {
    setSubMenuAnchorEl(null);
    setActiveSubMenu(null);
  };

  const handleCloseMainMenu = () => {
    setActiveMenuElement(null);
    setMainMenuAnchorEl(null);
    handleCloseSubMenu();
  };

  const handleMenuItemClick = (item: IBMenuItem, e: React.MouseEvent<HTMLElement>) => {
    e.stopPropagation();

    if (item.onClick && !item.subItems) item.onClick(e);

    if (!item.subItems) handleCloseMainMenu();
  };

  const handleSubMenuItemClick = (subItem: IBMenuItem, e: React.MouseEvent<HTMLElement>) => {
    e.stopPropagation();

    if (subItem.onClick) subItem.onClick(e);

    handleCloseMainMenu();
  };

  const handleMenuItemMouseEnter = (item: IBMenuItem, event: React.MouseEvent<HTMLElement>) => {
    if (item.subItems) {
      handleOpenSubMenu(event, item);
    } else {
      handleCloseSubMenu();
    }
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (!isActive) return;

    const isClickOutsideMainMenu = mainMenuRef.current && !mainMenuRef.current.contains(event.target as Node);
    const isClickOutsideSubMenu = !subMenuRef.current || !subMenuRef.current.contains(event.target as Node);
    const isClickOutsideButton = event.target instanceof Element && !event.target.closest('.MuiMenuButton-root');

    if (isClickOutsideMainMenu && isClickOutsideSubMenu && isClickOutsideButton) {
      handleCloseMainMenu();
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isActive]);

  return (
    <Dropdown>
      <MenuButton
        slots={{ root: IconButton }}
        className={className}
        slotProps={{
          root: buttonSlotProps,
        }}
        onClick={handleOpenMainMenu}
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        {icon || <MoreOutlined color="var(--text-primary)" />}
      </MenuButton>
      {Boolean(mainMenuAnchorEl) && isActive && (
        <Menu
          ref={mainMenuRef}
          placement={placement}
          sx={{
            minWidth: '240px',
            zIndex: 10000,
            visibility: mainMenuAnchorEl && isActive ? 'visible' : 'hidden',
            position: 'absolute',
            top: '100%',
            right: 0,
          }}
          open
          anchorEl={mainMenuAnchorEl}
          onClose={handleCloseMainMenu}
        >
          {items.map((group, groupIndex) => (
            <Box key={groupIndex}>
              {group.map((item, itemIndex) => {
                if (item.disabled && item.disabledTooltip) {
                  return (
                    <Tooltip arrow sx={{ zIndex: 10000 }} key={itemIndex} title={item.disabledTooltip}>
                      <Box>
                        <MenuItem disabled>
                          <ItemNode item={item} />
                        </MenuItem>
                      </Box>
                    </Tooltip>
                  );
                }
                return (
                  <MenuItem
                    key={itemIndex}
                    onClick={(e) => handleMenuItemClick(item, e)}
                    onMouseEnter={(e) => handleMenuItemMouseEnter(item, e)}
                    disabled={item.disabled}
                    color={item.color}
                  >
                    <ItemNode item={item} />
                  </MenuItem>
                );
              })}
              {groupIndex !== items.length - 1 && <Divider sx={{ margin: '4px 8px' }} />}
            </Box>
          ))}
        </Menu>
      )}
      {mainMenuAnchorEl && isActive && (
        <Menu
          ref={subMenuRef}
          open={Boolean(subMenuAnchorEl)}
          anchorEl={subMenuAnchorEl}
          onClose={handleCloseSubMenu}
          sx={{
            minWidth: '240px',
            zIndex: 99999,
            visibility: subMenuAnchorEl ? 'visible' : 'hidden',
            position: 'absolute',
            top: 0,
            left: '100%',
          }}
          placement={placement}
        >
          {activeSubMenu?.subItems?.map((subItem, subIndex) => (
            <MenuItem
              key={subIndex}
              onClick={(e) => handleSubMenuItemClick(subItem, e)}
              disabled={subItem.disabled}
              color={subItem.color}
            >
              {subItem.icon && (
                <ListItemDecorator sx={{ color: 'inherit', minInlineSize: 'unset', marginRight: '4px' }}>
                  {subItem.icon}
                </ListItemDecorator>
              )}
              {subItem.label}
            </MenuItem>
          ))}
        </Menu>
      )}
    </Dropdown>
  );
};
