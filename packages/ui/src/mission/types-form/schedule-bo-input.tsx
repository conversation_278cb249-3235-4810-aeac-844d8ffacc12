import { ILocaleContext } from '@bika/contents/i18n';
import { BikaDateTimeRepeatSchema, Scheduler } from '@bika/types/system/datetime';
import { TIMEZONE_OPTIONS } from '../../constant/timezone';
import { Box } from '../../layout-components';
import { BooleanInput } from '../../shared/types-form/boolean-input';
import { SelectInput } from '../../shared/types-form/select-input';
import { DatetimeOrDynamicBoInput } from '../../shared/types-form/system/datetime-or-dynamic-bo-input';
import { DatetimeRepeatBoInput } from '../../shared/types-form/system/datetime-repeat-bo-input';
import { ITypesFormProps } from '../../shared/types-form/types';

export interface ScheduleBoProps extends ITypesFormProps<Scheduler> {
  locale: ILocaleContext;
}

export function ScheduleBoInput(props: ScheduleBoProps) {
  const { onChange, value, locale } = props;
  const { repeat, timezone, datetime } = value as Scheduler;

  const { t } = locale;
  return (
    <Box>
      <SelectInput
        label={t.scheduler.timezone}
        options={TIMEZONE_OPTIONS}
        value={timezone ?? 'AUTO'}
        onChange={(newVal) => {
          onChange({ ...value, timezone: newVal ?? 'AUTO' });
        }}
      />

      <DatetimeOrDynamicBoInput
        locale={locale}
        label={t.mission.start_time}
        value={datetime}
        onChange={(_newValue) => {
          if (_newValue) {
            onChange({ ...value, datetime: _newValue });
          }
        }}
      />

      <BooleanInput
        onChange={(_newValue) => {
          if (_newValue) {
            const defaultValue = BikaDateTimeRepeatSchema.parse(undefined);
            onChange({
              ...value,
              repeat: defaultValue,
            });
          } else {
            onChange({
              ...value,
              repeat: undefined,
            });
          }
        }}
        value={repeat != null}
        label={t.mission.repeat}
      />

      {repeat != null && (
        <DatetimeRepeatBoInput
          locale={locale}
          value={repeat}
          onChange={(_newValue) => {
            onChange({
              ...value,
              repeat: _newValue,
            });
          }}
        />
      )}
    </Box>
  );
}
