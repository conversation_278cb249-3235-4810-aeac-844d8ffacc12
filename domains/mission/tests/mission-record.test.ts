import { generateNanoID } from 'basenext/utils/nano-id';
import { test, expect } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { MissionCreateMultiRecordsSchema } from '@bika/types/mission/bo';
import { MissionSO } from '../server/mission-so';

function waitForMissionMatchToBeMet(
  missionId: string,
  targetCount: number,
  timeout: number,
  interval: number,
): Promise<void> {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();

    const checkCondition = setInterval(async () => {
      const currentTime = Date.now();
      const mission = await MissionSO.init(missionId);
      if (mission.model.bo.state!.count === targetCount) {
        clearInterval(checkCondition);
        resolve();
      } else if (currentTime - startTime > timeout) {
        clearInterval(checkCondition);
        reject(new Error('Timeout waiting for condition to be met'));
      }
    }, interval);
  });
}

// 创建多个Records，并且有计数状态state
test('Mission: CRAETE_MULTI_RECORDS', async () => {
  // 创建一个提醒型任务
  // Mission.Create('REMIND')
  const { user, space, member } = await MockContext.initUserContext();

  const tplFolder = await space.installTemplateById(user, 'base-crm');
  // 拿到people，用于创建mission
  const peopleDatabaseNode = await tplFolder.findChildNodeByTemplateId('people');
  expect(peopleDatabaseNode).toBeDefined();
  const peopleDatabase = await peopleDatabaseNode?.toResourceSO<DatabaseSO>();
  expect(peopleDatabase).toBeDefined();

  // 创建一个mission，录入50个people吧
  const missions = await MissionSO.createMission({
    user,
    spaceId: space.id,
    missionTemplate: {
      type: 'CREATE_MULTI_RECORDS',
      amount: 50,
      databaseId: peopleDatabaseNode!.id,
      name: '创建50个人',
      to: [
        {
          type: 'ALL_MEMBERS',
        },
      ],
      dueDate: {
        end: {
          type: 'TODAY',
        },
      },
    },
  });

  // 只有1个，毕竟我空间站只有一个人(share模式，但1个人就1个任务)
  expect(missions.length).toBe(1);
  const mission = missions[0];
  expect(mission).toBeDefined();
  // 要求创建50个
  const missionBO = MissionCreateMultiRecordsSchema.parse(mission.model.bo);
  expect(missionBO.amount).toBe(50);
  expect(missionBO.state).toBeDefined();
  expect(missionBO.state!.count).toBe(0); // 从0开始计算

  // 开始创建数据吧! 创建一行数据，state.quantiy 自动+1
  // 创建10条记录
  for (let index = 0; index < 10; index += 1) {
    const record = await peopleDatabase?.createRecord(user, member, {
      name: generateNanoID('random_name_'),
      email: `${generateNanoID('')}@abc.com`,
    });
    expect(record).toBeDefined();
  }

  // 重新获取, 异步执行, 无法保证最终执行时间
  // const missionAgain = await MissionSO.init(mission.id);
  // const missionAgainBO = MissionCreateMultiRecordsSchema.parse(missionAgain.model.bo);
  // expect(missionAgainBO.state).toBeDefined();
  // expect(missionAgainBO.state!.count).toBe(10); // 创建了10条记录

  // 按一定时间内循环检查mission.model.bo.state.count，超时就失败
  await waitForMissionMatchToBeMet(mission.id, 10, 10000, 500) // 目标值为10，超时时间10秒，检查间隔500毫秒
    .then(() => console.log('Condition met within the timeout period.'))
    .catch((error) => console.error(error.message));
}, 20000);
