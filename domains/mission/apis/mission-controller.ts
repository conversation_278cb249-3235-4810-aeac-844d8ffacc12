import { RecipientSO } from '@bika/domains/system/server/recipients-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { TRPCError } from '@bika/server-orm/trpc';
import { Mission } from '@bika/types/mission/bo';
import { MissionVO } from '@bika/types/mission/vo';
import { MissionSO } from '../server/mission-so';

/**
 * 验证用户与mission的关系
 * 1. mission 是用户创建的
 * 2. mission 是用户被分配的
 */
async function checkUserInMission(userId: string, missionSO: MissionSO) {
  const createdBy = missionSO.model.createdBy;
  if (createdBy === userId) {
    return;
  }
  const recipients = await missionSO.getRecipients();
  const exist = await RecipientSO.checkUserInRecipients(userId, recipients);
  if (exist) {
    return;
  }
  throw new TRPCError({ code: 'FORBIDDEN' });
}

export async function list(user: UserSO, spaceId: string): Promise<MissionVO[]> {
  const member = await user.getMember(spaceId);
  const spaceSO = await member.getSpace();
  const missions = await spaceSO.getAllMissions();
  return Promise.all(missions.map((mission) => mission.toVO({ locale: user.locale })));
}

export async function info(user: UserSO, missionId: string): Promise<MissionVO> {
  const missionSO = await MissionSO.init(missionId);
  await checkUserInMission(user.id, missionSO);
  return missionSO.toVO({ locale: user.locale, validateIfInvalid: true });
}

export async function create(user: UserSO, spaceId: string, mission: Mission): Promise<MissionVO[]> {
  const missionSOs = await MissionSO.createMission({
    user,
    spaceId,
    missionTemplate: mission,
  });
  return Promise.all(missionSOs.map((missionSO) => missionSO.toVO({ locale: user.locale })));
}

export async function complete(user: UserSO, spaceId: string, missionId: string) {
  const member = await user.getMember(spaceId);
  return member.completeMissionManually(missionId);
}

export async function reject(user: UserSO, spaceId: string, missionId: string): Promise<boolean> {
  const member = await user.getMember(spaceId);
  return member.rejectMission(missionId);
}

export async function del(user: UserSO, spaceId: string, missionId: string) {
  const member = await user.getMember(spaceId);
  return member.deleteMission(missionId);
}

/**
 * 创建「请求数据」
 */
export async function createMissionInDatabase(user: UserSO, spaceId: string, databaseId: string): Promise<MissionVO[]> {
  const member = await user.getMember(spaceId);
  const space = await member.getSpace();
  const database = await space.getNode(databaseId);
  const missionTpl: Mission = {
    type: 'CREATE_RECORD',
    databaseId,
    // TODO: viewId:
    name: `请求在Database ID: ${database.getName(user.locale)}中创建一条记录`,
    description: `在Database ID: ${database.getName(user.locale)}中创建一条记录，请求人：${user.name}`,
    to: [{ type: 'ALL_MEMBERS' }],
    canReject: true,
  };
  const missionSOs = await MissionSO.createMission({
    user,
    spaceId,
    missionTemplate: missionTpl,
  });
  const missionVOs = await Promise.all(missionSOs.map((missionSO) => missionSO.toVO({ locale: user.locale })));

  return missionVOs;
}
