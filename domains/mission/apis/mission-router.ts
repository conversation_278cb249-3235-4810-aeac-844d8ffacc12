import { z } from 'zod';
import { UserSO } from '@bika/domains/user/server/user-so';
import { protectedProcedure, router } from '@bika/server-orm/trpc';
import { MissionSchema } from '@bika/types/mission/bo';
import * as MissionController from './mission-controller';

export const missionRouter = router({
  /**
   * list missions
   */
  list: protectedProcedure.input(z.object({ spaceId: z.string() })).query(async (opts) => {
    const { input, ctx } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    return MissionController.list(user, input.spaceId);
  }),

  /**
   * Retrieve mission info
   */
  info: protectedProcedure.input(z.object({ id: z.string() })).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return MissionController.info(user, input.id);
  }),

  /**
   * Create a new mission
   */
  create: protectedProcedure.input(z.object({ spaceId: z.string(), mission: MissionSchema })).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return MissionController.create(user, input.spaceId, input.mission);
  }),

  /**
   * 手工完成任务，部分任务支持，大部分任务是跟随系统事件自动完成的
   */
  complete: protectedProcedure.input(z.object({ spaceId: z.string(), id: z.string() })).mutation(async (opts) => {
    const { input, ctx } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    return MissionController.complete(user, input.spaceId, input.id);
  }),

  /**
   * 拒绝任务
   */
  reject: protectedProcedure.input(z.object({ spaceId: z.string(), id: z.string() })).mutation(async (opts) => {
    const { input, ctx } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    return MissionController.reject(user, input.spaceId, input.id);
  }),

  /**
   * 删除任务
   */
  delete: protectedProcedure.input(z.object({ spaceId: z.string(), id: z.string() })).mutation(async (opts) => {
    const { input, ctx } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    return MissionController.del(user, input.spaceId, input.id);
  }),

  /**
   * 请求数据任务
   * 统一使用 mission.create()
   * @deprecated 待重构
   */
  requestDatabaseMission: protectedProcedure
    .input(z.object({ spaceId: z.string(), databaseId: z.string() }))
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      return MissionController.createMissionInDatabase(user, input.spaceId, input.databaseId);
    }),
});
