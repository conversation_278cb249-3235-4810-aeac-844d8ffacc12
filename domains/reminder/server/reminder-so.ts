import assert from 'assert';
import { generateNanoID } from 'basenext/utils/nano-id';
import dayjs from 'dayjs';
import { NotificationSO } from '@bika/domains/notification/server/notification-so';
import { renderIString } from '@bika/domains/shared/server';
import { JobSO } from '@bika/domains/system/server/job-so';
import { RecipientSO } from '@bika/domains/system/server/recipients-so';
import { SchedulerRunTimeCalculator } from '@bika/domains/system/server/scheduler-run-time-calculator';
import { SchedulerSO } from '@bika/domains/system/server/scheduler-so';
import { ReminderRelationType, SchedulerModel } from '@bika/domains/system/server/types';
import { MemberSO } from '@bika/domains/unit/server/member-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { db, mongoose, PrismaPromise, RecipientModel, ReminderModel } from '@bika/server-orm';
import { <PERSON>minder } from '@bika/types/reminder/bo';
import { AgendaEventVO, ReminderVO } from '@bika/types/reminder/vo';
import { SchedulerSchema } from '@bika/types/system';
import { IReminderCreateParam } from './types';

export class ReminderSO {
  private readonly _model: ReminderModel;

  private constructor(reminderPO: ReminderModel) {
    this._model = reminderPO;
  }

  get model() {
    return this._model;
  }

  get id() {
    return this._model.id;
  }

  get schedulerBO() {
    const schedulerJSON = this._model.scheduler;
    const schedulerBO = SchedulerSchema.parse(schedulerJSON);
    return schedulerBO;
  }

  async getRecipients(): Promise<RecipientSO[]> {
    const reminderId = this._model.id;
    return RecipientSO.find('REMINDER', reminderId);
  }

  static async init(reminderId: string) {
    const reminderPO = await db.mongo.reminder.findOne({
      id: reminderId,
    });
    return new ReminderSO(reminderPO!);
  }

  async getEventsVO(limit: number = 10): Promise<AgendaEventVO[]> {
    const events: AgendaEventVO[] = [];
    const MAX_EVENTS = 365; // 上限预防

    const scheduler = this.schedulerBO;
    const datetime = scheduler.datetime as string;
    const basicEvent: AgendaEventVO = {
      type: 'REMINDER',
      id: this._model.id,
      start: datetime,
      end: datetime,
      title: this._model.name,
      description: this._model.description,
      isActive: this._model.isActive,
    };
    events.push(basicEvent);

    // 一次性，非重复的reminder
    if (!scheduler.repeat) {
      return events;
    }

    const timeZone = this._model.createdBy && (await UserSO.init(this._model.createdBy)).timeZone;
    const calc = SchedulerRunTimeCalculator.create(scheduler, timeZone ?? undefined);
    let startTime = dayjs(datetime).add(1, 'millisecond'); // TODO: 这里有可能是1970年，要筛选日期哦
    for (let i = 0; i < Math.min(MAX_EVENTS, limit - 1); i += 1) {
      const nextTime = calc.calculateNextRunTime(startTime);
      if (nextTime === null) {
        break;
      }
      const repeatEvent: AgendaEventVO = {
        type: 'REMINDER',
        title: this._model.name,
        description: this._model.description,
        id: this._model.id,
        start: nextTime?.toISOString(),
        end: nextTime?.toISOString(),
        isActive: this._model.isActive,
      };
      events.push(repeatEvent);
      startTime = nextTime.add(1, 'millisecond');
    }
    return events;
  }

  static async exist(relationId: string): Promise<boolean> {
    const count = await db.mongo.reminder.countDocuments({ id: relationId });
    return count > 0;
  }

  static async deleteByMissionId(missionId: string | string[]) {
    const missionIds = Array.isArray(missionId) ? missionId : [missionId];
    // 首先找对对应unit id的recipient
    const remindersPOs = await db.mongo.reminder.find({
      'property.missionId': { $in: missionIds },
    });
    if (remindersPOs.length === 0) {
      return;
    }
    const reminderIds = remindersPOs.map((r) => r.id);

    await Promise.all([
      db.mongo.reminder.deleteMany({
        id: { $in: reminderIds },
      }),
      db.mongo.recipient('REMINDER').deleteMany({
        relationType: 'REMINDER',
        relationId: { $in: reminderIds },
      }),
    ]);

    await SchedulerSO.deleteByRelationIds(reminderIds, ReminderRelationType);
  }

  /**
   *
   * @param toMemberId
   * @param reminderTpl
   * @param userId  创建者
   * @returns
   */
  static async createToMember(
    toMemberId: string,
    reminder: Reminder,
    userId?: string,
    createParam?: IReminderCreateParam,
  ): Promise<ReminderSO> {
    const user = userId ? await UserSO.init(userId) : undefined;
    const { reminderModel, recipientModels, schedulerOperation } = await this.createReminderOperationWithTemplate(
      { ...createParam, user },
      reminder,
      [toMemberId],
    );
    await db.mongo.transaction(async (session) => {
      if (schedulerOperation) {
        await schedulerOperation;
      }
      await db.mongo.reminder.create([reminderModel], { session });
      await db.mongo.recipient('REMINDER').create(recipientModels, { session });
    });
    return new ReminderSO(reminderModel);
  }

  static async createReminderOperationWithTemplate(
    createParam: IReminderCreateParam,
    reminder: Reminder,
    memberIds: string[],
  ): Promise<{
    reminderModel: ReminderModel;
    recipientModels: RecipientModel[];
    schedulerOperation?: PrismaPromise<SchedulerModel>;
  }> {
    const { user, now } = createParam;
    // 构造reminder模型
    const reminderModel = this.buildModel(createParam, reminder);
    // recipients
    const recipientModels: RecipientModel[] = [];
    for (const memberId of memberIds) {
      recipientModels.push(
        RecipientSO.buildModel('REMINDER', reminderModel.id, 'MEMBER', memberId, { isActive: true }),
      );
    }

    // 如果有就创建scheduler
    const schedulerCreateInput = SchedulerSO.buildSchedulerCreateInputWithTemplate({
      user,
      relationId: reminderModel.id,
      relationType: ReminderRelationType,
      // 取填充之后的 model.scheduler
      scheduler: reminderModel.scheduler,
      now,
    });
    const schedulerOperation = schedulerCreateInput && SchedulerSO.createOperation(schedulerCreateInput);
    return { reminderModel, recipientModels, schedulerOperation };
  }

  /**
   * 构造Reminder数据模型
   * @param createParam 创建参数
   * @param reminder Reminder BO object
   * @private
   */
  private static buildModel(createParam: IReminderCreateParam, reminder: Reminder): ReminderModel {
    const { user, timezone, property, props } = createParam;
    // 深拷贝，避免修改模板原始数据
    const { name, description, scheduler } = JSON.parse(JSON.stringify(reminder));
    const calc = SchedulerRunTimeCalculator.create(scheduler, user?.timeZone || timezone);
    const startDt = calc.getStartDatetime();
    // 模板可使用动态时间，落库时赋予具体时间
    scheduler.datetime = startDt.toISOString();
    return {
      id: generateNanoID('rmd'),
      name: renderIString(name, props),
      description: renderIString(description, props),
      scheduler,
      count: 0,
      property,
      isActive: true,
      createdBy: user?.id,
      updatedBy: user?.id,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  static async getRemindersByMemberId(memberId: string, isActive?: boolean): Promise<ReminderSO[]> {
    const recipientFindQuery: mongoose.FilterQuery<RecipientModel> = {
      relationType: 'REMINDER',
      recipientType: 'MEMBER',
      recipientId: memberId,
    };
    if (isActive !== undefined) {
      recipientFindQuery['state.isActive'] = isActive;
    }
    const reminderRecipients = await db.mongo.recipient('REMINDER').find(recipientFindQuery);
    const memberRems = reminderRecipients.map((rec) => rec.relationId);

    const remindersFindQuery: mongoose.FilterQuery<ReminderModel> = {
      id: { $in: memberRems },
    };
    if (isActive !== undefined) {
      remindersFindQuery.isActive = isActive;
    }
    const remindersPOs = await db.mongo.reminder.find(remindersFindQuery);
    return remindersPOs.map((reminderPO: ReminderModel) => new ReminderSO(reminderPO));
  }

  async getSchedulers(): Promise<SchedulerSO[]> {
    return SchedulerSO.findSchedulersByRelation([this.model.id], ReminderRelationType);
  }

  /**
   * 是否已经过期了，不用提醒了
   * 如果是，通常就是没有scheduler了
   */
  async isOverdue(): Promise<boolean> {
    const schedulers = await this.getSchedulers();
    return schedulers.length <= 0;
  }

  /**
   * Run Schedulers
   * @returns
   */
  async runSchedulers(forceOnce?: boolean): Promise<JobSO[]> {
    const schedulersSOs = await this.getSchedulers();
    const jobs: JobSO[] = [];
    if (schedulersSOs.length === 0) {
      return jobs;
    }
    const schedulerIds: string[] = schedulersSOs.map((scheduler) => scheduler.model.id);
    await SchedulerSO.deleteByIds(schedulerIds);

    // reminder处于激活状态，才会创建Job
    if (!this.model.isActive) {
      return jobs;
    }
    // 目前场景，一个 Reminder 只有一个Scheduler，所以只有一个Job
    const job = await JobSO.new({
      type: 'REMINDER_NOTIFICATION',
      reminderId: this.id,
    });
    jobs.push(job);

    // 上一个完成后，再次重新创建Scheduler。
    // 如果是 SchedulerType.ONCE 一次性的reminder，不用创建了
    if (forceOnce) {
      return jobs;
    }
    const schedulerBO = this.schedulerBO;
    if (schedulerBO.repeat) {
      const user = this.model.createdBy ? await UserSO.init(this.model.createdBy) : undefined;
      await SchedulerSO.createSchedulerOperationsWithTemplate({
        user,
        relationId: this.model.id,
        relationType: ReminderRelationType,
        scheduler: schedulerBO,
      });
    }
    return jobs;
  }

  /**
   * 手动执行提醒器的提醒通知
   */
  async doRemind(): Promise<NotificationSO | null> {
    const recipients: RecipientSO[] = await this.getRecipients();
    if (recipients.length === 0) {
      return null;
    }
    const { recipientType, recipientId } = (recipients[0] as RecipientSO).model;
    assert(recipientType === 'MEMBER'); // 暂时report都是member
    const reportReceiver = await MemberSO.init(recipientId);
    const spaceId = reportReceiver.spaceId;
    // 为所有reminder的接收者，都进行一个通知
    // 这个notification在数据库层面仅有一个，但notification的recipient有多个
    const noticeSO = await NotificationSO.createToRecipients(
      {
        type: 'REMINDER',
        spaceId,
        reminderId: this._model.id,
      },
      recipients,
    );

    // 增加一次remind的计数
    await db.mongo.reminder.findOneAndUpdate(
      { id: this._model.id },
      {
        $inc: { count: 1 },
      },
    );

    return noticeSO;
  }

  async toEventsVO(): Promise<AgendaEventVO[]> {
    return this.getEventsVO();
  }

  toVO(): ReminderVO {
    return {
      id: this._model.id,
      name: this._model.name,
      description: this._model.description!,
      scheduler: this._model.scheduler,
      isActive: this._model.isActive,
    };
  }
}
