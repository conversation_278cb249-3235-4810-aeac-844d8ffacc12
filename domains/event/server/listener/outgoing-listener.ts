import { ServerError, errors } from '@bika/contents/config/server/error';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { RecordSO } from '@bika/domains/database/server/record-so';
import { FormSO } from '@bika/domains/form/server/form-so';
import { SpaceSO } from '@bika/domains/space/server/space-so';
import { MemberSO } from '@bika/domains/unit/server/member-so';
import { db } from '@bika/server-orm';
import { SpaceJoinInfo } from '@bika/types/space/vo';
import { isInCI } from 'sharelib/app-env';
import { OutgoingWebhookSO } from '../event/outgoing-webhook-so';

export class OutgoingListener {
  static async onRecordCreated(
    databaseSO: DatabaseSO,
    recordSOs: RecordSO[],
    options?: { memberSO?: MemberSO; formId?: string },
  ) {
    const locale = options?.memberSO?.locale;
    if (options?.formId) {
      const formVO = await (await FormSO.init(options.formId)).toVO({ locale });
      for (const recordSO of recordSOs) {
        OutgoingWebhookSO.emit(databaseSO.spaceId, {
          eventType: 'ON_FORM_SUBMITTED',
          form: formVO,
          record: recordSO.toVO({ locale }),
        });
      }
    } else {
      const databaseVO = await databaseSO.toVO({ locale });
      for (const recordSO of recordSOs) {
        OutgoingWebhookSO.emit(databaseSO.spaceId, {
          eventType: 'ON_RECORD_CREATED',
          database: databaseVO,
          record: await recordSO.toVO({ locale }),
        });
      }
    }
  }

  static async beforeMemberJoined(
    spaceId: string,
    userId: string,
    teamId: string,
    roleIds?: string[],
    joinInfo?: SpaceJoinInfo,
  ) {
    await OutgoingWebhookSO.syncEmit(spaceId, {
      eventType: 'BEFORE_MEMBER_JOINED',
      member: {
        spaceId,
        userId,
        teamId,
        roleIds,
        joinInfo,
      },
      onSuccess: (response) => {
        if (!isInCI()) {
          db.log.write({
            kind: 'SPACE_AUDIT_LOG',
            type: 'space.outgoing-webhook',
            userid: 'system',
            spaceid: spaceId,
            data: JSON.stringify({
              type: 'before_member_joined',
              request: {
                userId,
                teamId,
                roleIds,
                joinInfo,
              },
              response: response.data,
            }),
            createdat: new Date().toISOString(),
          });
        }
      },
      onError: async (response) => {
        if (!isInCI()) {
          db.log.write({
            kind: 'SPACE_AUDIT_LOG',
            type: 'space.outgoing-webhook',
            userid: 'system',
            spaceid: spaceId,
            data: JSON.stringify({
              type: 'before_member_joined',
              request: {
                userId,
                teamId,
                roleIds,
                joinInfo,
              },
              response: response.data,
            }),
            createdat: new Date().toISOString(),
          });
        }
        const spaceName = await SpaceSO.getNameById(spaceId);
        throw new ServerError(errors.unit.member_cannot_join_space, {
          spaceName,
        });
      },
    });
  }
}
