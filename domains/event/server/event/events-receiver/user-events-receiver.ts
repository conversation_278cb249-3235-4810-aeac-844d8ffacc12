import { NewUserBikaCoin } from '@bika/contents/config/server/store';
import { UserSO } from '@bika/domains/user/server/user-so';
import { getAppEnv } from 'sharelib/app-env';

export class UserEventsReceiver {
  async onUserCreated(user: UserSO, referralCode?: string) {
    // 开发环境下，奖励一点Credit
    const newUserBikaCoin = getAppEnv() !== 'PRODUCTION' ? NewUserBikaCoin.DEVELOPMENT : NewUserBikaCoin.PRODUCTION;
    if (newUserBikaCoin > 0) {
      const coinAccount = await user.coins.getAccount();
      await coinAccount.earn(100, 'CREDIT', {
        reason: 'gm',
        description: '开发环境下，新用户注册奖励100Credit',
      });
    }

    // 如果有推荐码，发放新用户奖励
    if (referralCode) {
      await user.acceptReferral(referralCode);
    }

    if (user.email && getAppEnv() === 'PRODUCTION') {
      try {
        // 临时使用 后面会改成调用sdk
        await fetch(
          'https://staging.bika.ai/api/openapi/apitable/fusion/v1/datasheets/datLjLpCLTHgjSXoYh4Fcuum/records',
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: 'Bearer bkteA3Geu2C2LMli4iu8twVKDeWRitahyZG',
            },
            body: JSON.stringify({
              records: [
                {
                  fields: {
                    fldv3GIRFcX9ID4VQma9XM8X: user.email,
                    fldLSHhMrpADKQDvXhwsMKh9: user.name,
                    fldhuTfrJKXZJY9V5nzPHYvv: ['Subscribed'],
                    fldxnBC1xZE2bHpcgotFbiw8: new Date().toISOString(),
                  },
                },
              ],
              fieldKey: 'id',
            }),
          },
        );

        // const data = await res.json();

        // const database = await DatabaseSO.init('datLjLpCLTHgjSXoYh4Fcuum');
        // await database.createRecord(undefined, {
        //   fldv3GIRFcX9ID4VQma9XM8X: user.email,
        //   fldLSHhMrpADKQDvXhwsMKh9: user.name,
        //   fldhuTfrJKXZJY9V5nzPHYvv: ['Subscribed'],
        //   fldxnBC1xZE2bHpcgotFbiw8: new Date().toISOString(),
        // });
      } catch (e) {
        console.error(e);
        // 不阻塞
        // console.error(e);
      }
    }
  }
}
