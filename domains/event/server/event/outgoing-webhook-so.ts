import axios from 'axios';
import { generateNanoID } from 'basenext/utils/nano-id';
import { getAppEnv, isInCI } from 'sharelib/app-env';
import { db, $Enums, OutgoingWebhook as OutgoingWebhookPO } from '@bika/server-orm';
import { OutgoingWebhookBO, ServerEventTypesSchema, type ServerEventType } from '@bika/types/events/bo';
import { OutgoingWebhookEmitterDTO } from '@bika/types/events/dto';
import type { BaseOutgoingWebhookVO, OutgoingWebhookVO } from '@bika/types/events/vo';
import { iStringParse, type iString } from '@bika/types/i18n/bo';
// import { db } from '@bika/server-orm';
/**
 * 注册Outgoing Webhook，外部接受事件
 */
export class OutgoingWebhookSO {
  private _model: OutgoingWebhookPO;

  constructor(model: OutgoingWebhookPO) {
    this._model = model;
  }

  get id() {
    return this._model.id;
  }

  // 返回Model PO，不转换SO
  static async listPOByEventType(eventType: ServerEventType) {
    const pos = await db.prisma.outgoingWebhook.findMany({
      where: {
        eventType,
      },
    });
    return pos;
  }

  /**
   * 查找特定类型的webhook是否存在
   */
  static async hasOutgoingWebhook(
    eventType: ServerEventType,
    options?: {
      scope?: $Enums.OutgoingWebhookScope;
      spaceId?: string;
    },
  ): Promise<boolean> {
    const pos = await db.prisma.outgoingWebhook.findFirst({
      where: {
        eventType,
        ...(options?.scope ? { relationType: options.scope } : {}),
        ...(options?.spaceId ? { relationId: options.spaceId } : {}),
      },
    });
    return !!pos;
  }

  static async listPOByEventTypeAndCallbackURL(
    eventType: ServerEventType,
    scope: $Enums.OutgoingWebhookScope,
    callbackURL: string,
  ) {
    const pos = await db.prisma.outgoingWebhook.findMany({
      where: {
        eventType,
        relationType: scope,
        callbackURL,
      },
    });
    return pos;
  }

  public static async list(scope: $Enums.OutgoingWebhookScope, spaceId?: string) {
    const pos = await db.prisma.outgoingWebhook.findMany({
      where: {
        relationType: scope,
        relationId: spaceId,
      },
    });

    return pos.map((po) => new OutgoingWebhookSO(po));
  }

  public static async listByEventTypeAndSpaceId(eventType: ServerEventType, spaceId: string) {
    return db.prisma.outgoingWebhook.findMany({
      where: {
        eventType,
        relationType: 'SPACE',
        relationId: spaceId,
      },
    });
  }

  public static convertToVO(po: OutgoingWebhookPO, dto: OutgoingWebhookEmitterDTO): OutgoingWebhookVO {
    return {
      ...dto,
      id: po.id,
      name: iStringParse(po.name as iString),
      description: iStringParse(po.description as iString),
      callbackURL: po.callbackURL,
    };
  }

  public static async emit(spaceId: string, emitterDto: OutgoingWebhookEmitterDTO) {
    const pos = await this.listPOByEventType(emitterDto.eventType);
    const filterPOs = pos.filter((po) => {
      if (!po.callbackURL) return false;

      if (po.relationType === 'SPACE' && po.relationId === spaceId) {
        const bo = po.bo as OutgoingWebhookBO;
        if (bo.nodeId) {
          // 如果有nodeId，则需要判断是否符合条件
          switch (emitterDto.eventType) {
            case 'ON_NODE_CREATED':
              return emitterDto.node.id === bo.nodeId;
            case 'ON_RECORD_CREATED':
              return emitterDto.database.id === bo.nodeId;
            case 'ON_FORM_SUBMITTED': {
              return emitterDto.form.id === bo.nodeId;
            }
            default:
              return false;
          }
        }
        return true;
      }
      if (po.relationType === 'SITE_ADMIN') {
        return true;
      }
      return false;
    });

    const promises = filterPOs.map((po) => this.triggerWebhookCallback(po.callbackURL, emitterDto));

    await Promise.allSettled(promises);
  }

  /**
   * 同步发送事件, 同步发送事件，会阻塞，直到所有事件发送完毕
   * @param spaceId 空间ID
   * @param dto 事件DTO
   */
  public static async syncEmit(spaceId: string, dto: OutgoingWebhookEmitterDTO) {
    const pos = await this.listPOByEventType(dto.eventType);

    const allowEmit = (po: OutgoingWebhookPO) => {
      if (po.relationType === 'SPACE' && po.relationId === spaceId) {
        return true;
      }
      if (po.relationType === 'SITE_ADMIN' && (getAppEnv() === 'SELF-HOSTED' || isInCI())) {
        return true;
      }
      return false;
    };

    for (const po of pos) {
      if (!allowEmit(po)) {
        continue;
      }
      await this.triggerWebhookCallback(po.callbackURL, dto);
    }
  }

  /**
   * 这个只是Base 的，具体的事件类型需要在具体的VO中定义
   *
   * 一般是不用这个的，用emit发送事件
   *
   * @returns
   */
  public async toVO(): Promise<BaseOutgoingWebhookVO> {
    const eventType = ServerEventTypesSchema.parse(this._model.eventType);
    return Promise.resolve({
      id: this._model.id,
      name: iStringParse(this._model.name as iString),
      description: iStringParse(this._model.description as iString),
      callbackURL: this._model.callbackURL,
      eventType,
    });
  }

  public static async create(options: {
    bo: OutgoingWebhookBO;
    scope: $Enums.OutgoingWebhookScope;
    spaceId?: string;
    userId?: string;
  }) {
    const newPO = await db.prisma.outgoingWebhook.create({
      data: {
        id: generateNanoID('owh'),
        name: options.bo.name,
        description: options.bo.description,
        eventType: options.bo.eventType,
        callbackURL: options.bo.callbackURL,
        bo: options.bo,
        relationType: options.scope,
        relationId: options.spaceId,
        createdBy: options.userId,
      },
    });
    return new OutgoingWebhookSO(newPO);
  }

  public static async delete(id: string) {
    const res = await db.prisma.outgoingWebhook.delete({
      where: {
        id,
      },
    });
    if (!res) {
      throw new Error('OutgoingWebhook not found');
    }
    return true;
  }

  private static async triggerWebhookCallback(callbackURL: string, dto: OutgoingWebhookEmitterDTO) {
    try {
      const response = await axios.post(callbackURL, dto);
      if (response.status !== 200 && response.status !== 201) {
        await dto.onError?.(response);
      }
      await dto.onSuccess?.(response);
    } catch (error) {
      console.log('call outgoing webhook error', error);
      await dto.onError?.(error);
    }
  }
}
