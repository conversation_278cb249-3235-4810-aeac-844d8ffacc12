import { z } from 'zod';
import { publicProcedure, router } from '@bika/server-orm/trpc';
import * as SMSController from './sms-controller';

export const smsRouter = router({
  sendVerificationSms: publicProcedure
    .input(
      z.object({
        phone: z.string(),
      }),
    )
    .mutation(async (opts) => {
      const { input } = opts;
      return SMSController.sendSmsVerificationCode(input.phone);
    }),
});
