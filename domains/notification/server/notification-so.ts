import { generateNanoID } from 'basenext/utils/nano-id';
import { EmailSO } from '@bika/domains/email/server/email-so';
import { getDefaultLocale } from '@bika/domains/shared/server';
import { RecipientSO } from '@bika/domains/system/server/recipients-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { db, NotificationModel, RecipientModel } from '@bika/server-orm';
import { Locale } from '@bika/types/i18n/bo';
import { NotificationProperty } from '@bika/types/notification/bo';
import { NotificationType } from '@bika/types/notification/type';
import { BaseNotificationVO, NotificationVO } from '@bika/types/notification/vo';
import { Pagination, PaginationSchema } from '@bika/types/shared';
import { To } from '@bika/types/unit/bo';
import { NotificationHandlerFactory } from './handlers';
import { PushSO } from './push/push-so';

export class NotificationSO {
  private readonly _model: NotificationModel;

  private _cacheRecipients?: RecipientSO[];

  /**
   *
   * @param noticePO
   * @param initRecipients 可传可不传，惰式加载使用，外部异步调用
   */
  private constructor(noticePO: NotificationModel, initRecipients?: RecipientSO[]) {
    this._model = noticePO;
    this._cacheRecipients = initRecipients;
  }

  get id() {
    return this._model.id;
  }

  get model() {
    return this._model;
  }

  get type(): NotificationType {
    return this._model.type as NotificationType;
  }

  get property(): NotificationProperty {
    return this._model.property as NotificationProperty;
  }

  /**
   * 异步惰式获取接收者
   * @returns
   */
  async getRecipients(useCache: boolean = true) {
    if (this._cacheRecipients && useCache) {
      return this._cacheRecipients;
    }
    this._cacheRecipients = await RecipientSO.find('NOTIFICATION', this._model.id);
    return this._cacheRecipients;
  }

  async toVO(userId?: string): Promise<NotificationVO | undefined> {
    let locale: Locale | undefined;
    // 指定用户时，返回是否已读
    let read: boolean | undefined;
    if (userId) {
      const recipients = await this.getRecipients();
      read = recipients.find((r) => r.recipientId === userId)?.state.read;
      const userSO = await UserSO.findById(userId);
      locale = userSO?.locale;
    }
    const { id, property, createdAt } = this.model;
    // 未完成的VO
    const incompleteVO: BaseNotificationVO = {
      id,
      type: this.type,
      property,
      title: '',
      content: '',
      read,
      createdAt: createdAt.toISOString(),
    };
    // 根据消息类型，填充VO
    const handler = NotificationHandlerFactory.getHandler(this.type);
    try {
      return handler.fillInVO(incompleteVO, locale);
    } catch (_e) {
      // 消息关联的数据，如Mission，可以被删除了，返回undefined
      return undefined;
    }
  }

  static async countUserNotifications(userId: string, read: boolean = false): Promise<number> {
    return db.mongo.recipient('NOTIFICATION').countDocuments({
      relationType: 'NOTIFICATION',
      recipientType: 'USER',
      recipientId: userId,
      state: {
        read,
      },
    });
  }

  /**
   * 分页查询用户的通知
   */
  static async find(
    userId: string,
    opts?: {
      hasRead?: boolean;
      pagination?: Pagination;
    },
  ): Promise<NotificationSO[]> {
    const { hasRead, pagination } = opts || {};
    const { pageNo, pageSize } = PaginationSchema.parse(pagination ?? {});
    const usersRecipients = await db.mongo.recipient('NOTIFICATION').find({
      relationType: 'NOTIFICATION',
      recipientType: 'USER',
      recipientId: userId,
      ...(hasRead === undefined ? {} : { 'state.read': hasRead }),
    });
    if (usersRecipients.length === 0) {
      return [];
    }
    const usersRecipientMap = usersRecipients.reduce((acc, cur) => {
      acc.set(cur.relationId, RecipientSO.initWithModel(cur));
      return acc;
    }, new Map<string, RecipientSO>());

    const userNotices = [...usersRecipientMap.keys()];
    const notices = await db.mongo.notification
      .find({
        id: { $in: userNotices },
      })
      .skip(pageNo > 0 ? (pageNo - 1) * pageSize : 0)
      .limit(pageSize)
      .sort({ _id: -1 });
    return notices.map((notice) => new NotificationSO(notice, [usersRecipientMap.get(notice.id)!]));
  }

  private static buildModel(props: NotificationProperty): NotificationModel {
    return {
      id: generateNanoID('not'),
      type: props.type,
      // title,
      // content,
      property: props,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  /**
   * 创建Notification给用户
   *
   * @param props
   * @param to 这里要对recipient进行转换，比如写了member，但要转成user，notification基于user
   * @returns
   */
  static async create(props: NotificationProperty, to: To) {
    const notificationModel = this.buildModel(props);
    const toUser = await RecipientSO.convertToUser(to);
    const recipientModel = RecipientSO.buildModel('NOTIFICATION', notificationModel.id, 'USER', toUser.userId, {
      read: false,
    });
    return this.doCreateNotification(notificationModel, [recipientModel]);
  }

  /**
   * 创建通知
   * 向Recipients发送，通常被应用层调用，如Reminder等
   * 创建单个Notification，多个Recipient共享一个
   * 通知通常会有多个渠道，如邮件、Slack、企微、Chrome、APP内等，能推就推
   */
  static async createToRecipients(
    props: NotificationProperty,
    anotherRecipients: RecipientSO[],
  ): Promise<NotificationSO> {
    // 创建我们自己的通知
    const notificationModel = this.buildModel(props);
    // 创建 Recipients
    const recipientModels: RecipientModel[] = [];
    for (const recipient of anotherRecipients) {
      // 必须是其它模块传递过来的
      if (recipient.relationType === 'NOTIFICATION') {
        throw new Error('NOTIFICATION relationType is not allowed');
      }
      const userId = await recipient.convertToUserId();
      if (userId) {
        recipientModels.push(
          RecipientSO.buildModel('NOTIFICATION', notificationModel.id, 'USER', userId, {
            read: false,
          }),
        );
      }
    }

    return this.doCreateNotification(notificationModel, recipientModels);
  }

  private static async doCreateNotification(
    notificationModel: NotificationModel,
    recipientModels: RecipientModel[],
  ): Promise<NotificationSO> {
    await db.mongo.recipient('NOTIFICATION').init();
    await db.mongo.transaction(async (session) => {
      await db.mongo.notification.create([notificationModel], { session });
      await db.mongo.recipient('NOTIFICATION').insertMany(recipientModels, { session });
    });
    // build notificationSO
    const recipients: RecipientSO[] = recipientModels.map((recipient) => RecipientSO.initWithModel(recipient));
    const notificationSO = new NotificationSO(notificationModel, recipients);
    await notificationSO.doNotice();
    return notificationSO;
  }

  /**
   * 执行一次notice触发，通常在创建的时候才触发的，之后不会重复触发了(还没遇到该场景)
   */
  async doNotice() {
    const recipients = await this.getRecipients();
    for (const recipient of recipients) {
      let email: string | null = null;
      let locale = getDefaultLocale();
      let userSO;
      const userId = await recipient.convertToUserId();
      const notificationVO = await this.toVO(userId);
      if (!notificationVO) {
        continue;
      }
      if (userId) {
        userSO = await UserSO.findById(userId);
        if (userSO) {
          email = userSO.email;
          locale = userSO.locale;
          // 集成通知推送 Integration Notification 推送
          PushSO.sendPushNotifications(userSO, notificationVO);
        }
      }

      // 邮件通知
      if (email && userSO?.allowPushNotification('email')) {
        const handler = NotificationHandlerFactory.getHandler(this.type);
        const mailBody = handler.renderMailTemplate(locale, notificationVO);
        const senderName = notificationVO.type !== 'SYSTEM' ? notificationVO!.space?.name : undefined; // undefined sender name，就会用上Bika.ai的了

        if (mailBody) {
          EmailSO.send(
            {
              type: 'SERVICE',
              subject: mailBody.title,
              body: mailBody.content,
              to: email,
              senderName,
            },
            { relationId: this.id },
          );
        }
      }
    }
  }

  static async markAllAsRead(userId: string): Promise<number> {
    // 找到user的notification，然后read它
    const match = await db.mongo.recipient('NOTIFICATION').updateMany(
      {
        recipientType: 'USER',
        recipientId: userId,
        state: { read: false },
      },
      {
        state: { read: true },
      },
    );
    return match.matchedCount;
  }

  /**
   * 标记已读
   * @param notificationIds
   */
  static async markAsRead(userId: string, notificationIds: string[]): Promise<number> {
    const match = await db.mongo.recipient('NOTIFICATION').updateMany(
      {
        recipientType: 'USER',
        recipientId: userId,
        relationType: 'NOTIFICATION',
        relationId: { $in: notificationIds },
        state: { read: false },
      },
      { state: { read: true } },
    );
    return match.matchedCount;
  }
}
