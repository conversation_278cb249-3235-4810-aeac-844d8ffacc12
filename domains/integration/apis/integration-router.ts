import { z } from 'zod';
import { UserSO } from '@bika/domains/user/server/user-so';
import { router, protectedProcedure } from '@bika/server-orm/trpc';
import { OutgoingWebhookVOSchema } from '@bika/types/events/vo';
import * as T from '@bika/types/integration/dto';
import { IntegrationVOSchema } from '@bika/types/integration/vo';

export const integrationRouter = router({
  /**
   * 返回integrations vo和outgoing webhooks vos
   */
  list: protectedProcedure
    .input(T.IntegrationListDTOSchema)
    .output(
      z.object({
        integrations: z.array(IntegrationVOSchema),
        outgoingWebhooks: z.array(OutgoingWebhookVOSchema),
      }),
    )
    .query(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      const { spaceId, type } = input;
      const member = await user.getMember(spaceId);
      const space = await member.getSpace();
      const integrations = await space.getIntegrations(type);
      return {
        integrations: integrations.map((integration) => integration.toVO()),
        outgoingWebhooks: [],
      };
    }),
  info: protectedProcedure.input(T.IntegrationInfoDTOSchema).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    const { spaceId, id } = input;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    // 管理员权限判断
    await space.getAdminRoleAclSO().authorize(member, 'readIntegration');
    const integration = await space.getIntegration(id);
    return integration.toVO();
  }),
  create: protectedProcedure.input(T.IntegrationCreateDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    const { spaceId, data } = input;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    // 管理员权限判断
    await space.getAdminRoleAclSO().authorize(member, 'createIntegration');
    // 用量检查
    const entitlement = await space.getEntitlement();
    await entitlement.checkUsageExceed({ feature: 'SPACE_INTEGRATIONS', value: 1 });
    // 可以创建了
    const integrationSO = await space.createIntegration(user.id, data);
    return integrationSO.toVO();
  }),
  update: protectedProcedure.input(T.IntegrationUpdateDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    const { spaceId, id, data } = input;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    // 管理员权限判断
    await space.getAdminRoleAclSO().authorize(member, 'updateIntegration');
    const integration = await space.getIntegration(id);
    const updatedIntegration = await integration.update(user.id, data);
    return updatedIntegration.toVO();
  }),
  delete: protectedProcedure.input(T.IntegrationDeleteDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    const { spaceId, id } = input;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    // 管理员权限判断
    await space.getAdminRoleAclSO().authorize(member, 'deleteIntegration');
    const integration = await space.getIntegration(id);
    await integration.delete();
  }),
});
