import { AISO } from '@bika/domains/ai/server/ai-so';
import { PresetLanguageAIModelDef } from '@bika/types/ai/bo';
import { OpenAIIntegration } from '@bika/types/integration/bo';
import { IntegrationActionParams, IntegrationActionResult, SpaceIntegrationHandler } from './base-handler';

export interface OpenAIHandlerActionParams extends IntegrationActionParams {
  prompt: string;
  model?: string;
}
export class DeepSeekHandler extends SpaceIntegrationHandler<OpenAIHandlerActionParams, IntegrationActionResult> {
  getProperty(): OpenAIIntegration {
    return this.model.bo as OpenAIIntegration;
  }

  get defaultModel(): PresetLanguageAIModelDef {
    return 'deepseek-reasoner' as PresetLanguageAIModelDef;
  }

  get apiKey(): string {
    return this.getProperty().apiKey;
  }

  get baseUrl(): string {
    return this.getProperty().baseUrl || 'https://api.deepseek.com/v1';
  }

  get organizationId(): string | undefined {
    return this.getProperty().organizationId;
  }

  /**
   * 执行OpenAI的API调用 提示词
   * @param params
   * @returns
   */
  async action(params: OpenAIHandlerActionParams): Promise<IntegrationActionResult> {
    const { prompt, model } = params;
    const property = this.getProperty();
    const { apiKey, baseUrl } = property;
    const response = await AISO.invokeByOpenAI(prompt, {
      model: (model as PresetLanguageAIModelDef) || 'deepseek/deepseek-v3',
      apiKey,
      baseUrl: baseUrl || 'https://api.deepseek.com/v1',
    });
    return { response };
  }
}
