import { generateNanoID } from 'basenext/utils/nano-id';
import { SpaceSO } from '@bika/domains/space/server/space-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { db, IntegrationRelationType } from '@bika/server-orm';
import {
  Integration,
  IntegrationType,
  MySQL,
  PostgreSQL,
  SpaceIntegration,
  UserIntegration,
} from '@bika/types/integration/bo';
import { IntegrationVO } from '@bika/types/integration/vo';
import { UserLinkType } from '@bika/types/user/bo';
import { IntegrationHandler } from './handlers/base-handler';
import { determineIntegrationHandler } from './handlers/handler-factory';
import { IntegrationResourceAdapter } from './integration-recource-adapter';
import { IntegrationModel, IntegrationResourceSO, SpaceTypeIntegration, UserTypeIntegration } from './types';

/**
 * integration domain service object.
 */
export class IntegrationSO {
  private _model: IntegrationModel;

  private _handler: IntegrationHandler | undefined;

  // public getProperty<T extends IntegrationProperty>(): T {
  //   return this._model.property as unknown as T;
  // }

  public getBO<T extends Integration>(): T {
    return this._model.bo as unknown as T;
  }

  private constructor(model: IntegrationModel) {
    this._model = model;
  }

  public get model() {
    return this._model;
  }

  toVO(): IntegrationVO {
    return {
      id: this.id,
      verified: this.model.verified,
      bo: this.getBO(),
    };
  }

  public get id() {
    return this.model.id;
  }

  public get name() {
    return this.model.name;
  }

  get type(): IntegrationType {
    return this.model.type as IntegrationType;
  }

  get relationType(): IntegrationRelationType {
    return this.model.relationType;
  }

  get relationId(): string {
    return this.model.relationId;
  }

  get handler(): IntegrationHandler {
    if (!this._handler) {
      this._handler = determineIntegrationHandler<IntegrationHandler>(this.model);
    }
    return this._handler;
  }

  getHandler<T extends IntegrationHandler>(): T {
    return determineIntegrationHandler<T>(this.model);
  }

  /**
   * init with id
   * @param id integration id
   */
  public static async init(id: string) {
    const integration = await this.initMaybeNull(id);
    if (!integration) {
      throw new Error('Integration not found');
    }
    return integration;
  }

  static async initMaybeNull(integrationId: string): Promise<IntegrationSO | null> {
    const integrationPO = await db.prisma.integration.findUnique({
      where: { id: integrationId },
    });
    return integrationPO && this.initWithModel(integrationPO);
  }

  public static initWithModel(model: IntegrationModel) {
    return new IntegrationSO(model);
  }

  public async getOwner(): Promise<UserSO | SpaceSO> {
    if (this.relationType === UserTypeIntegration) {
      return UserSO.init(this.relationId);
    }
    if (this.relationType === SpaceTypeIntegration) {
      return SpaceSO.init(this.relationId);
    }
    throw new Error(`Unsupported relation type: ${this.relationType}`);
  }

  async delete(): Promise<void> {
    await db.prisma.integration.delete({ where: { id: this.id } });
  }

  /**
   * 找到对应的集成
   */
  static async findByRelationIdAndRelationType(
    relationId: string,
    relationType: IntegrationRelationType,
    type?: IntegrationType,
  ): Promise<IntegrationSO[]> {
    const models = await db.prisma.integration.findMany({
      where: {
        relationId,
        relationType,
        type: type || undefined,
      },
    });
    return models.map((model) => IntegrationSO.initWithModel(model));
  }

  static async findByRelationIdAndType(relationId: string, type: IntegrationType): Promise<IntegrationModel[]> {
    return db.prisma.integration.findMany({
      where: {
        relationId,
        type,
      },
    });
  }

  static async createForUserLinkType(userId: string, type: UserLinkType): Promise<IntegrationSO | undefined> {
    switch (type) {
      case 'GOOGLE':
        return this.createForUser(userId, {
          type: 'GOOGLE',
          name: 'google 集成',
          description: 'google 集成',
        });
      case 'GITHUB':
        return this.createForUser(userId, {
          type: 'GITHUB',
          name: 'github 集成',
          description: 'github 集成',
        });
      case 'WEIXIN':
        return this.createForUser(userId, {
          type: 'WECHAT',
          name: 'wechat 集成',
          description: 'wechat 集成',
        });
      // no default
      default:
        return undefined;
    }
  }

  static async createForUser(userId: string, integration: UserIntegration): Promise<IntegrationSO> {
    return this.create(userId, integration, UserTypeIntegration, userId);
  }

  static async createForSpace(userId: string, spaceId: string, integration: SpaceIntegration): Promise<IntegrationSO> {
    return this.create(userId, integration, SpaceTypeIntegration, spaceId);
  }

  private static async create(
    userId: string,
    bo: Integration,
    relationType: IntegrationRelationType,
    relationId: string,
  ): Promise<IntegrationSO> {
    const integrationModel = await db.prisma.integration.create({
      data: {
        id: generateNanoID('itg'),
        name: bo.name,
        description: bo.description as object | undefined,
        type: bo.type,
        bo,
        relationType,
        relationId,
        createdBy: userId,
        updatedBy: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });
    return this.initWithModel(integrationModel);
  }

  async update(userId: string, bo: Integration): Promise<IntegrationSO> {
    const model = await db.prisma.integration.update({
      where: { id: this.id },
      data: {
        name: bo.name,
        description: bo.description as object | undefined,
        type: bo.type,
        bo,
        relationType: this.relationType,
        relationId: this.relationId,
        updatedBy: userId,
        updatedAt: new Date(),
      },
    });
    return new IntegrationSO(model);
  }

  static async deleteByUserLinkType(userId: string, type: UserLinkType): Promise<IntegrationSO | void> {
    switch (type) {
      case 'GOOGLE':
        return this.deleteByUserTypeAndyRelationId(userId, 'GOOGLE');
      case 'GITHUB':
        return this.deleteByUserTypeAndyRelationId(userId, 'GITHUB');
      case 'WEIXIN':
        return this.deleteByUserTypeAndyRelationId(userId, 'WECHAT');
      // no default
    }
  }

  static async deleteByUserTypeAndyRelationId(relationId: string, type: IntegrationType): Promise<void> {
    await db.prisma.integration.deleteMany({
      where: {
        type,
        relationType: UserTypeIntegration,
        relationId,
      },
    });
  }

  // static getEmailAccountByUserName(intgSOs: IntegrationSO[], username: string): IntegrationSO | undefined {
  //   return intgSOs.find((so) => (so.model.property as
  //     unknown as SMTPEmailAccount).username === username);
  // }

  toResourceSO<T extends IntegrationResourceSO>(): T {
    const adapter = new IntegrationResourceAdapter(this);
    const integrationType = this.type as IntegrationType;
    switch (integrationType) {
      case PostgreSQL.value:
        return adapter.toPostgreAccountSO() as T;
      case MySQL.value:
        return adapter.toMysqlAccountSO() as T;
      default:
        throw new Error(`Integration type ${integrationType} not supported`);
    }
  }

  async setVerified(verified: boolean): Promise<void> {
    await db.prisma.integration.updateMany({
      where: {
        id: this.id,
      },
      data: {
        verified,
      },
    });
  }
}
