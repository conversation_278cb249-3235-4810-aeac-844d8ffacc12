import { test, expect, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { MockContext } from '@bika/domains/__tests__/mock';
import { useAIChatContexts, useAIChatCache, useAIChatData } from '../client/chat/hooks/use-ai-chat-cache';
import { getGlobalIndexedDB } from '@bika/types/website/context';
import type { AIChatContextVO } from '@bika/types/ai/vo';

// Mock the global IndexedDB
const mockDB = {
  init: vi.fn().mockResolvedValue(undefined),
  get: vi.fn(),
  put: vi.fn().mockResolvedValue(undefined),
  delete: vi.fn().mockResolvedValue(undefined),
};

vi.mock('@bika/types/website/context', () => ({
  getGlobalIndexedDB: () => mockDB,
}));

// Mock the space context
const mockSpaceContext = {
  data: {
    id: 'test-space-id',
  },
};

vi.mock('@bika/types/space/context', () => ({
  useSpaceContext: () => mockSpaceContext,
}));

// Mock ahooks
vi.mock('ahooks', () => ({
  useLocalStorageState: (key: string, options: any) => {
    const [state, setState] = React.useState(options.defaultValue);
    return [state, setState];
  },
}));

describe('AI Chat Cache with IndexedDB', () => {
  const mockSelector = {
    type: 'agent' as const,
    agent: {
      type: 'node' as const,
      nodeId: 'test-node-id',
    },
  };

  const mockContexts: AIChatContextVO[] = [
    {
      type: 'attachment',
      attachment: {
        id: 'test-attachment-1',
        name: 'test.pdf',
        path: 'test/path.pdf',
        size: 1024,
        mimeType: 'application/pdf',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    },
    {
      type: 'node',
      node: {
        id: 'test-node-1',
        name: 'Test Node',
        resourceType: 'DOCUMENT',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  test('useAIChatContexts should load contexts from IndexedDB', async () => {
    // Mock IndexedDB to return stored contexts
    mockDB.get.mockResolvedValue({
      id: 'ai-chat-cache-test-space-id-agent-node:test-node-id',
      storageKey: 'ai-chat-cache-test-space-id-agent-node',
      chatId: 'test-node-id',
      contexts: mockContexts,
      updatedAt: new Date(),
    });

    const { result } = renderHook(() => useAIChatContexts(mockSelector));

    // Initially loading
    expect(result.current.isLoading).toBe(true);
    expect(result.current.contexts).toEqual([]);

    // Wait for the effect to complete
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    expect(mockDB.init).toHaveBeenCalled();
    expect(mockDB.get).toHaveBeenCalledWith(
      'AI_CHAT_CONTEXTS',
      'ai-chat-cache-test-space-id-agent-node:test-node-id'
    );
    expect(result.current.isLoading).toBe(false);
    expect(result.current.contexts).toEqual(mockContexts);
  });

  test('useAIChatContexts should save contexts to IndexedDB', async () => {
    mockDB.get.mockResolvedValue(null); // No existing data

    const { result } = renderHook(() => useAIChatContexts(mockSelector));

    // Wait for initial load
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    // Set new contexts
    await act(async () => {
      await result.current.setContexts(mockContexts);
    });

    expect(mockDB.put).toHaveBeenCalledWith('AI_CHAT_CONTEXTS', {
      id: 'ai-chat-cache-test-space-id-agent-node:test-node-id',
      storageKey: 'ai-chat-cache-test-space-id-agent-node',
      chatId: 'test-node-id',
      contexts: mockContexts,
      updatedAt: expect.any(Date),
    });
  });

  test('useAIChatContexts should handle functional updates', async () => {
    mockDB.get.mockResolvedValue({
      contexts: [mockContexts[0]], // Start with one context
    });

    const { result } = renderHook(() => useAIChatContexts(mockSelector));

    // Wait for initial load
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    // Add another context using functional update
    await act(async () => {
      await result.current.setContexts(prev => [...prev, mockContexts[1]]);
    });

    expect(mockDB.put).toHaveBeenCalledWith('AI_CHAT_CONTEXTS', {
      id: 'ai-chat-cache-test-space-id-agent-node:test-node-id',
      storageKey: 'ai-chat-cache-test-space-id-agent-node',
      chatId: 'test-node-id',
      contexts: mockContexts, // Should contain both contexts
      updatedAt: expect.any(Date),
    });
  });

  test('useAIChatData should integrate contexts from IndexedDB', async () => {
    mockDB.get.mockResolvedValue({
      contexts: mockContexts,
    });

    const { result } = renderHook(() => useAIChatData(mockSelector));

    // Wait for contexts to load
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    expect(result.current.data.contexts).toEqual(mockContexts);
    expect(result.current.api.setContexts).toBeDefined();
    expect(result.current.api.setAttachments).toBeDefined(); // Backward compatibility
  });

  test('should create correct composite key format', async () => {
    const { result } = renderHook(() => useAIChatContexts(mockSelector));

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    expect(mockDB.get).toHaveBeenCalledWith(
      'AI_CHAT_CONTEXTS',
      'ai-chat-cache-test-space-id-agent-node:test-node-id'
    );
  });

  test('should handle different talk types', async () => {
    const expertSelector = {
      type: 'agent' as const,
      agent: {
        type: 'expert' as const,
        expertKey: 'test-expert-key',
      },
    };

    const { result } = renderHook(() => useAIChatContexts(expertSelector));

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    expect(mockDB.get).toHaveBeenCalledWith(
      'AI_CHAT_CONTEXTS',
      'ai-chat-cache-test-space-id-agent-expert:test-expert-key'
    );
  });

  test('should handle IndexedDB errors gracefully', async () => {
    mockDB.get.mockRejectedValue(new Error('IndexedDB error'));
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    const { result } = renderHook(() => useAIChatContexts(mockSelector));

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    expect(consoleSpy).toHaveBeenCalledWith(
      'Failed to load chat contexts from IndexedDB:',
      expect.any(Error)
    );
    expect(result.current.isLoading).toBe(false);
    expect(result.current.contexts).toEqual([]);

    consoleSpy.mockRestore();
  });
});
