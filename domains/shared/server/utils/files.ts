import fs from 'node:fs';
import path from 'node:path';
import os from 'os';
import axios from 'axios';
import mime from 'mime-types';
import { generateNanoID } from 'basenext/utils/nano-id';

/**
 * 下载成blob
 * @param url 下载地址
 * @param ext 文件后缀
 * @returns Blob 二进制
 */
export async function httpGetObjectBlob(url: string, ext: string): Promise<Blob> {
  const getResponse = await axios({
    url,
    method: 'GET',
    responseType: 'arraybuffer', // blob只能用于浏览器，这里arraybuffer用于nodejs
  });
  const blob = new Blob([getResponse.data], { type: mime.lookup(ext) as string });
  const filePath = path.join(os.tmpdir(), 'blobtest.m4a');
  fs.writeFileSync(filePath, getResponse.data, 'binary');
  return blob;
}

/**
 * 资源地址下载到本地 /tmp 目录里
 *
 * @param url
 */
export async function downloadUrlToTmpFile(url: string): Promise<string> {
  const response = await axios({
    url,
    method: 'GET',
    responseType: 'stream',
  });

  const contentType = response.headers['content-type'];
  if (!contentType) {
    throw new Error('Content type not found');
  }

  // Determine the file extension based on the content type
  const extension = mime.extension(contentType);
  if (!extension) {
    throw new Error('Unsupported content type');
  }
  // Generate a unique file name
  const fileName = `downloaded.${extension}`;
  const dirPath = path.join(os.tmpdir(), generateNanoID(''));
  const localFilePath = path.join(dirPath, fileName);
  await fs.promises.mkdir(dirPath, { recursive: true });

  const writer = fs.createWriteStream(localFilePath);

  response.data.pipe(writer);

  return new Promise((resolve, reject) => {
    writer.on('finish', () => resolve(localFilePath));
    writer.on('error', reject);
  });
}

/**
 * Buffer下载到本地
 */
export async function bufferToFile(buffer: Buffer, ext: string): Promise<string> {
  const dirPath = path.join(os.tmpdir(), generateNanoID(''));
  const localFilePath = path.join(dirPath, `downloaded.${ext}`);
  await fs.promises.mkdir(dirPath, { recursive: true });
  fs.writeFileSync(localFilePath, new Uint8Array(buffer));
  return localFilePath;
}
