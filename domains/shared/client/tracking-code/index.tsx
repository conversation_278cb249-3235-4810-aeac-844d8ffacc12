'use client';

/**
 * 客户端、前端第三方集成，如tracking、analytics等
 */
import { GoogleTagManager, GoogleAnalytics } from '@next/third-parties/google';
// import { Analytics } from '@vercel/analytics/react';
// import { SpeedInsights } from '@vercel/speed-insights/next';
// import PlausibleProvider from 'next-plausible';
import type { AppEnv } from 'sharelib/app-env';
// import { MicrosoftClarity } from './clarity';

export { FirebaseCloudMessaging } from './firebase-cloud-messaging';

// export { useRecordVoice } from './record-voice';

export function TrackingCode(props: { appEnv: AppEnv }) {
  const GOOGLE_ANALYTICS_ID = props.appEnv === 'PRODUCTION' ? 'G-P455LDN1NC' : 'G-52ETNWPGXV';
  const GOOGLE_TAG_MANAGER_ID = 'GTM-MQWHTL4H';
  return (
    <>
      <GoogleTagManager gtmId={GOOGLE_TAG_MANAGER_ID} />
      <GoogleAnalytics gaId={GOOGLE_ANALYTICS_ID} />
      {/* <PlausibleProvider domain={PLAUSIBLE_ID} /> */}
      {/* <SpeedInsights /> */}
      {/* <Analytics /> */}
      {/* <MicrosoftClarity /> */}
    </>
  );
}
