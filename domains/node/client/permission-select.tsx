// import type { PopperPlacementType } from '@mui/base/Popper';
import type { SxProps } from '@mui/joy/styles/types';
import type { Placement as PopperPlacementType } from '@popperjs/core';
import _ from 'lodash';
import type React from 'react';
import { useLocale } from '@bika/contents/i18n/context';
import { DoubleSelect } from '@bika/domains/shared/client/components/double-select';
import { type AccessPrivilege, AccessPrivileges } from '@bika/types/permission/bo';
import { Typography } from '@bika/ui/texts';

const { compact } = _;

interface IPermissionSelectProps {
  onChangePrivilege: (value: AccessPrivilege) => void;
  onRemovePrivileage: () => void;
  currentPrivilege: AccessPrivilege;
  disabledPrivilege?: AccessPrivilege[];
  hideRemoveButton?: boolean;
  listboxPlacement?: PopperPlacementType; // 控制弹出框位置
  slotProps?: {
    trigger?: {
      sx: SxProps;
    };
  };
}

type PermissionSelectedValue = AccessPrivilege | 'REMOVE';

/**
 * 权限选择器UI控件
 *
 * @param param0
 * @returns
 */
export const PermissionSelect: React.FC<IPermissionSelectProps> = ({
  onChangePrivilege,
  onRemovePrivileage,
  currentPrivilege,
  disabledPrivilege = [],
  slotProps,
  hideRemoveButton,
  listboxPlacement = 'bottom-end',
}) => {
  const disabledSelect = AccessPrivileges.length === disabledPrivilege.length;
  const { t } = useLocale();

  return (
    <DoubleSelect<PermissionSelectedValue>
      defaultValue={currentPrivilege}
      disabled={disabledSelect}
      options={compact([
        [
          {
            label: t.node.permission.full_access,
            value: 'FULL_ACCESS',
            desc: t.node.permission.full_access_desc,
            disabled: disabledPrivilege.includes('FULL_ACCESS'),
          },
          {
            label: t.node.permission.can_edit,
            value: 'CAN_EDIT',
            desc: t.node.permission.can_edit_desc,
            disabled: disabledPrivilege.includes('CAN_EDIT'),
          },
          {
            label: t.node.permission.can_edit_content,
            value: 'CAN_EDIT_CONTENT',
            desc: t.node.permission.can_edit_content_desc,
            disabled: disabledPrivilege.includes('CAN_EDIT_CONTENT'),
          },
          {
            label: t.node.permission.can_view,
            value: 'CAN_VIEW',
            desc: t.node.permission.can_view_desc,
            disabled: disabledPrivilege.includes('CAN_VIEW'),
          },
          {
            label: t.node.permission.no_access,
            value: 'NO_ACCESS',
            desc: t.node.permission.no_access_desc,
            disabled: disabledPrivilege.includes('NO_ACCESS'),
          },
        ],
        !hideRemoveButton && [
          {
            label: t.node.permission.remove,
            value: 'REMOVE',
            disabled: false,
            render: () => <Typography sx={{ color: 'var(--status-danger)' }}>{t.node.permission.remove}</Typography>,
            style: { height: '40px', color: 'var(--status-danger) !important' },
          },
        ],
      ])}
      onChange={(value: PermissionSelectedValue) => {
        if (value === 'REMOVE') {
          onRemovePrivileage();
        } else {
          onChangePrivilege(value);
        }
      }}
      listboxPlacement={listboxPlacement}
      slotSx={{
        button: {
          sx: {
            border: 'none',
          },
        },
        trigger: {
          sx: {
            border: 'none !important',
            backgroundColor: 'unset !important',
            boxShadow: 'none',
            color: 'var(--text-secondary)',

            ...slotProps?.trigger?.sx,
          },
        },
        listbox: {
          sx: {
            maxWidth: '400px !important',
            '&.Mui-selected': {
              backgroundColor: 'unset !important',
              '&:hover': {
                backgroundColor:
                  'var(--variant-plainHoverBg, var(--joy-palette-neutral-plainHoverBg, var(--joy-palette-neutral-100, #F0F4F8))) !important',
              },
            },
          },
        },
      }}
    ></DoubleSelect>
  );
};
