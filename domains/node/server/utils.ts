/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-param-reassign */
import _ from 'lodash';
import { generateNanoID } from 'basenext/utils/nano-id';
import { AttachmentSO } from '@bika/domains/attachment/server/attachment-so';
import { IRelationIdOpts } from '@bika/domains/node/server/types';
import { isArray } from '@bika/domains/shared/shared';
import { TemplateIdPathStore } from '@bika/domains/template/server/template-id-path-store';
import { Bikafile } from '@bika/server-orm/bikafile';
import { Action, Automation, FindRecordsAction } from '@bika/types/automation/bo';
import { Dashboard } from '@bika/types/dashboard/bo';
import { Database, DatabaseField, AttachmentCellData } from '@bika/types/database/bo';
import {
  CONST_PREFIX_VIEW,
  CONST_PREFIX_FIELD,
  CONST_PREFIX_RECORD,
  CONST_PREFIX_ACTION,
  CONST_PREFIX_TRIGGER,
  CONST_PREFIX_ATTACHMENT,
  CONST_PREFIX_OPTION,
  CONST_PREFIX_WIDGET,
} from '@bika/types/database/vo';
import { FormBO } from '@bika/types/form/bo';
import { Folder, NodeResource } from '@bika/types/node/bo';
import { NodeTreeVO } from '@bika/types/node/vo';
import { AvatarLogo, AttachmentAvatar } from '@bika/types/system';
import { CustomTemplate } from '@bika/types/template/bo';
import { NodeResourceAdapter } from './node-resource-adapter';
import { SpaceAttachmentSO } from '../../space/server/space-attachment-so';
import { UnitFactory } from '../../unit/server/unit-factory';

export const canReadNode = (node: NodeTreeVO) => {
  const isRootNode = node.type === 'ROOT';
  if (isRootNode) {
    // 如果第二层节点存在某一个<可读>以上, 让跟节点临时可读, 但返回依然是不可读的
    const hasReadableChild = node.children?.some(
      (child) => child.permission && (child.permission.abilities.readNode || false),
    );
    if (hasReadableChild) {
      return true;
    }
  }
  if (node.permission) {
    return node.permission.abilities.readNode || false;
  }
  // 默认不走权限
  return true;
};

export function addChildrenIfCanRead(nodes: NodeTreeVO[], node: NodeTreeVO) {
  if (canReadNode(node)) {
    nodes.push(node);
  }
}

const TemplateIdToIdRef: { [key: string]: string } = {
  templateId: 'id',
  foreignDatabaseTemplateId: 'foreignDatabaseId',
  brotherFieldTemplateId: 'brotherFieldId',
  relatedLinkFieldTemplateId: 'relatedLinkFieldId',
  lookupTargetFieldTemplateId: 'lookupTargetFieldId',
  expressionTemplate: 'expression',
  fieldTemplateId: 'fieldId',
  viewTemplateId: 'viewId',
  formTemplateId: 'formId',
  formTemplateIds: 'formIds',
  databaseTemplateId: 'databaseId',
  dashboardTemplateId: 'dashboardId',
  widgetTemplateId: 'widgetId',
  nodeTemplateId: 'nodeId',
  roleTemplateId: 'roleId',
  teamTemplateId: 'teamId',
};

const expressionRef: { [key: string]: string } = {
  expressionTemplate: 'expression',
};

const t = (str: string, values: any): string => {
  const compiled = _.template(str, {
    interpolate: /{([\s\S]+?)}/g,
  });
  return compiled(values);
};

export const detachBO = (bo: any) => {
  const templateIdKeys = Object.keys(TemplateIdToIdRef);
  _.mapKeys(bo, (value, key) => {
    if (templateIdKeys.includes(key)) {
      _.unset(bo, key);
    }
    if (_.isObject(value)) {
      detachBO(value);
    }
  });
};

const generateNewId = (id: string): string => {
  const idPrefix = id.substring(0, 3);
  // option and attachement
  if (idPrefix === CONST_PREFIX_ATTACHMENT || idPrefix === CONST_PREFIX_OPTION) {
    return id;
  }
  return generateNanoID(idPrefix, id.length - idPrefix.length);
};

export const transformBOById = (bo: any) => {
  const idKeys = Object.values(TemplateIdToIdRef);
  const expressionKeys = Object.values(expressionRef);

  const relation: Map<string, string> = new Map<string, string>();

  const initRelation = (data: any) => {
    _.mapKeys(data, (value, key) => {
      if (idKeys.includes(key) && _.isString(value)) {
        if (!relation.has(value)) {
          relation.set(value, generateNewId(value));
        }
      }
      if (_.isObject(value)) {
        initRelation(value);
      }
    });
  };

  const transformBO = (data: any) => {
    _.mapKeys(data, (value, key) => {
      if (idKeys.includes(key) && _.isString(value)) {
        if (relation.has(value)) {
          _.set(data, key, relation.get(value));
        }
      }
      if (expressionKeys.includes(key) && _.isString(value)) {
        const newExpression = t(value, Object.fromEntries(relation.entries()));
        _.set(data, key, newExpression);
      }
      // handle map relation, such as record data map fieldId => value
      if (relation.has(key)) {
        const newId = relation.get(key);
        if (newId) {
          _.set(data, newId, value);
          _.unset(data, key);
        }
      }
      if (_.isObject(value)) {
        transformBO(value);
      }
    });
  };

  initRelation(bo);
  transformBO(bo);
};

export const convertBOByTemplateId = (
  bo: object,
  getInstanceId: (templateId: string) => string,
  opts?: { isTemplateOperation?: boolean; ignoreKeys?: string[] },
) => {
  const { isTemplateOperation, ignoreKeys } = opts || {};

  const templateIdKeys = Object.keys(TemplateIdToIdRef);

  const convertToInstanceId = (data: object, key: string, value: string) => {
    // 非模板操作
    if (!isTemplateOperation) {
      return getInstanceId(value);
    }
    // 模板操作，处理特殊key
    if (['fieldTemplateId', 'viewTemplateId'].includes(key) && 'databaseTemplateId' in data) {
      return getInstanceId(`${data.databaseTemplateId}:${value}`);
    }
    if (key === 'roleTemplateId') {
      return getInstanceId(`ROLE:${value}`);
    }
    if (key === 'teamTemplateId') {
      return getInstanceId(`TEAM:${value}`);
    }
    return getInstanceId(value);
  };

  const convertBO = (data: any) => {
    const unsetKeys: string[] = [];
    _.mapKeys(data, (value, key) => {
      if (ignoreKeys && ignoreKeys.includes(key)) {
        return;
      }
      if (templateIdKeys.includes(key)) {
        // 数组类型的模板ID，如 formTemplateIds
        if (_.isArray(value) && value.every((i) => _.isString(i))) {
          const instanceIds = value.map((templateId) => getInstanceId(templateId));
          _.set(data, TemplateIdToIdRef[key], instanceIds);
          unsetKeys.push(key);
        }
        if (_.isString(value)) {
          const instanceId = convertToInstanceId(data, key, value);
          // 赋值实例ID
          _.set(data, TemplateIdToIdRef[key], instanceId);
          // 字段ID/视图ID 依赖同一层级的数据库ID，先不在这置空
          unsetKeys.push(key);
        }
      }
      if (_.isObject(value)) {
        convertBO(value);
      }
      if (_.isArray(value)) {
        value.forEach((item) => convertBO(item));
      }
    });
    // 置空模板ID
    unsetKeys.forEach((key) => {
      _.unset(data, key);
    });
  };

  convertBO(bo);
};

export async function boToCreatePO(spaceId: string, resources: NodeResource[], existingResources?: NodeResource[]) {
  const templateIdPathStore = new TemplateIdPathStore();

  const idPathStore = new TemplateIdPathStore();

  const unitIds = new Set<string>();

  const replaceInstanceId =
    (folderTemplateId?: string) =>
    (id: string): string => {
      let instanceId = idPathStore.getMayBeNull(id);
      if (instanceId) {
        return instanceId;
      }

      instanceId = templateIdPathStore.getMayBeNull(id);
      if (instanceId) {
        return instanceId;
      }
      if (folderTemplateId) {
        instanceId = templateIdPathStore.getMayBeNull(`${folderTemplateId}:${id}`);
        if (instanceId) {
          return instanceId;
        }
      }
      return id;
    };

  const setTemplateIdStore = (
    value: string,
    key: string,
    param: {
      resourceTemplateId?: string;
      folderTemplateId?: string;
    },
  ) => {
    if (key.split(':').length > 1) {
      if (!templateIdPathStore.getMayBeNull(key)) {
        templateIdPathStore.set(key, value);
      }
    } else {
      const { resourceTemplateId, folderTemplateId } = param;
      let newKey = key;
      if (resourceTemplateId && folderTemplateId) {
        newKey = `${folderTemplateId}:${resourceTemplateId}:${key}`;
      } else if (folderTemplateId) {
        newKey = `${folderTemplateId}:${key}`;
      } else if (resourceTemplateId) {
        newKey = `${resourceTemplateId}:${key}`;
      }
      if (!templateIdPathStore.getMayBeNull(newKey)) {
        templateIdPathStore.set(newKey, value);
      }
    }
  };

  const getId = (prefix: string, genNewId?: boolean, originalId?: string) =>
    genNewId ? generateNanoID(prefix) : originalId || generateNanoID(prefix);
  const getResourceId = (resource: NodeResource, genNewId?: boolean) => {
    if (genNewId) {
      return NodeResourceAdapter.generateId(resource.resourceType);
    }
    return resource.id || NodeResourceAdapter.generateId(resource.resourceType);
  };
  const setId = async (data: NodeResource[], genNewId?: boolean, prefix?: string) => {
    // init id
    for (const resource of data) {
      const id = getResourceId(resource, genNewId);
      if (resource.templateId) {
        // 有些模版有依赖 重复安装了依赖模版 因此跳过设置 防止报错 会优先采用ID进行关联
        setTemplateIdStore(id, resource.templateId, { folderTemplateId: prefix });
      }
      if (resource.id) {
        idPathStore.set(resource.id, id);
      }
      resource.id = id;

      // handle records data
      if (resource.resourceType === 'DATABASE') {
        const fieldMap = ((resource as Database).fields ?? []).reduce<Record<string, DatabaseField>>((acc, field) => {
          if (field.id) {
            acc[field.id] = field;
          } else if (field.templateId) {
            acc[field.templateId] = field;
          }
          return acc;
        }, {});

        // records
        (resource as Database)?.records?.forEach((record) => {
          const recordId = generateNanoID(CONST_PREFIX_RECORD);
          if (record.templateId && resource.templateId) {
            setTemplateIdStore(recordId, record.templateId, {
              folderTemplateId: prefix,
              resourceTemplateId: resource.templateId,
            });
          }
          if (record.id) {
            idPathStore.set(record.id, recordId);
          }
          record.id = recordId;
          if (record.data) {
            for (const fieldKey in record.data) {
              if (Object.hasOwnProperty.call(record.data, fieldKey)) {
                const field = fieldMap[fieldKey];
                if (field.type === 'MEMBER' && _.isArray(record.data[fieldKey])) {
                  for (const unitId of record.data[fieldKey]) {
                    if (unitId) {
                      unitIds.add(unitId as string);
                    }
                  }
                }
              }
            }
          }
        });
      }

      // fields
      (resource as Database)?.fields?.forEach((field) => {
        const fieldId = getId(CONST_PREFIX_FIELD, genNewId, field.id);
        if (resource.templateId && field.templateId) {
          setTemplateIdStore(fieldId, field.templateId, {
            folderTemplateId: prefix,
            resourceTemplateId: resource.templateId,
          });
        }
        if (field.id) {
          idPathStore.set(field.id, fieldId);
        }
        field.id = fieldId;
      });
      // views
      (resource as Database)?.views?.forEach((view) => {
        const viewId = getId(CONST_PREFIX_VIEW, genNewId, view.id);
        if (resource.templateId && view.templateId) {
          setTemplateIdStore(viewId, view.templateId, {
            folderTemplateId: prefix,
            resourceTemplateId: resource.templateId,
          });
        }
        if (view.id) {
          idPathStore.set(view.id, viewId);
        }
        view.id = viewId;
      });
      // triggers
      (resource as Automation)?.triggers?.forEach((trigger) => {
        const triggerId = getId(CONST_PREFIX_TRIGGER, genNewId, trigger.id);
        if (resource.templateId && trigger.templateId) {
          setTemplateIdStore(triggerId, trigger.templateId, {
            folderTemplateId: prefix,
            resourceTemplateId: resource.templateId,
          });
        }
        if (trigger.id) {
          idPathStore.set(trigger.id, triggerId);
        }
        trigger.id = triggerId;
      });
      // actions
      (resource as Automation)?.actions?.forEach((action) => {
        const setActionId = (data: Action) => {
          const actionId = getId(CONST_PREFIX_ACTION, genNewId, data.id);
          if (resource.templateId && data.templateId) {
            setTemplateIdStore(actionId, data.templateId, {
              folderTemplateId: prefix,
              resourceTemplateId: resource.templateId,
            });
          }
          if (data.id) {
            idPathStore.set(data.id, actionId);
          }
          data.id = actionId;
        };
        setActionId(action);
        if (Object.hasOwnProperty.call(action, 'actions')) {
          (action as FindRecordsAction)?.actions?.forEach((subAction: Action) => setActionId(subAction));
        }
      });
      // widgets
      (resource as Dashboard)?.widgets?.forEach((widget) => {
        const widgetId = getId(CONST_PREFIX_WIDGET, genNewId, widget.id);
        if (resource.templateId && widget.templateId) {
          setTemplateIdStore(widgetId, widget.templateId, {
            folderTemplateId: prefix,
            resourceTemplateId: resource.templateId,
          });
        }
        if (widget.id) {
          idPathStore.set(widget.id, widgetId);
        }
        widget.id = widgetId;
      });

      if (resource.resourceType === 'FOLDER') {
        setId((resource as Folder).children ?? [], genNewId, resource.templateId);
      }
    }

    // db check
    // check unitId relation
    if (unitIds.size > 0) {
      const existUnitIds = await UnitFactory.selectIdsBySpaceIdAndUnitIds(spaceId, Array.from(unitIds));
      for (const unitId of unitIds) {
        if (existUnitIds.includes(unitId)) {
          idPathStore.set(unitId, '1');
        } else {
          idPathStore.set(unitId, '0');
        }
      }
    }
  };

  const setRelation = () => {
    for (const resource of resources) {
      const resourceSO = NodeResourceAdapter.new(resource.resourceType);
      if (resource.resourceType === 'FOLDER') {
        resourceSO.relationInstanceId(resource, { replaceInstanceId: replaceInstanceId(resource.templateId) });
      } else {
        resourceSO.relationInstanceId(resource, { replaceInstanceId: replaceInstanceId() });
      }
    }
  };

  await setId(resources, true);
  if (existingResources) {
    await setId(existingResources, false);
  }

  setRelation();
}

/**
 * 初始化id->templateId 关联关系，没有templateId用Id填充
 * 注意: 当前资源一定是po to bo 的数据
 * @param resource stored resource bo
 */
export function initIdPathStore(resources: NodeResource[]): TemplateIdPathStore {
  const idPathStore = new TemplateIdPathStore();

  const setIdPathStore = (data: NodeResource[]) => {
    for (const resource of data) {
      if (!resource.id) {
        throw new Error(`Invalid parameter: ${resource.resourceType}`);
      }
      idPathStore.set(resource.id, resource.templateId || resource.id);
      // fields
      (resource as Database)?.fields?.forEach((field) => {
        if (!field.id) {
          throw new Error(`Invalid parameter: ${field.type}`);
        }
        idPathStore.set(field.id, field.templateId || field.id);
      });
      // views
      (resource as Database)?.views?.forEach((view) => {
        if (!view.id) {
          throw new Error(`Invalid parameter: ${view.type}`);
        }
        idPathStore.set(view.id, view.templateId || view.id);
      });
      // records
      (resource as Database)?.records?.forEach((record) => {
        if (!record.id) {
          throw new Error(`Invalid parameter of the record: ${resource.id}`);
        }
        idPathStore.set(record.id, record.templateId || record.id);
      });
      // triggers
      (resource as Automation)?.triggers?.forEach((trigger) => {
        if (!trigger.id) {
          throw new Error(`Invalid parameter: ${trigger.triggerType}`);
        }
        idPathStore.set(trigger.id, trigger.templateId || trigger.id);
      });
      // actions, recursive action
      (resource as Automation)?.actions?.forEach((action) => {
        if (!action.id) {
          throw new Error(`Invalid parameter: ${action.actionType}`);
        }
        idPathStore.set(action.id, action.templateId || action.id);
      });
      // widgets
      (resource as Dashboard)?.widgets?.forEach((widget) => {
        if (!widget.id) {
          throw new Error(`Invalid parameter: ${widget.type}`);
        }
        idPathStore.set(widget.id, widget.templateId || widget.id);
      });

      if (resource.resourceType === 'FOLDER') {
        setIdPathStore((resource as Folder).children ?? []);
      }
    }
  };

  setIdPathStore(resources);
  return idPathStore;
}

export const processRelationInstanceId = (params: {
  instanceId?: string;
  templateId?: string;
  prevKey?: string;
  opts: IRelationIdOpts;
}) => {
  const { instanceId, templateId, prevKey, opts } = params;
  const { convertToInstanceId, replaceInstanceId, convertToTemplateId } = opts;
  if (convertToInstanceId && !instanceId && templateId) {
    // 字段/视图等ID处理，需要带上数据库ID；角色/团队等ID处理，需要带上前缀ROLE/TEAM
    const key = prevKey ? `${prevKey}:${templateId}` : templateId;
    return { instanceId: convertToInstanceId(key), templateId: undefined };
  }
  // if (replaceInstanceId && instanceId) {
  //   return { instanceId: replaceInstanceId(instanceId), templateId };
  // }
  if (replaceInstanceId) {
    return { instanceId: replaceInstanceId(templateId || instanceId!), templateId };
  }
  if (convertToTemplateId) {
    return { instanceId: undefined, templateId: templateId || (instanceId && convertToTemplateId(instanceId)) };
  }
  return { instanceId, templateId };
};

async function getAttachmentSO(cover: AvatarLogo | string | undefined): Promise<AttachmentSO | undefined> {
  if (cover === undefined) {
    return undefined;
  }
  if (typeof cover === 'string') {
    return undefined;
  }
  switch (cover.type) {
    case 'AI':
    case 'ATTACHMENT': {
      return AttachmentSO.init(cover.attachmentId);
    }
    default:
      return undefined;
  }
}

function getTemplateAsset(attachment: AttachmentSO): AttachmentAvatar {
  const attachmentId = attachment.id.startsWith('tpl') ? attachment.id : `tpl${attachment.id}`;
  const relativePath = `template/${attachmentId}${attachment.ext}`;
  return {
    type: 'ATTACHMENT',
    attachmentId,
    relativePath,
  };
}

async function attachToBikafileAsset(
  cover: AvatarLogo | string | undefined,
  bikaFile: Bikafile,
): Promise<string | AvatarLogo | undefined> {
  const so = await getAttachmentSO(cover);
  if (!so) {
    return cover;
  }
  const asset = getTemplateAsset(so);

  const assetName = asset.relativePath.split('/').pop()!;
  if (!bikaFile.hasAsset(assetName)) {
    const blob = await so.getObjectBlob();
    const buffer = await blob.arrayBuffer();
    bikaFile.addAsset(assetName, Buffer.from(buffer));
  }

  return asset;
}

export async function handleTemplateAssets(template: CustomTemplate, bikaFile: Bikafile) {
  // template cover
  const cover = await attachToBikafileAsset(template.cover, bikaFile);
  if (cover) {
    template.cover = cover;
  }

  const handleResourceAssets = async (resources: NodeResource[]) => {
    for (const resource of resources) {
      resource.icon = (await attachToBikafileAsset(resource.icon, bikaFile)) as AvatarLogo;
      if (resource.resourceType === 'FOLDER') {
        await handleResourceAssets((resource as Folder).children ?? []);
      }
      if (resource.resourceType === 'FORM') {
        (resource as FormBO).brandLogo = await attachToBikafileAsset((resource as FormBO).brandLogo, bikaFile);
      }
      if (resource.resourceType === 'DATABASE') {
        if (!(resource as Database).records) {
          continue;
        }
        const fields = (resource as Database).fields;
        const fieldMap = _.keyBy(fields, 'templateId');
        for (const record of (resource as Database).records!) {
          for (const key of Object.keys(record.data || {})) {
            const field = fieldMap[key];
            if (field?.type === 'ATTACHMENT') {
              const cellvalue = record.data[key];
              if (isArray(cellvalue) && cellvalue.length) {
                for (const value of cellvalue) {
                  const { id, path } = value as AttachmentCellData;
                  const newAsset = await attachToBikafileAsset(
                    {
                      type: 'ATTACHMENT',
                      attachmentId: id,
                      relativePath: path,
                    },
                    bikaFile,
                  );
                  if (newAsset && typeof newAsset === 'object' && newAsset.type === 'ATTACHMENT') {
                    (value as AttachmentCellData).id = newAsset.attachmentId;
                    (value as AttachmentCellData).path = newAsset.relativePath;
                  }
                }
              }
            }
          }
        }
      }
    }
  };
  await handleResourceAssets(template.resources);
}

export async function handleResourceAssets(spaceId: string, resources: NodeResource[], bikaFile: Bikafile) {
  const resourceIds: string[] = [];
  // 遍历资源，获取所有资源ID
  const recursiveResource = (resources: NodeResource[]) => {
    for (const resource of resources) {
      if (resource.id) {
        resourceIds.push(resource.id);
      }
      if (resource.resourceType === 'FOLDER' && (resource as Folder).children) {
        recursiveResource((resource as Folder).children!);
      }
    }
  };
  recursiveResource(resources);

  const handler = async (so: AttachmentSO) => {
    const blob = await so.getObjectBlob();
    const buffer = await blob.arrayBuffer();
    bikaFile.addAsset(`${so.id}${so.ext}`, Buffer.from(buffer));
  };
  // 遍历资源，处理资源资产
  await SpaceAttachmentSO.exportAttachments(spaceId, resourceIds, handler);
}
