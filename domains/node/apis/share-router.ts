import { z } from 'zod';
import { NodeController } from '@bika/domains/node/apis';
import { SpaceAuditLogSO } from '@bika/domains/system/server/audit/space-audit-log-so';
import { RoleSO } from '@bika/domains/unit/server/role-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { protectedProcedure, router } from '@bika/server-orm/trpc';
import * as T from '@bika/types/node/dto';
import { CollaboratorPaginationVOSchema } from '@bika/types/permission/vo';

/**
 * Node share router
 */
export const nodeShareRouter = router({
  /**
   * Retrieve the share info of a node
   */
  info: protectedProcedure.input(T.NodeShareInfoDTOSchema).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    const { spaceId, id: nodeId } = input;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    const node = await space.getNode(nodeId);
    const nodeAclSO = await node.toAclSO();
    return nodeAclSO.toVO({ locale: user.locale });
  }),
  /**
   * Retrieve the collaborators of a node
   */
  collaborators: protectedProcedure
    .input(T.NodeCollaboratorListDTOSchema)
    .output(CollaboratorPaginationVOSchema)
    .query(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      const { spaceId, id: nodeId, pageNo, pageSize } = input;
      const member = await user.getMember(spaceId);
      const space = await member.getSpace();
      const node = await space.getNode(nodeId);
      const nodeAclSO = await node.toAclSO();
      return nodeAclSO.collaborators({ pageNo, pageSize });
    }),
  /**
   * Update the share scope of a node
   */
  updateScope: protectedProcedure.input(T.NodeShareUpdateDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    const { spaceId, id: nodeId, scope } = input;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    const node = await space.getNode(nodeId);
    const nodeAclSO = await node.toAclSO();
    await nodeAclSO.operator(user.id).updateShareScope(scope);
    // 记录审计事件
    SpaceAuditLogSO.createFromRequestContext(ctx, {
      spaceId: space.id,
      type: 'share.scope.update',
      id: node.id,
      name: JSON.stringify(node.name),
      scope,
    });
  }),
  /**
   * Restore share config of a node
   */
  restore: protectedProcedure.input(T.NodeShareRestoreDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    const { spaceId, id: nodeId } = input;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    const node = await space.getNode(nodeId);
    const nodeAclSO = await node.toAclSO();
    await nodeAclSO.operator(user.id).restore();
    // 记录审计事件
    SpaceAuditLogSO.createFromRequestContext(ctx, {
      spaceId: space.id,
      type: 'share.restore',
      id: node.id,
      name: JSON.stringify(node.name),
    });
  }),
  /**
   * Create a password for a sharing node
   */
  createPassword: protectedProcedure.input(T.NodeSharePasswordCreateDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    const { spaceId, id: nodeId } = input;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    const node = await space.getNode(nodeId);
    const nodeAclSO = await node.toAclSO();
    await nodeAclSO.operator(user.id).createSharePassword();
    // 记录审计事件
    SpaceAuditLogSO.createFromRequestContext(ctx, {
      spaceId: space.id,
      type: 'share.password.create',
      id: node.id,
      name: JSON.stringify(node.name),
    });
  }),
  /**
   * Update the password of a sharing node
   */
  updatePassword: protectedProcedure.input(T.NodeSharePasswordUpdateDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    const { spaceId, id: nodeId, password } = input;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    const node = await space.getNode(nodeId);
    const nodeAclSO = await node.toAclSO();
    await nodeAclSO.operator(user.id).updateSharePassword(password);
    // 记录审计事件
    SpaceAuditLogSO.createFromRequestContext(ctx, {
      spaceId: space.id,
      type: 'share.password.update',
      id: node.id,
      name: JSON.stringify(node.name),
    });
  }),
  /**
   * Delete the password of a sharing node
   */
  deletePassword: protectedProcedure.input(T.NodeSharePasswordDeleteDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    const { spaceId, id: nodeId } = input;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    const node = await space.getNode(nodeId);
    const nodeAclSO = await node.toAclSO();
    await nodeAclSO.operator(user.id).deleteSharePassword();
    // 记录审计事件
    SpaceAuditLogSO.createFromRequestContext(ctx, {
      spaceId: space.id,
      type: 'share.password.delete',
      id: node.id,
      name: JSON.stringify(node.name),
    });
  }),
  /**
   * Grant permission for a member unit to a node
   */
  grantPermission: protectedProcedure.input(T.NodeGrantPermissionSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    await NodeController.grantNodePermissionByMember(ctx, input);
  }),

  /**
   * Revoke permission for a member unit to a node
   */
  revokePermission: protectedProcedure.input(T.NodeRevokePermissionSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    const { spaceId, id: nodeId, unitId } = input;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    const node = await space.getNode(nodeId);
    const unit = await space.getUnit(unitId);
    const nodeAclSO = await node.toAclSO();
    await nodeAclSO.operator(user.id).revoke(unit);
    // 记录审计事件
    SpaceAuditLogSO.createFromRequestContext(ctx, {
      spaceId: space.id,
      type: 'share.revoke',
      id: node.id,
      name: JSON.stringify(node.name),
      unit: unit.isRole ? JSON.stringify((unit as RoleSO).getName()) : unit.getName(user.locale),
    });
  }),

  toggleShortURL: protectedProcedure
    .input(
      z.object({
        nodeId: z.string(),
      }),
    )
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      return NodeController.toggleShortURL(ctx, input);
    }),
});
