import { errors, ServerError } from '@bika/contents/config/server/error';
import { TmpAttachmentSO } from '@bika/domains/attachment/server/tmp-attachment-so';
import { EventSO } from '@bika/domains/event/server/event/event-so';
import { FolderSO, TemplateFolderSO } from '@bika/domains/node/server/folder-so';
import { NodeSO } from '@bika/domains/node/server/node-so';
import { StoreTemplateSO } from '@bika/domains/store/server/store-template-so';
import { RemoteStorageSO } from '@bika/domains/system/server';
import { SpaceAuditLogSO } from '@bika/domains/system/server/audit/space-audit-log-so';
import { UserNodeGuideService } from '@bika/domains/user/server/user-node-guide-service';
import { UserSO } from '@bika/domains/user/server/user-so';
import { TRPCError } from '@bika/server-orm/trpc';
import { db } from '@bika/server-orm';
import { BikafileData } from '@bika/types/bikafile/bo';
import { EditorNodeFolderDTO } from '@bika/types/editor/dto';
import { NodeResource, Folder } from '@bika/types/node/bo';
import * as T from '@bika/types/node/dto';
import { NodeDetailVO, NodeInfoVO, NodeTreeVO, FolderVO, type TemplateFolderVO } from '@bika/types/node/vo';
import { NodeShareScope } from '@bika/types/permission/vo';
import { ApiFetchRequestContext } from '@bika/types/user/vo';
import { TalkSO } from '../../talk/server/talk-so';
import { ShortURLSO } from '../server/short-url-so';
import { PRIVATE_ROOT_NODE_PREFIX, nodeResourceLockKey } from '../server/types';
import { canReadNode } from '../server/utils';

export async function listResource(user: UserSO, req: T.NodeListDTOZodOutput): Promise<NodeTreeVO[]> {
  const { spaceId, parentId, name, resourceType, pageNo, pageSize } = req;
  const member = await user.getMember(spaceId);
  const space = await member.getSpace();
  // search under the parent folder
  const nodes = await space.findNodes({
    pagination: { pageNo, pageSize },
    parentId,
    name,
    type: resourceType,
    loopParents: true,
    privateUnitId: parentId?.startsWith(PRIVATE_ROOT_NODE_PREFIX) ? member.id : undefined,
  });
  const nodeVOList = await Promise.all(nodes.map((node) => node.toVO({ locale: user.locale, userId: user.id })));
  // 过滤不可读的资源节点
  return nodeVOList.filter((node) => node.permission?.abilities.readNode || false);
}

export async function searchResource(user: UserSO, req: T.NodeSearchDTO): Promise<NodeTreeVO[]> {
  const { spaceId, keyword, resourceType, scope } = req;
  const member = await user.getMember(spaceId);
  const space = await member.getSpace();
  const rootFolder = await space.getRootFolder();
  const children = await rootFolder.searchChildren({
    keyword,
    resourceType,
    privateUnitId: scope === 'PRIVATE' ? member.id : undefined,
    pageNo: req.pageNo,
    pageSize: req.pageSize,
    nodeId: req.nodeId,
  });
  const nodeVOList = await Promise.all(children.map((node) => node.toVO({ locale: user.locale, userId: user.id })));
  // can not search unreadable node whith keyword, but return the node itself at default, on the too
  return nodeVOList.filter((node) => (req.nodeId === node.id ? true : canReadNode(node)));
}

export async function retrieveRoot(
  user: UserSO,
  spaceId: string,
  scope?: 'SPACE' | 'PRIVATE',
): Promise<FolderVO | null> {
  const member = await user.getMember(spaceId);
  const space = await member.getSpace();
  const rootFolder = await space.getRootFolder();
  // 鉴权
  // if (scope === 'SPACE') {
  // 团队资源需要验证权限, 私人不需要
  // const aclSO = await rootFolder.toAclSO();
  // const canRead = await aclSO.can(member, 'readNode');
  // if (!canRead) {
  //   return null;
  // }
  // }
  const nodeTree = await rootFolder.toVO({ locale: user.locale, userId: user.id, depth: 2, scope });
  if (scope === 'PRIVATE') {
    const memberCount = await space.getMemberCount();
    if (!nodeTree.children?.length && memberCount < 2) {
      return { ...nodeTree, children: undefined };
    }
  }
  return nodeTree;
}

export async function retrievePermission(ctx: ApiFetchRequestContext, req: T.NodeInfoDTO) {
  const { session } = ctx;
  const { id } = req;
  const node = await NodeSO.init(id);

  const userId = session?.userId;

  const aclSO = await node.toAclSO();
  const isPublicAccess = await aclSO.isPublicAccess();
  const privilege = await node.getPrivilege(aclSO, userId);

  return {
    isPublicAccess,
    privilege,
    sharing: aclSO.isSharing,
    sharePassword: aclSO.sharePassword !== null,
    shareScope: aclSO.shareScope as NodeShareScope,
  };
}

export async function retrieveNodeInfo(ctx: ApiFetchRequestContext, req: T.NodeInfoDTO): Promise<NodeInfoVO> {
  const {
    session,
    // locale
  } = ctx;
  const { id } = req;
  if (session?.userId) {
    const user = await UserSO.init(session.userId);
    const node = await NodeSO.init(id);
    const aclSO = await node.toAclSO();
    // 鉴权
    await aclSO.authorize(user, 'readNode');
    return node.toInfoVO();
  }

  const node = await NodeSO.init(id);
  const aclSO = await node.toAclSO();
  const isPublicAccess = await aclSO.isPublicAccess();
  if (!isPublicAccess) {
    throw new TRPCError({ code: 'UNAUTHORIZED' });
  }

  return node.toInfoVO();
}

export async function retrieveNodeTree(ctx: ApiFetchRequestContext, req: T.NodeInfoDTO): Promise<NodeTreeVO> {
  const { id } = req;

  const userId = ctx.session?.userId;
  const locale = ctx.locale;

  if (userId) {
    const user = await UserSO.init(userId);
    const node = await NodeSO.init(id);
    const aclSO = await node.toAclSO();
    // 鉴权
    await aclSO.authorize(user, 'readNode');
    return node.toVO({ locale: user.locale, userId: user.id });
  }

  const node = await NodeSO.init(id);
  const aclSO = await node.toAclSO();
  const isPublicAccess = await aclSO.isPublicAccess();
  if (!isPublicAccess) {
    throw new TRPCError({ code: 'UNAUTHORIZED' });
  }

  return node.toVO({ locale });
}

export async function retrieveDetail(
  ctx: ApiFetchRequestContext,
  req: T.NodeInfoDTO,
  newTalk: boolean = false,
): Promise<NodeDetailVO> {
  const { session, locale } = ctx;
  const { id } = req;
  if (session?.userId) {
    const user = await UserSO.init(session.userId);
    const node = await NodeSO.init(id);
    const aclSO = await node.toAclSO();

    // 鉴权
    await aclSO.authorize(user, 'readNode');

    // 检测是否首次访问该类型的节点
    const isFirstVisit = newTalk ? false : await UserNodeGuideService.checkAndMarkFirstVisit(user, node.type);

    const nodeDetailVO = await node.toNodeDetailVO({
      locale: user.locale,
      userId: user.id,
      scope: id.startsWith(PRIVATE_ROOT_NODE_PREFIX) ? 'PRIVATE' : node.scope,
      timeZone: user.timeZone,
    });

    // 添加首次访问标记
    nodeDetailVO.isFirstVisit = isFirstVisit;

    //  通常这个接口是被用户点开了详情，那么: 激活 FeedSO
    if (newTalk === true) {
      TalkSO.upsertByNode(node, user);
      const sharing = await user.existSpace(node.spaceId);
      if (!sharing) RemoteStorageSO.setUserLastActiveSpaceNode(user.id, node.spaceId, node.id);
    }

    // space 审计
    SpaceAuditLogSO.createFromRequestContext(ctx, {
      type: 'node.get',
      id: node.id,
      name: JSON.stringify(node.name),
      spaceid: node.spaceId,
      spaceId: node.spaceId,
    });
    return nodeDetailVO;
  }

  const node = await NodeSO.init(id);
  const aclSO = await node.toAclSO();
  const isPublicAccess = await aclSO.isPublicAccess();
  if (!isPublicAccess) {
    throw new TRPCError({ code: 'UNAUTHORIZED' });
  }
  return node.toNodeDetailVO({ locale });
}

export async function retrievePosition(ctx: ApiFetchRequestContext, req: T.NodeInfoDTO): Promise<FolderVO> {
  const { id } = req;
  const node = await NodeSO.init(id);
  // 目录树，不包括root
  const parents = await node.getParents();
  // 因为默认加载两层，所以这里只需要拿到第三层的folder, 获取目录树就好了
  if (parents.length > 1) {
    const parent = parents[parents.length - 2];
    const folder = await parent.toResourceSO<FolderSO>();
    // 这里depth 需要全量到当前文件
    const folderVO = await folder.toVO({
      locale: ctx.locale,
      userId: ctx.session?.userId,
      depth: parents.length, // 全量到当前文件的下一个深度
    });
    return folderVO;
  }
  throw new Error('Invalid node position');
}

export async function retrieveBO(ctx: ApiFetchRequestContext, req: T.NodeInfoDTO): Promise<NodeResource> {
  const { session } = ctx;
  const { id } = req;
  if (session?.userId) {
    const user = await UserSO.init(session.userId);
    const node = await NodeSO.init(id);
    const aclSO = await node.toAclSO();
    // 鉴权
    await aclSO.authorize(user, 'readNode');
    return node.toBO();
  }

  const node = await NodeSO.init(id);
  const aclSO = await node.toAclSO();
  const isPublicAccess = await aclSO.isPublicAccess();
  if (!isPublicAccess) {
    throw new TRPCError({ code: 'UNAUTHORIZED' });
  }
  return node.toBO();
}

/**
 * 一个接口获取root / fodler / template fodler的detail bo
 *
 * @param ctx
 * @param req
 * @returns
 */
export async function retrieveNodeFolderDTO(
  ctx: ApiFetchRequestContext,
  req: T.NodeInfoDTO,
): Promise<EditorNodeFolderDTO> {
  // const { userId } = ctx.session!;
  // const user = await UserSO.init(userId);

  const nodeDetailVO = await retrieveDetail(ctx, req);

  if (nodeDetailVO.type === 'FOLDER' || nodeDetailVO.type === 'ROOT') {
    const folderBO = await retrieveBO(ctx, req);
    return {
      nodeFolderType: nodeDetailVO.type,
      data: folderBO as Folder,
    };
  }
  if (nodeDetailVO.type === 'TEMPLATE') {
    const templateFolderVO = nodeDetailVO.resource as TemplateFolderVO;
    const templateId = templateFolderVO.templateId!;
    const storeTemplateSO = await StoreTemplateSO.init(templateId);
    const storeTemplateVO = await storeTemplateSO.toVO(ctx.session?.userId);

    const folderBO = await retrieveBO(ctx, req);
    return {
      nodeFolderType: 'TEMPLATE',
      data: folderBO as Folder,
      template: storeTemplateVO,
    };
  }

  throw new Error(`Invalid node type: ${nodeDetailVO.type}`);
}

export async function createResource(ctx: ApiFetchRequestContext, req: T.NodeCreateDTO): Promise<NodeDetailVO> {
  const { spaceId, parentId, data } = req;
  const { userId } = ctx.session!;
  const user = await UserSO.init(userId);
  const member = await user.getMember(spaceId);
  const space = await member.getSpace();
  const parentNode = await space.getNode(parentId);
  const isPrivateParentNode = parentId.startsWith(PRIVATE_ROOT_NODE_PREFIX);
  if (!isPrivateParentNode) {
    // 鉴权
    const nodeAcl = await parentNode.toAclSO();
    // 鉴权
    await nodeAcl.authorize(member, 'createNode');
  }

  // 检查用量
  const entitlement = await space.getEntitlement();
  await entitlement.checkUsageExceed({ feature: 'RESOURCES', value: 1 });
  const folder = await parentNode.toResourceSO<FolderSO>();
  const node = await folder.createChildSimple(user, data, {
    scope: isPrivateParentNode ? 'PRIVATE' : 'SPACE',
    createDefaultRecords: true,
  });
  const nodeDetailVO = await node.toNodeDetailVO({ locale: user.locale, userId: user.id });
  // 记录审计事件
  SpaceAuditLogSO.createFromRequestContext(ctx, {
    type: 'node.create',
    id: node.id,
    name: JSON.stringify(node.name),
    parent: JSON.stringify(parentNode.name),
    spaceid: space.id,
    spaceId: space.id,
  });
  return nodeDetailVO;
}

export async function deleteResource(ctx: ApiFetchRequestContext, req: T.NodeDeleteDTO): Promise<void> {
  const { spaceId, id } = req;
  const { userId } = ctx.session!;
  const user = await UserSO.init(userId);
  const member = await user.getMember(spaceId);
  const space = await member.getSpace();
  const node = await space.getNode(id);
  const nodeAclSO = await node.toAclSO();

  // 鉴权
  await nodeAclSO.authorize(member, 'deleteNode');
  await node.delete(user);

  EventSO.node.onDelete(node, member);

  // 记录审计事件
  SpaceAuditLogSO.createFromRequestContext(ctx, {
    spaceId: space.id,
    type: 'node.delete',
    name: JSON.stringify(node.name),
    spaceid: space.id,
  });
}

export async function updateResource(ctx: ApiFetchRequestContext, req: T.NodeUpdateDTOReq) {
  const { spaceId, id, data } = req;
  const { userId } = ctx.session!;
  const user = await UserSO.init(userId);
  const member = await user.getMember(spaceId);
  const space = await member.getSpace();
  const node = await space.getNode(id);
  const nodeAclSO = await node.toAclSO();
  // 鉴权
  await nodeAclSO.authorize(member, 'updateNode');
  // 互斥更新,因为有排序和移动 lock for 5 seconds
  const updatedNode = await node.update(user, data);
  // 记录审计事件
  SpaceAuditLogSO.createFromRequestContext(ctx, {
    spaceId: space.id,
    type: 'node.update',
    id: node.id,
    name: JSON.stringify(node.name),
    spaceid: space.id,
  });
  EventSO.node.onUpdate(updatedNode);
}

export async function moveResource(ctx: ApiFetchRequestContext, req: T.NodeMoveDTOReq) {
  const { spaceId, id, data } = req;
  const { userId } = ctx.session!;
  const user = await UserSO.init(userId);
  const member = await user.getMember(spaceId);
  const space = await member.getSpace();
  const node = await space.getNode(id);
  const nodeAclSO = await node.toAclSO();
  // 被移动的节点鉴权(可更改)
  await nodeAclSO.authorize(member, 'updateNode');
  // 目标父节点鉴权
  const { parentId } = data;
  const parentNode = !parentId ? await space.getRootFolder() : await space.getNode(parentId);
  const isPrivateRoot = parentId?.startsWith(PRIVATE_ROOT_NODE_PREFIX);
  const parentAclSO = await parentNode.toAclSO();
  await parentAclSO.authorize(member, 'createNode');
  if (parentNode.scope !== node.scope && !isPrivateRoot) {
    // check references
    const references = await node.getReferences();
    for (const reference of references) {
      if (reference.scope !== parentNode.scope) {
        throw new ServerError(errors.node.cannot_move_node_to_public_scope);
      }
    }
  }
  // 排序和移动,互斥更新,lock for 5 seconds
  const lock = await db.redis.lock(nodeResourceLockKey(spaceId, 'move'), 5000);
  try {
    const updatedNode = await node.move(user, { ...data, scope: isPrivateRoot ? 'PRIVATE' : parentNode.scope });
    EventSO.node.onMoved(updatedNode, node);
  } finally {
    await lock.release();
  }
  // 私有节点移动到空间不记录审计事件
  if (!req.data.scope) {
    // 记录审计事件
    SpaceAuditLogSO.createFromRequestContext(ctx, {
      spaceId: space.id,
      type: 'node.move',
      id: node.id,
      name: JSON.stringify(node.name),
      spaceid: space.id,
    });
  }
}

export async function publishTemplate(ctx: ApiFetchRequestContext, req: T.NodePublishDTO): Promise<string> {
  const { spaceId, id, data } = req;
  const { userId } = ctx.session!;
  const user = await UserSO.init(userId);
  const member = await user.getMember(spaceId);
  const space = await member.getSpace();
  const node = await space.getNode(id);
  // 权限判断
  await space.getAdminRoleAclSO().authorize(member, 'publishResource');
  if (node.type === 'ROOT') {
    throw new Error('Cannot publish root node');
  }
  if (data.type === 'LOCAL' && !node.templateId) {
    throw new Error('Export only support template folder');
  }
  const folder = await node.toResourceSO<FolderSO | TemplateFolderSO>();
  const templateId = await folder.publish(user, data);
  // 记录审计事件
  SpaceAuditLogSO.createFromRequestContext(ctx, {
    spaceId: space.id,
    type: 'node.publish',
    id: node.id,
    name: JSON.stringify(node.name),
    spaceid: space.id,
    templateid: templateId,
  });
  return templateId;
}

export async function detachTemplate(ctx: ApiFetchRequestContext, req: T.NodeDetachDTO): Promise<NodeTreeVO> {
  const { spaceId, id } = req;
  const { userId } = ctx.session!;
  const user = await UserSO.init(userId);
  const member = await user.getMember(spaceId);
  const space = await member.getSpace();
  const node = await space.getNode(id);
  const nodeAclSO = await node.toAclSO();
  // 鉴权
  await nodeAclSO.authorize(member, 'updateNode');
  const templateFolderSO = await node.toResourceSO<TemplateFolderSO>();
  await templateFolderSO.detach();
  const folder = await NodeSO.init(id);
  const folderVO = await folder.toVO({ locale: user.locale, userId: user.id });
  // 记录审计事件
  SpaceAuditLogSO.createFromRequestContext(ctx, {
    spaceId: space.id,
    type: 'node.detach',
    id: node.id,
    name: JSON.stringify(node.name),
    spaceid: space.id,
  });
  return folderVO;
}

export async function exportResource(ctx: ApiFetchRequestContext, req: T.NodeExportDTO): Promise<string> {
  const { spaceId, id, withRecords } = req;
  const { userId } = ctx.session!;
  const user = await UserSO.init(userId);
  const member = await user.getMember(spaceId);
  const space = await member.getSpace();
  const node = await space.getNode(id);
  const nodeAclSO = await node.toAclSO();
  // 鉴权
  await nodeAclSO.authorize(member, 'exportNode');
  const resourceSO = await node.toResourceSO();
  const exportBO = await resourceSO.export({ withRecords, locale: user.locale });

  const url = await node.exportBikafileAndGetDownloadUrl(
    {
      format: 'RESOURCES',
      resources: [exportBO],
    },
    user.locale,
  );
  // 记录审计事件
  SpaceAuditLogSO.createFromRequestContext(ctx, {
    spaceId: space.id,
    type: 'node.export',
    id: node.id,
    name: JSON.stringify(node.name),
    spaceid: space.id,
    url,
  });
  return url;
}

export async function bikafilePreview(user: UserSO, req: Omit<T.BikafileImportDTO, 'type'>): Promise<BikafileData> {
  const { spaceId, tmpAttachmentId } = req;
  const member = await user.getMember(spaceId);
  const space = await member.getSpace();
  const folder = await space.getRootFolder();
  const node = folder.toNodeSO();
  const nodeAclSO = await node.toAclSO();
  // 鉴权
  await nodeAclSO.authorize(member, 'importNode');
  return folder.bikafilePreview(tmpAttachmentId);
}

export async function importResource(ctx: ApiFetchRequestContext, req: T.NodeImportDTO): Promise<string> {
  const { spaceId, parentNodeId } = req;
  const { userId } = ctx.session!;
  const user = await UserSO.init(userId);
  const member = await user.getMember(spaceId);
  const space = await member.getSpace();
  const folder = parentNodeId ? await FolderSO.init(parentNodeId) : await space.getRootFolder();
  const isPrivateRootNode = parentNodeId && parentNodeId.startsWith(PRIVATE_ROOT_NODE_PREFIX);
  if (!isPrivateRootNode) {
    const node = folder.toNodeSO();
    const nodeAclSO = await node.toAclSO();
    // 鉴权
    await nodeAclSO.authorize(member, 'importNode');
  }
  let folderId = '';
  if (req.type === T.NodeImportTypeSchema.enum.bikafile) {
    const { tmpAttachmentId } = req;
    const tmpAttachment = await TmpAttachmentSO.initById(tmpAttachmentId);
    const scope = isPrivateRootNode ? 'PRIVATE' : folder.scope;
    folderId = await folder.importResourceFromBikafile(user, tmpAttachment, scope);
  }
  if (req.type === 'vika') {
    const integrationSO = await space.getIntegration(req.integrationId);
    folderId = await folder.importResourceFromVika(
      user,
      integrationSO,
      req.vikaResourceId.split(',').filter((i) => i),
    );
  }
  const importedFolder = await space.getNode(folderId);
  // 记录审计事件
  SpaceAuditLogSO.createFromRequestContext(ctx, {
    spaceId: space.id,
    type: 'node.import',
    id: importedFolder.id,
    name: JSON.stringify(importedFolder.name),
    parent: JSON.stringify(folder.name),
    spaceid: space.id,
  });
  return folderId;
}

export async function upgradeTemplateFolder(ctx: ApiFetchRequestContext, req: T.NodeUpgradeDTO) {
  const { spaceId, id } = req;
  const { userId } = ctx.session!;
  const user = await UserSO.init(userId);
  const member = await user.getMember(spaceId);
  const space = await member.getSpace();
  await space.getAdminRoleAclSO().authorize(member, 'upgradeTemplate');
  const node = await space.getNode(id);
  const templateFolder = await node.toResourceSO<TemplateFolderSO>();
  await templateFolder.upgradeTemplate(user);
  // 记录审计事件
  SpaceAuditLogSO.createFromRequestContext(ctx, {
    spaceId: space.id,
    type: 'node.upgrade',
    id: node.id,
    name: JSON.stringify(node.name),
    spaceid: space.id,
  });
}

export async function requestAccess(user: UserSO, req: T.NodeApplyAccessDTO) {
  const { id } = req;
  const node = await NodeSO.init(id);
  const nodeAclSO = await node.toAclSO();
  await nodeAclSO.createRequestAccess(user);
}

export async function sharePasswordValidate(req: T.NodeSharePasswordValidateDTO) {
  const { id, password } = req;
  const node = await NodeSO.init(id);
  const aclSO = await node.toAclSO();
  const isPublicAccess = await aclSO.isPublicAccess();
  if (!isPublicAccess) {
    throw new Error('node is not public access');
  }
  if (!aclSO.sharePassword) {
    throw new Error('share node has not password protected');
  }
  return aclSO.validateSharePassword(password);
}

export async function toggleShortURL(ctx: ApiFetchRequestContext, dto: { nodeId: string }) {
  const { userId } = ctx.session!;
  const user = await UserSO.init(userId);
  const node = await NodeSO.init(dto.nodeId);
  const member = await user.getMember(node.spaceId);
  // const space = await member.getSpace();
  const aclSO = await node.toAclSO();
  // 鉴权
  await aclSO.authorize(member, 'shareNode');

  let sUrlVO = await ShortURLSO.get('NODE_RESOURCE', dto.nodeId);
  if (!sUrlVO) sUrlVO = await ShortURLSO.create('NODE_RESOURCE', dto.nodeId);
  else {
    await ShortURLSO.delete(sUrlVO.model.id);
  }

  return sUrlVO;
}

export async function grantNodePermissionByMember(ctx: ApiFetchRequestContext, dto: T.NodeGrantPermissionDTO) {
  const { userId } = ctx.session!;
  const user = await UserSO.init(userId);
  const { spaceId, id: nodeId, unitIds, privilege } = dto;
  const member = await user.getMember(spaceId);
  const space = await member.getSpace();
  const node = await space.getNode(nodeId);
  const units = await space.getUnits(unitIds);
  const nodeAclSO = await node.toAclSO();
  await nodeAclSO.operator(user.id).grant(units, privilege);
  // 被授权单元的名称
  const granteesNames = units.map((grantee) => grantee.getName(user.locale));
  // 记录审计事件
  SpaceAuditLogSO.createFromRequestContext(ctx, {
    spaceId: space.id,
    type: 'share.grant',
    id: node.id,
    name: JSON.stringify(node.name),
    units: granteesNames.join(','),
    privilege,
  });
}

export async function grantNodePermissionByUser(user: UserSO, dto: Omit<T.NodeGrantPermissionDTO, 'spaceId'>) {
  const { id: nodeId, unitIds, privilege } = dto;
  const node = await NodeSO.init(nodeId);
  const nodeAclSO = await node.toAclSO();
  const space = await node.getSpace();
  const units = await space.getUnits(unitIds);
  if (!units.length) {
    throw new ServerError(errors.unit.units_not_match_in_space);
  }
  await nodeAclSO.operator(user.id).grantByUser(units, privilege);
  // 被授权单元的名称
  const granteesNames = units.map((grantee) => grantee.getName(user.locale));
  const auditLog = {
    spaceId: space.id,
    type: 'share.grant',
    id: node.id,
    name: JSON.stringify(node.name),
    units: granteesNames.join(','),
    privilege,
  };
  // 记录审计事件
  await db.log.write({
    kind: 'SPACE_AUDIT_LOG',
    // 转string
    data: JSON.stringify(auditLog),
    type: 'share.grant',
    userid: user.id,
    spaceid: space.id,
    client_ip: '',
    client_version: '',
    server_hostname: '',
    server_platform: '',
    createdat: new Date().toISOString(),
  });
}
