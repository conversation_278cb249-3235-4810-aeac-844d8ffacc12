import { generateNanoID } from 'basenext/utils/nano-id';
import { describe, it, expect } from 'vitest';
import { errors } from '@bika/contents/config/server/error/errors';
import { ServerError } from '@bika/contents/config/server/error/server_error';
import { NodeController } from '@bika/domains/node/apis';
import { FolderSO } from '@bika/domains/node/server/folder-so';
import { PRIVATE_ROOT_NODE_PREFIX } from '@bika/domains/node/server/types';
import { UserSO } from '@bika/domains/user/server/user-so';
import { MockContext } from '../../__tests__/mock';
import { AINodeSO } from '../../node-resources/ai-agent/ai-node-so';

describe('move-resource', () => {
  const initMirror = async (user: UserSO, folder: FolderSO, scope: 'PRIVATE' | 'SPACE') => {
    // create private resource
    const viewId = generateNanoID('viw');
    const database = await folder.createChildSimple(
      user,
      {
        resourceType: 'DATABASE',
        name: 'test',
        views: [
          {
            id: viewId,
            name: 'test',
            type: 'TABLE',
          },
        ],
      },
      { scope },
    );
    const mirror = await folder.createChildSimple(
      user,
      {
        resourceType: 'MIRROR',
        name: 'test',
        mirrorType: 'DATABASE_VIEW',
        databaseId: database.id,
        viewId,
      },
      {
        scope,
      },
    );
    return { database, mirror };
  };
  it('move-mirror--cannot move single mirror to public scope', async () => {
    const { space, rootFolder, user } = await MockContext.initUserContext();
    const { user: user2 } = await MockContext.initUserContext();
    const rootTeam = await space.getRootTeam();
    await space.joinUser(user2.id, rootTeam.id);
    // create private mirror resource
    const { mirror } = await initMirror(user, rootFolder, 'PRIVATE');

    const requestContext = await MockContext.createMockRequestContext(user);
    // move mirror to public scope, should throw error
    await expect(
      NodeController.moveResource(requestContext, {
        spaceId: space.id,
        id: mirror.id,
        data: {
          parentId: rootFolder.id,
        },
      }),
    ).rejects.toThrow(new ServerError(errors.node.cannot_move_node_to_public_scope));
  });

  it('move-mirror--can move private mirror to private root scope', async () => {
    const { space, rootFolder, user } = await MockContext.initUserContext();
    const { user: user2 } = await MockContext.initUserContext();
    const rootTeam = await space.getRootTeam();
    await space.joinUser(user2.id, rootTeam.id);
    const { mirror } = await initMirror(user, rootFolder, 'PRIVATE');
    const requestContext = await MockContext.createMockRequestContext(user);
    // move mirror to public scope, should throw error
    await expect(
      NodeController.moveResource(requestContext, {
        spaceId: space.id,
        id: mirror.id,
        data: {
          parentId: `${PRIVATE_ROOT_NODE_PREFIX}${rootFolder.id}`,
        },
      }),
    ).resolves.not.toThrow();
  });

  it('move-mirror--can move private mirror to team witch linked database in team ', async () => {
    const { space, rootFolder, user } = await MockContext.initUserContext();
    const { user: user2 } = await MockContext.initUserContext();
    const rootTeam = await space.getRootTeam();
    await space.joinUser(user2.id, rootTeam.id);
    const { database, mirror } = await initMirror(user, rootFolder, 'PRIVATE');
    const requestContext = await MockContext.createMockRequestContext(user);
    await NodeController.moveResource(requestContext, {
      spaceId: space.id,
      id: database.id,
      data: {
        parentId: rootFolder.id,
      },
    });
    await expect(
      NodeController.moveResource(requestContext, {
        spaceId: space.id,
        id: mirror.id,
        data: {
          parentId: rootFolder.id,
        },
      }),
    ).resolves.not.toThrow();
  });
});

describe('create-ai-agent', () => {
  it('create-ai-agent', async () => {
    const { space, rootFolder, user } = await MockContext.initUserContext();
    const requestContext = MockContext.createMockRequestContext(user);
    const node = await NodeController.createResource(requestContext, {
      spaceId: space.id,
      parentId: rootFolder.id,
      data: {
        name: 'StockNewsImpactAgent',
        description: 'An AI agent that tracks U.S. stock news and generates a 4-page impact report.',
        resourceType: 'AI',
        bo: {
          prompt:
            'You are an AI agent specialized in tracking U.S. stock news and generating a structured 4-page impact report. Your main task is to:\n\n1. Monitor real-time U.S. stock news from reliable sources.\n2. Analyze the sentiment and potential impact of each news item on stock markets.\n3. Generate a detailed, 4-page report summarizing key insights, including:\n   - Page 1: Summary of top 5 news items with impact scores\n   - Page 2: Sector-wise analysis (e.g., tech, healthcare, finance)\n   - Page 3: Stock-specific impact (top 10 stocks affected)\n   - Page 4: Predictive trends based on recent news patterns\n\nUse markdown formatting for clarity and ensure the report is concise yet comprehensive.',
          skillsets: [
            { kind: 'toolsdk', key: 'mcp-hacker-news', includes: [] },
            { kind: 'toolsdk', key: '@feedmob/singular-reporting', includes: ['create_report'] },
          ],
        },
      },
    });
    const aiNode = await AINodeSO.init(node.id);
    const bo = await aiNode.toBO();
    expect(bo.skillsets).toStrictEqual([
      { kind: 'toolsdk', key: 'mcp-hacker-news', includes: [] },
      { kind: 'toolsdk', key: '@feedmob/singular-reporting', includes: ['create_report'] },
    ]);
  });
});
