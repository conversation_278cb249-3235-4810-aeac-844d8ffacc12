import { Box } from '@mui/material';
import Image from 'next/image';
import React, { FC } from 'react';
import { VisibilityAdapter } from '@bika/domains/shared/client/components/visibility-adapter';
// import { AvatarImg, AvatarSize } from '@bika/ui/components/avatar/index';
import { AttachmentVO } from '@bika/types/attachment/vo';
import { AvatarLogo } from '@bika/types/system';
import { getAppEnv } from 'sharelib/app-env';
import { IconButton } from '@bika/ui/button';
import { AvatarImg, AvatarSize } from '@bika/ui/components/avatar/index';
import {
  useAttachmentUpload,
  CropShape,
  DEFAULT_DATA_LIST,
  ImageCropUpload,
} from '@bika/ui/components/image-crop-upload/index';
import EditOutlined from '@bika/ui/icons/components/edit_outlined';

export const CoverComponent: FC<{
  value?: AvatarLogo;
  name?: string;
  onChange?: (cover: AvatarLogo) => void;
  upload: (file: File) => Promise<AttachmentVO | undefined>;
}> = ({ upload, value, onChange, name }) => {
  const [showImageUpload, setShowImageUpload] = React.useState(false);
  const { unsplashDownload } = useAttachmentUpload();

  const env = getAppEnv();
  const imageSwitchEnabled = env === 'SELF-HOSTED' || env === 'INTEGRATION';

  return (
    <Box sx={{ position: 'relative' }}>
      {showImageUpload && (
        <ImageCropUpload
          // upload={upload}
          // unsplashDownload={unsplashDownload}
          onClose={() => {
            setShowImageUpload(false);
          }}
          // shape={PreviewShape.Square}
          previewShapeStyle={{
            width: '210px',
            height: '70px',
          }}
          upload={upload}
          config={{
            unsplash: {
              unsplashDownload,
            },
            preset: {
              presetPhotos: DEFAULT_DATA_LIST.map((url) => ({ url })),
            },
          }}
          cropShape={CropShape.Rectangle}
          confirm={(folderCover) => {
            onChange?.(folderCover);
            setShowImageUpload(false);
          }}
          allowTab={['ATTACHMENT', 'UNSPLASH', 'URL', 'PRESET']}
          value={
            value || {
              type: 'PRESET',
              url: DEFAULT_DATA_LIST[0],
            }
          }
          // initPreview={
          //   value ? (
          //     <AvatarImg
          //       name={name}
          //       avatar={value}
          //       customSize={AvatarSize.Size120}
          //       shape="SQUARE"
          //       style={{
          //         width: '210px !important',
          //         height: '70px !important',
          //       }}
          //     />
          //   ) : (
          //     <div className={'bg-[--bg-page] w-[210px] h-[70px] rounded flex justify-center items-center	'}>
          //       <Image src={CONST_FIRST_IMAGE} width={210} height={70} alt=""></Image>
          //     </div>
          //   )
          // }
        />
      )}
      <Box
        sx={{
          '&:hover': {
            '& > .edit': {
              opacity: 1,
            },
            '& > .MuiIconButton-root': {
              opacity: 1,
            },
          },
        }}
      >
        <VisibilityAdapter privilege="FULL_ACCESS">
          {imageSwitchEnabled && (
            <IconButton
              className={'edit'}
              onClick={() => setShowImageUpload(true)}
              sx={{
                position: 'absolute',
                top: 24,
                right: 24,
                // backgroundColor: 'rgba(255, 255, 255, 0.8)',
                backgroundColor: 'var(--bg-page)',
                opacity: 0,
                transition: 'opacity 0.2s',
                borderRadius: '50%',
                '&:hover': {
                  backgroundColor: 'var(--bg-page)',
                },
              }}
            >
              <EditOutlined />
            </IconButton>
          )}
        </VisibilityAdapter>
        {value == null ? (
          <Image
            src={DEFAULT_DATA_LIST[0]}
            // '/assets/images/form_page_header.jpeg'}
            alt={'image'}
            height={240}
            width={720}
            className={'w-full'}
          />
        ) : (
          <AvatarImg
            name={name}
            avatar={value}
            style={{
              height: '240px !important',
              width: '720px !important',
              borderBottomLeftRadius: '0px !important',
              borderBottomRightRadius: '0px !important',
            }}
            customSize={AvatarSize.Size120}
            shape="SQUARE"
          />
        )}
      </Box>
    </Box>
  );
};
