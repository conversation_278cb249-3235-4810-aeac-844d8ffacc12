import { Context } from 'hono';
import { checkChinaMainlandIP } from '@bika/domains/shared/server/utils/geoip/index';
import { SystemMetaVO } from '@bika/types/openapi/vo';
import { getAppEnv } from 'sharelib/app-env';

export async function getMeta(c: Context): Promise<SystemMetaVO> {
  return {
    version: process.env.VERSION,
    appEnv: getAppEnv(),
    hostname: process.env.APP_HOSTNAME,
    isFromCN: checkChinaMainlandIP(c.req.header('X-Forwarded-For')) ? true : undefined, // undefined让它从返回值里消失
    // 返回一些请求头
    headers: {
      'X-Vercel-IP-Country': c.req.header('X-Vercel-IP-Country'),
      'X-Vercel-IP-Country-Region': c.req.header('X-Vercel-IP-Country-Region'),
      'X-Vercel-IP-City': c.req.header('X-Vercel-IP-City'),
      'X-Forwarded-For': c.req.header('X-Forwarded-For'), // 通常是IP来源
      'User-Agent': c.req.header('User-Agent'), // 请求里的
      'Accept-Language': c.req.header('Accept-Language'), // 请求里的
      'CF-IPCountry': c.req.header('CF-IPCountry'),
    },
  } as SystemMetaVO;
}
