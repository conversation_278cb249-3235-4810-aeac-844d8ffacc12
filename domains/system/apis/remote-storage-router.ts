import { z } from 'zod';
import { protectedProcedure, router } from '@bika/server-orm/trpc';
import {
  RemoteStoragePropertySchema,
  RemoteStoragePropertyDatabaseViewFilterSchema,
  SupportAPIStorageTypeSchema,
  RemoteStoragePropertyDatabaseViewSortSchema,
  RemoteStoragePropertyDatabaseViewHiddenColumnSchema,
  RemoteStoragePropertyDatabaseViewRowGroupSchema,
  RemoteStoragePropertyKanbanViewHiddenGroupSchema,
  RemoteStoragePropertyDatabaseViewColWidthSchema,
  RemoteStoragePropertyDatabaseViewRowHeightSchema,
  DatabaseRemoteStoragePropertySchema,
} from '@bika/types/system/remote-storage';
import { FilterConditionMigrateAdapter } from '../../database/shared/filter-condition-migrate-adapter';
import { RemoteStorageSO } from '../server/remote-storage/remote-storage-so';

/**
 * Remote Storage，类似前端的Local Storage
 */
export const remoteStorageRouter = router({
  setItem: protectedProcedure
    .input(
      z.object({
        type: SupportAPIStorageTypeSchema,
        value: RemoteStoragePropertySchema,
      }),
    )
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      const userId = ctx.session!.userId;
      const { type, value } = input;
      if (type === 'KANBAN_VIEW_HIDDEN_GROUPS') {
        const { viewId } = RemoteStoragePropertyKanbanViewHiddenGroupSchema.parse(value);
        const target = { userId, viewId };
        return RemoteStorageSO.set(target, value);
      }
      if (type === 'DATABASE_VIEW_HIDDEN_COLUMNS') {
        const { viewId } = RemoteStoragePropertyDatabaseViewHiddenColumnSchema.parse(value);
        const target = { userId, viewId };
        return RemoteStorageSO.set(target, value);
      }
      if (type === 'DATABASE_VIEW_COL_WIDTH') {
        const { viewId } = RemoteStoragePropertyDatabaseViewColWidthSchema.parse(value);
        const target = { userId, viewId };
        return RemoteStorageSO.set(target, value);
      }
      if (type === 'DATABASE_VIEW_ROW_GROUP') {
        const { viewId } = RemoteStoragePropertyDatabaseViewRowGroupSchema.parse(value);
        const target = { userId, viewId };
        return RemoteStorageSO.set(target, value);
      }

      if (type === 'DATABASE_VIEW_SORT') {
        const { viewId } = RemoteStoragePropertyDatabaseViewSortSchema.parse(value);
        const target = { userId, viewId };
        return RemoteStorageSO.set(target, value);
      }
      if (type === 'DATABASE_VIEW_ROW_HEIGHT') {
        const { viewId } = RemoteStoragePropertyDatabaseViewRowHeightSchema.parse(value);
        const target = { userId, viewId };
        return RemoteStorageSO.set(target, value);
      }
      if (type === 'DATABASE_VIEW_FILTER') {
        const { viewId } = RemoteStoragePropertyDatabaseViewFilterSchema.parse(value);
        const target = { userId, viewId };
        return RemoteStorageSO.set(target, value);
      }
      return RemoteStorageSO.set(userId, value);
    }),

  getItemProperty: protectedProcedure
    .input(
      z.object({
        type: SupportAPIStorageTypeSchema,
        options: z
          .object({
            viewId: z.string(),
          })
          .optional(),
      }),
    )
    .output(z.object({ data: DatabaseRemoteStoragePropertySchema.optional() }))
    .query(async (opts) => {
      const { input, ctx } = opts;
      const userId = ctx.session!.userId;
      const { type, options } = input;
      const property = await RemoteStorageSO.getProperty(type, options ? { userId, ...options } : userId);
      const parsed = DatabaseRemoteStoragePropertySchema.safeParse(property);
      // 兼容`DATABASE_VIEW_FILTER`类型的property里的filter.conditions
      if (!parsed.success) {
        return { data: undefined };
      }
      const data = parsed.data;
      if (data.type === 'DATABASE_VIEW_FILTER' && data.filters && data.filters.conditions.length > 0) {
        // 迁移conditions到conds
        const newConditions = FilterConditionMigrateAdapter.migrate(data.filters.conditions ?? []);
        data.filters.conditions = [];
        data.filters.conds = newConditions;
      }
      return { data };
    }),

  removeItem: protectedProcedure
    .input(
      z.object({
        type: SupportAPIStorageTypeSchema,
        options: z.object({
          viewId: z.string(),
        }),
      }),
    )
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      const userId = ctx.session!.userId;
      const { type, options } = input;
      return RemoteStorageSO.delete(type, options ? { ...options, userId } : userId);
    }),

  /**
   * @deprecated， 不需要前端调用，后端智能调用
   */
  // setActiveSpaceNode: protectedProcedure
  //   .input(
  //     z.object({
  //       spaceId: z.string(),
  //       nodeId: z.string(),
  //     }),
  //   )
  //   .mutation(async (opts) => {
  //     const { input, ctx } = opts;
  //     const userId = ctx.session!.userId;
  //     return RemoteStorageSO.setUserLastActiveSpaceNode(userId, input.spaceId, input.nodeId);
  //   }),
});
