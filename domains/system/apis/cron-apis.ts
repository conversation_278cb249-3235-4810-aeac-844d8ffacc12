import { Hono } from 'hono';
import * as SiteAdminController from '@bika/domains/admin/apis/site-admin-controller';
import { AISearchSO } from '@bika/domains/ai/server/ai-search-so';
import { SubscriptionReportCursorSO, UsageLogSO, SubscriptionStateCheckCursorSO } from '@bika/domains/pricing/server';
import { getAppEnv } from 'sharelib/app-env';
import * as CronController from './cron-controller';

// cron route: /api/cron/*

const app = new Hono();

/**
 * 每天UTC 00:00
 */
app.get('/daily-sync', async (c) => {
  const sendTemplatesWarningEmails =
    getAppEnv() === 'INTEGRATION' ? await SiteAdminController.sendAllTemplatesWarningEmailToAll() : null;
  return c.json({
    message: 'daily executed sync',
    sendTemplatesWarningEmails,
  });
});

/**
 * 每分钟
 */
app.get('/minutely-sync', async (c) => {
  // 异步执行席位数定期上报
  SubscriptionReportCursorSO.scan();
  // 订阅状态定期检查
  SubscriptionStateCheckCursorSO.scan();
  // 执行用量汇总统计
  UsageLogSO.scanSummarizableLog();
  // 刷新twitter token
  CronController.refreshTwitterAccessToken();
  const automationRes = await CronController.scanRunnableSchedulers();
  // const count = await db.search.moveMongoDBtoES();
  return c.json({
    automation: automationRes,
    // olap: count,
  });
});

/**
 * 每小时
 */
app.get('/hourly-sync', async (_c) => {});

/**
 * 手动触发，跑一次全局重建es索引检查，所有节点检查，超级久的哦，每个节点间隔50ms
 */
app.get('/manually', async (_c) => {
  AISearchSO.rewriteAllNodeSearchIndex(50);
});

export default app;
