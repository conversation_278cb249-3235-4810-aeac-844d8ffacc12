import { generateNanoID } from 'basenext/utils/nano-id';
import dayjs from 'dayjs';
import { ActionSO } from '@bika/domains/automation/server/action-so';
import { TriggerSO } from '@bika/domains/automation/server/trigger-so';
import { MissionSO } from '@bika/domains/mission/server/mission-so';
import { ReminderSO } from '@bika/domains/reminder/server/reminder-so';
import { Logger, sliceArray } from '@bika/domains/shared/server';
import { UserSO } from '@bika/domains/user/server/user-so';
import { db, Prisma, PrismaPromise } from '@bika/server-orm';
import { Scheduler } from '@bika/types/system';
import { SchedulerRunTimeCalculator } from './scheduler-run-time-calculator';
import {
  AutomationActionRelationType,
  AutomationTriggerRelationType,
  DelayActionSchedulerProperty,
  ISchedulerCreateParam,
  MissionRelationType,
  ReminderRelationType,
  SchedulerModel,
  SchedulerRelationTypeEnums,
  SchedulerProperty,
} from './types';

/**
 * Scheduler是一个技术模块（不是业务模块），即：没有BO、没有VO、模板不可配，抽象模块。
 * 用于实现定时任务，是定时Trigger和Reminder的依赖具体实现。
 */
export class SchedulerSO {
  private readonly _model: SchedulerModel;

  private constructor(model: SchedulerModel) {
    this._model = model;
  }

  get model() {
    return this._model;
  }

  get id() {
    return this._model.id;
  }

  get relationId() {
    return this._model.relationId;
  }

  get relationType() {
    return this._model.relationType;
  }

  get runTime() {
    return this._model.runTime;
  }

  get property(): SchedulerProperty | undefined {
    return this.model.property ? (this.model.property as unknown as SchedulerProperty) : undefined;
  }

  toVO() {
    return {
      id: this._model.id,
      relationId: this.relationId,
    };
  }

  static initWithModel(model: SchedulerModel) {
    return new SchedulerSO(model);
  }

  static async findSchedulersByRelation(
    relationIds: string[],
    relationType?: SchedulerRelationTypeEnums,
    property?: Prisma.JsonNullableFilter,
  ): Promise<SchedulerSO[]> {
    if (relationIds.length === 0) {
      return [];
    }
    const schedulers = await db.prisma.scheduler.findMany({
      where: {
        relationType,
        relationId: { in: relationIds },
        property,
      },
    });
    return schedulers.map((scheduler) => this.initWithModel(scheduler));
  }

  static async countByRelationId(relationId: string): Promise<number> {
    return db.prisma.scheduler.count({
      where: { relationId },
    });
  }

  /**
   * 分批扫描所有scheduler，减少一次性查询的压力和内存占用，直到没有更多的scheduler。
   * @returns 总共扫描的scheduler数量和最后一批scheduler
   */
  static async batchScanRunnableSchedulers(): Promise<{ count: number; schedulers: SchedulerSO[] }> {
    let count: number = 0;
    let schedulers: SchedulerSO[];
    // 以现在作为时间节点，防止 do while 没退出，在里面 new Date() 一直匹配时间节点之后创建的scheduler
    // 节点之后创建的scheduler，交由下一次扫描去处理
    const now = new Date();
    do {
      schedulers = await this.scanRunnableSchedulers(now, 1000);
      count += schedulers.length;
    } while (schedulers.length === 1000);
    return { count, schedulers };
  }

  /**
   * 检查所有scheduler，有没有时间超过的
   *
   * @param now 筛选比这个时间更早的scheduler，进行执行，默认都是now，测试时可以传入一个时间，做模拟
   * @param limit 限制扫描的数量，一次性扫描太多会导致内存占用过高
   * @param relationType 可以指定关系类型，比如只查trigger的scheduler
   */
  static async scanRunnableSchedulers(
    now?: Date,
    limit?: number,
    relationType?: SchedulerRelationTypeEnums,
  ): Promise<SchedulerSO[]> {
    const runnableSchedulers = await db.prisma.scheduler.findMany({
      where: {
        runTime: {
          lt: now || new Date(), // less than or equal 小于现在时间
        },
        relationType,
      },
      take: limit,
      orderBy: [{ createdAt: 'asc' }],
    });
    const schedulers = [];
    for (const schedulerPO of runnableSchedulers) {
      const schedulerSO = this.initWithModel(schedulerPO);
      schedulers.push(schedulerSO);
      // 异步执行，不阻塞其他scheduler的执行
      schedulerSO.run();
    }
    return schedulers;
  }

  /**
   * SchedulerSO不会自己创建Scheduler
   * 获得创建Scheduler的数据库Input参数
   * 由子的XXXScheduler中间表创建时一同创建，一次性事务执行
   *
   * @returns
   * @param schedulerRelationType
   * @param schedulerRelationId
   * @param runTime
   */
  static async create(createParam: ISchedulerCreateParam): Promise<SchedulerSO> {
    const schedulerPO = await this.createSchedulerOperation(createParam);
    return this.initWithModel(schedulerPO);
  }

  /**
   * 根据BO对象创建Scheduler的数据库操作
   * @param createParam 创建参数
   */
  static createSchedulerOperationsWithTemplate(createParam: {
    user?: UserSO;
    relationId: string;
    relationType: SchedulerRelationTypeEnums;
    scheduler: Scheduler;
    now?: dayjs.Dayjs;
  }): PrismaPromise<SchedulerModel> | undefined {
    const schedulerCreateInput = this.buildSchedulerCreateInputWithTemplate(createParam);
    return schedulerCreateInput && this.createOperation(schedulerCreateInput);
  }

  /**
   * @param now 现在的时间是多少，可以传入虚构时间做测试
   */
  static buildSchedulerCreateInputWithTemplate(createParam: {
    user?: UserSO;
    relationId: string;
    relationType: SchedulerRelationTypeEnums;
    scheduler: Scheduler;
    now?: dayjs.Dayjs;
  }): Prisma.SchedulerCreateInput | undefined {
    const { user, relationId, relationType, scheduler, now } = createParam;
    const userTimezone = user && user.timeZone;
    const calc = SchedulerRunTimeCalculator.create(scheduler, userTimezone);
    const runTime = calc.calculateNextRunTime(now);
    // 下次执行时间，已经过去了，证明这个cron失效，不用创建了
    if (!runTime || runTime.toDate() < new Date()) {
      return undefined;
    }
    return this.buildSchedulerCreateInput({
      userId: user && user.id,
      relationId,
      relationType,
      runTime: runTime.toDate(),
    });
  }

  /**
   * 创建Scheduler的操作
   * @param createParam 创建参数
   */
  static createSchedulerOperation(createParam: ISchedulerCreateParam): PrismaPromise<SchedulerModel> {
    const schedulerCreateInput = this.buildSchedulerCreateInput(createParam);
    return this.createOperation(schedulerCreateInput);
  }

  static buildSchedulerCreateInput(createParam: ISchedulerCreateParam): Prisma.SchedulerCreateInput {
    const { userId, relationType, relationId, property, runTime } = createParam;
    return {
      id: generateNanoID('sch'),
      relationType,
      relationId,
      property: property && (property as unknown as Prisma.JsonObject),
      runTime,
      createdBy: userId,
    };
  }

  static createOperation(input: Prisma.SchedulerCreateInput): PrismaPromise<SchedulerModel> {
    return db.prisma.scheduler.create({
      data: input,
    });
  }

  static createMany(inputs: Prisma.SchedulerCreateManyInput[]): PrismaPromise<Prisma.BatchPayload> {
    return db.prisma.scheduler.createMany({
      data: inputs,
    });
  }

  static async deleteByIds(schedulerIds: string[]) {
    if (schedulerIds.length === 0) {
      return;
    }

    await Promise.all(
      sliceArray<string>(schedulerIds).map(async (ids) =>
        db.prisma.scheduler.deleteMany({
          where: {
            id: { in: ids },
          },
        }),
      ),
    );
  }

  static async deleteByRelationIds(relationIds: string[], relationType?: SchedulerRelationTypeEnums) {
    if (relationIds.length === 0) {
      return;
    }
    await Promise.all(
      sliceArray<string>(relationIds).map(async (ids) =>
        db.prisma.scheduler.deleteMany({
          where: {
            relationType,
            relationId: { in: ids },
          },
        }),
      ),
    );
  }

  /**
   * 是否已经可以执行了？即now > runTime
   */
  checkRunnable(date: Date = new Date()): boolean {
    return date > this._model.runTime;
  }

  async run() {
    const relationId = this.relationId;
    switch (this.relationType) {
      case AutomationActionRelationType: {
        const actionSO = await ActionSO.initMaybeNull(relationId);
        /**
         * TODO
         * 1. action可能已经被删除了，需要更新运行历史状态
         * 2. 自动化可能关闭了，不再运行，需要更新运行历史状态
         */
        if (actionSO) {
          actionSO.runSchedulers(this.model.property as unknown as DelayActionSchedulerProperty);
        }
        break;
      }
      case AutomationTriggerRelationType: {
        const triggerSO = await TriggerSO.findById(relationId);
        if (triggerSO) {
          // trigger.runSchedulers() 内会删除scheduler
          triggerSO.runSchedulers();
          return;
        }
        // trigger可能已经被删除了，但是scheduler还在
        Logger.warn(`SchedulerSO.scanRunnableSchedulers: Trigger not found: ${relationId}`);
        break;
      }
      case ReminderRelationType: {
        const exist = await ReminderSO.exist(relationId);
        if (exist) {
          const reminderSO = await ReminderSO.init(relationId);
          // reminder.runSchedulers() 内会删除scheduler
          reminderSO.runSchedulers();
          return;
        }
        Logger.warn(`SchedulerSO.scanRunnableSchedulers: Reminder not found: ${relationId}`);
        break;
      }
      case MissionRelationType: {
        const mission = await MissionSO.initMaybeNull(relationId);
        if (mission) {
          mission.runSchedulers();
        }
        break;
      }
      default:
        throw new Error(`Unknown SchedulerRelationType: ${this.relationType}`);
    }
    await SchedulerSO.deleteOperation(this.id);
  }

  static deleteOperation(schedulerId: string) {
    return db.prisma.scheduler.delete({
      where: {
        id: schedulerId,
      },
    });
  }
}
