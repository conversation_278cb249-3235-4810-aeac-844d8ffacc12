import assert from 'assert';
import os from 'os';
import dayjs from 'dayjs';
import { SpaceAuditLogFormatters } from '@bika/contents/config/server/log/space-audit-log-formatter';
import { UnitFactory } from '@bika/domains/unit/server/unit-factory';
import { parseAttributesFromRequest } from '@bika/server-orm/utils';
import { db } from '@bika/server-orm';
import { Locale } from '@bika/types/i18n/bo';
import { SpaceAuditLogData, SpaceAuditLogDataSchema } from '@bika/types/log/bo';
import { SpaceAuditLogDTO } from '@bika/types/log/dto';
import { SpaceAuditLogVO } from '@bika/types/log/vo';
import { Pagination, PaginationSchema } from '@bika/types/shared';
import { SpaceAuditLog } from '@bika/types/system';
import { ApiFetchRequestContext } from '@bika/types/user/vo';

export class SpaceAuditLogSO {
  static create(model: SpaceAuditLog) {
    db.log.write(model);
  }

  static async createFromRequestContext(ctx: ApiFetchRequestContext, dto: SpaceAuditLogDTO) {
    const { req, session } = ctx;
    if (!session?.userId) {
      // 无用户信息，不记录审计日志
      return;
    }
    if (!req) {
      // 无请求信息，不记录审计日志
      return;
    }
    const sessionAttributes = parseAttributesFromRequest(req.headers);
    await db.log.write({
      kind: 'SPACE_AUDIT_LOG',
      // 转string
      data: JSON.stringify(dto),
      type: dto.type,
      userid: session.userId,
      spaceid: dto.spaceId,
      client_ip: sessionAttributes.ip ?? '',
      client_version: sessionAttributes.version ?? '',
      server_hostname: os.hostname(),
      server_platform: os.platform(),
      createdat: dayjs().toISOString(),
    });
  }

  // private static transformObject<T extends Record<string, unknown>>(data: T): Record<string, unknown> {
  //   const result: Record<string, unknown> = {}; // Use Partial<U> for incremental construction
  //   Object.keys(data).forEach((key) => {
  //     const value = data[key as keyof T] as unknown; // Convert to 'unknown' first to allow safe casting
  //     const [prefix, suffix] = key.split('_');
  //     if (suffix && prefix) {
  //       // Ensure that the prefix key in result is a nested object
  //       if (typeof result[prefix] !== 'object' || result[prefix] === null) {
  //         result[prefix] = {}; // Type-safe assertion to create the nested object
  //       }

  //       // Safely assign the nested value
  //       (result[prefix] as Record<string, unknown>)[suffix] = value;
  //     } else {
  //       result[key] = value; // Directly assign the value if it isn't a nested object
  //     }
  //   });

  //   return result;
  // }

  static async list(
    locale: Locale,
    q: { spaceId: string; startDate: Date; endDate: Date; ip?: string },
    pagination?: Pagination,
  ) {
    const { spaceId, startDate, endDate, ip } = q;
    const { pageNo, pageSize } = PaginationSchema.parse(pagination ?? {});

    let where: Record<string, string> = { spaceid: spaceId };

    if (ip && ip.length > 0) {
      where = { ...where, client_ip: ip };
    }

    const result = await db.log.paginationSearch<SpaceAuditLog>('SPACE_AUDIT_LOG', {
      where,
      startTime: startDate,
      endTime: endDate,
      skip: pageSize * (pageNo - 1),
      take: pageSize,
    });

    // const logs = result.records
    //   .map((row) => {
    //     // const transformedData = this.transformObject<Record<string, unknown>>(row);
    //     const parsed = SpaceAuditLogSchema.safeParse(row);
    //     if (parsed.success) {
    //       return parsed.data;
    //     }
    //     return undefined;
    //   })
    //   .filter((log) => log !== undefined);

    // console.log(logs);
    const userIds = result.records.map((log) => log.userid).filter(Boolean);
    const members = userIds.length > 0 ? await UnitFactory.findMembers(userIds, spaceId) : [];

    const list: SpaceAuditLogVO[] = result.records
      .map((log) => {
        const foundMembers = members.filter((member) => log.userid === member.userId);
        let data: SpaceAuditLogData | undefined;
        if (log.data) {
          const parsed = SpaceAuditLogDataSchema.safeParse(JSON.parse(log.data));
          if (parsed.success) {
            data = parsed.data;
          } else {
            console.warn('Failed to parse audit log data, maybe deprecated: ', parsed.error);
            return undefined;
          }
        }

        assert(data, 'data should not be undefined');

        const { name: nameFormatter, description: descFormatter } = SpaceAuditLogFormatters[log.type];

        if (foundMembers.length > 0) {
          const foundMember = foundMembers[0];
          return {
            name: nameFormatter(data, locale),
            description: descFormatter(data, locale),
            clientIP: log.client_ip,
            actor: {
              id: foundMember.id,
              name: foundMember.getName(),
              email: foundMember.email,
              avatar: foundMember.avatar,
            },
            createdAt: log.createdat,
          };
        }
        return {
          name: nameFormatter(data, locale),
          description: descFormatter(data, locale),
          clientIP: log.client_ip,
          actor: {
            id: 'unknown',
            name: 'unknown',
            email: 'unknown',
          },
          createdAt: log.createdat,
        };
      })
      .filter((log) => log !== undefined);

    return {
      list,
      pagination: { pageNo, pageSize, total: result.total },
    };
  }
}
