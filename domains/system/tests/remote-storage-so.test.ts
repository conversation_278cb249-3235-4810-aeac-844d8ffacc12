import dayjs from 'dayjs';
import { expect, test } from 'vitest';
import { generateEmail, generateRandomString } from '@bika/domains/shared/server';
import { generateNanoID } from 'basenext/utils/nano-id';
import { DateTimeSO } from '@bika/types/system';
import { RemoteStoragePropertyDatabaseViewFilterSchema } from '@bika/types/system/remote-storage';
import { RemoteStorageHelper } from '../server/remote-storage/remote-storage-helper';
import { RemoteStorageSO } from '../server/remote-storage/remote-storage-so';

test('Remote Storage for empty key (system)', async () => {
  const now = dayjs();
  const later3Minutes = now.add(3, 'minute');
  const ago3Minutes = now.subtract(3, 'minute');

  // const bars = await RemoteStorageSO.getProperty('WEBSITE_NOTIFICATION_BARS', 'SYSTEM');
  // expect(bars).toBeUndefined();
  await RemoteStorageHelper.websiteNotificationBars.save([
    {
      html: 'test',
    },
    {
      html: 'during',
      startAt: now.toISOString(),
      endAt: now.add(3, 'minute').toISOString(),
    },
    {
      html: 'later',
      startAt: now.add(3, 'minute').toISOString(),
      endAt: now.add(4, 'minute').toISOString(),
    },
    {
      html: 'ago',
      startAt: now.subtract(3, 'minute').toISOString(),
      endAt: now.subtract(4, 'minute').toISOString(),
    },
  ]);
  const bars2 = await RemoteStorageHelper.websiteNotificationBars.list();
  expect(bars2).toBeDefined();
  expect(bars2!.length).toBe(2);
});

test('Remote Storage as Local Storage Test', async () => {
  const mockUserId = 'test_user_id';
  await RemoteStorageSO.delete('DATABASE_VIEW_FILTER', mockUserId);

  // 没有
  const getValue = await RemoteStorageSO.getProperty('DATABASE_VIEW_FILTER', mockUserId);
  expect(getValue).toBeUndefined();

  const filtersSet = await RemoteStorageSO.set(mockUserId, {
    type: 'DATABASE_VIEW_FILTER',
    viewId: 'test_view_id',
    filters: {
      conjunction: 'And',
      conditions: [],
    },
  });
  expect(filtersSet!.type).toBe('DATABASE_VIEW_FILTER');

  // 有了
  const getValue2 = await RemoteStorageSO.getProperty('DATABASE_VIEW_FILTER', mockUserId);
  expect(getValue2).not.toBeNull();
  const props = RemoteStoragePropertyDatabaseViewFilterSchema.parse(getValue2);
  expect(props.filters).toBeDefined();
});

test('Verification Code Storage Test', async () => {
  const email = generateEmail();
  const code = generateRandomString(6);
  const type = 'MAIL_VERIFICATION_CODE';
  const newCode = await RemoteStorageHelper.verificationCode.create(type, email, code);
  expect(newCode).toBeDefined();
  const verificationCode101 = await RemoteStorageSO.getVerificationCode(type, email);
  expect(verificationCode101).toBe(code);

  // 11分钟后
  const now = DateTimeSO.now().toDayJS();
  const elevenMinuteLater = now.add(11, 'minute');
  const verificationCode102 = await RemoteStorageSO.getVerificationCode(type, email, elevenMinuteLater.toDate());
  expect(verificationCode102).toBeNull();

  // 再次创建一个
  const code2 = generateRandomString(6);
  await RemoteStorageHelper.verificationCode.create(type, email, code2);
  const match2 = await RemoteStorageSO.matchVerificationCode(type, email, code2);
  expect(match2).toBeTruthy();
});

test('User Last Active Space Storage Test', async () => {
  const userId = generateNanoID('test_usr');
  const spaceId = generateNanoID('test_spc');
  const nullActiveSpaceId = await RemoteStorageSO.getUserLastActiveSpace(userId);
  expect(nullActiveSpaceId).toBeUndefined();

  await RemoteStorageSO.saveOrUpdateUserLastActiveSpace(userId, spaceId);
  const activeSpaceId = await RemoteStorageSO.getUserLastActiveSpace(userId);
  expect(activeSpaceId).toBe(spaceId);

  const spaceId2 = generateNanoID('test_spc');
  await RemoteStorageSO.saveOrUpdateUserLastActiveSpace(userId, spaceId2);
  const activeSpaceId2 = await RemoteStorageSO.getUserLastActiveSpace(userId);
  expect(activeSpaceId2).toBe(spaceId2);
});
