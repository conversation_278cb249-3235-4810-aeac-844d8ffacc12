import { z } from 'zod';
import { UnitFactory } from '@bika/domains/unit/server/unit-factory';
import { UserSO } from '@bika/domains/user/server/user-so';
import { publicProcedure, protectedProcedure, router } from '@bika/server-orm/trpc';
import { MemberListDTOSchema, MemberInfoDTOSchema, MemberUpdateDTOSchema } from '@bika/types/unit/dto';
import { MemberVOSchema } from '@bika/types/unit/vo';
import { MemberController } from './member-controller';

export const memberRouter = router({
  list: protectedProcedure.input(MemberListDTOSchema).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return MemberController.list(user, input);
  }),

  info: protectedProcedure.input(MemberInfoDTOSchema).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return MemberController.info(user, input);
  }),

  batchGetByUserIds: publicProcedure
    .input(z.object({ spaceId: z.string(), userIds: z.string().array().max(20) }))
    .output(z.array(MemberVOSchema))
    .query(async ({ input }) => {
      const { spaceId, userIds } = input;
      const members = await UnitFactory.findMembers(userIds, spaceId);
      return Promise.all(members.map((member) => member.toVO()));
    }),

  update: protectedProcedure.input(MemberUpdateDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    return MemberController.update(user, input);
  }),

  getTeams: protectedProcedure
    .input(
      z.object({
        spaceId: z.string(),
        memberId: z.string(),
      }),
    )
    .query(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      return MemberController.getTeams(user, input.spaceId, input.memberId);
    }),

  getRoles: protectedProcedure
    .input(
      z.object({
        spaceId: z.string(),
        memberId: z.string(),
      }),
    )
    .query(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      return MemberController.getRoles(user, input.spaceId, input.memberId);
    }),

  leaveSpace: protectedProcedure.input(z.object({ spaceId: z.string() })).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return MemberController.leaveSpace(user, input.spaceId);
  }),

  kickMembers: protectedProcedure
    .input(z.object({ spaceId: z.string(), memberIds: z.string().array().max(200) }))
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      return MemberController.kickMembers(user, input.spaceId, input.memberIds);
    }),
});
