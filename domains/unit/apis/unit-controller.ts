import axios, { AxiosResponse } from 'axios';
import { ServerError, errors } from '@bika/contents/config/server/error';
import type { TeamSO } from '@bika/domains/unit/server/team-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { OutgoingWebhook as OutgoingWebhookPO } from '@bika/server-orm';
import { ServerEventType } from '@bika/types/events/bo';
import { LocaleType } from '@bika/types/system';
import { getAppEnv, isInCI } from 'sharelib/app-env';
import { UnitListDTO, TeamSubListDTO, OutgoingContactListDTO, InviteOutgoingContactDTO } from '@bika/types/unit/dto';
import { TeamSubListPageItemVO, TeamSubListPageVO, UnitPaginationVO, UnitRenderOpts } from '@bika/types/unit/vo';
import { OutgoingWebhookSO } from '../../event/server/event/outgoing-webhook-so';

export class UnitController {
  static async list(user: UserSO, req: UnitListDTO): Promise<UnitPaginationVO> {
    const { spaceId, name, ids, isGuest, pageNo, pageSize } = req;
    // check operate user is in space
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    // await space.toAcl().authenticate(member, 'readMember');
    const { pagination, list } = await space.findUnits({ name, ids, isGuest }, { pageNo, pageSize });
    const data = await Promise.all(list.map((unit) => unit.toVO({ locale: user.locale, withDetail: true })));
    return { pagination, data };
  }

  /**
   * combined unit(member + team)  page
   */
  static async getTeamSubList(user: UserSO, req: TeamSubListDTO): Promise<TeamSubListPageVO> {
    const { spaceId, teamId, pageNo, pageSize } = req;
    // check operate user is in space
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    // await space.toAcl().authenticate(member, 'readMember');

    const team = await space.getTeam(teamId);
    const data = await this.getCombinedUnitPageRecords(team, pageNo, pageSize, user.locale);
    return {
      pagination: { pageNo, pageSize, total: data.length },
      data,
    };
  }

  static async getCombinedUnitPageRecords(
    team: TeamSO,
    pageNo: number,
    pageSize: number,
    locale?: LocaleType,
  ): Promise<TeamSubListPageItemVO[]> {
    const memberCount = await team.getDirectMembersCount();
    const beginPos = (pageNo - 1) * pageSize;

    // 分页区间（[]示意）均在成员范围内
    // m1 [m2 m3] m4 t1 t2 ...
    // m1 m2 [m3 m4] t1 t2 ...
    const hasChildren = await team.hasChildren();
    if (pageNo * pageSize <= memberCount || !hasChildren) {
      const members = await team.getDirectMembers(pageSize, beginPos);
      return Promise.all(members.map((m) => m.toVO({ locale })));
    }

    // 分页区间（[]示意）均在部门范围内
    // m1 m2 ... t1 [t2 t3] t4
    // m1 m2 ... [t1 t2 t3] t4
    if (beginPos >= memberCount) {
      const children = await team.getChildren();
      if (beginPos >= memberCount + children.length) {
        return [];
      }
      const teams = await team.getChildTeams(pageSize, beginPos - memberCount);
      return Promise.all(teams.map((t) => t.toVO({ locale, withDetail: true })));
    }

    // 分页区间（[]示意）包含部分成员和部分部门
    // m1 m2 [m3 t1] t2 t3 ...
    const members = await team.getDirectMembers(pageSize, (pageNo - 1) * pageSize);
    const memberVOList = await Promise.all(members.map((m) => m.toVO()));
    // 补充部门
    const teams = await team.getChildTeams(pageSize - members.length, 0);
    const teamVOs = await Promise.all(teams.map((t) => t.toVO({ locale, withDetail: true })));
    return [...memberVOList, ...teamVOs];
  }

  static async outgoingContactList(user: UserSO, req: OutgoingContactListDTO): Promise<UnitPaginationVO> {
    const { type, name, pageNo, pageSize, spaceId, teamId } = req;
    const outgoingWebhooks = await this.findOutgoingWebhook('ON_MEMBER_INVITE', spaceId);
    // use the last outgoing webhook
    const callbackURL = outgoingWebhooks[outgoingWebhooks.length - 1].callbackURL;
    const response = await axios
      .get(callbackURL, {
        params: {
          type,
          name,
          pageNo,
          pageSize,
          teamId: teamId || '',
        },
        headers: {
          'Content-Type': 'application/json',
          userId: user.id,
          spaceId,
        },
      })
      .catch((error) => {
        console.error(`call outgoing webhook error: ${callbackURL}`, error);
        throw new ServerError(errors.space.call_outgoing_webhook_error, {
          error,
        });
      });
    return this.handleOutgoingWebhookResponse(response);
  }

  static async inviteOutgoingUnits(user: UserSO, req: InviteOutgoingContactDTO) {
    const { spaceId, teamIds, userIds, roleIds, nodeId } = req;
    const outgoingWebhooks = await this.findOutgoingWebhook('DO_MEMBER_INVITE', spaceId);
    const callbackURL = outgoingWebhooks[outgoingWebhooks.length - 1].callbackURL;
    const response = await axios
      .post(
        callbackURL,
        {
          spaceId,
          teamIds,
          userIds,
          roleIds,
          nodeId,
        },
        {
          headers: {
            'Content-Type': 'application/json',
            userId: user.id,
            spaceId,
          },
        },
      )
      .catch((error) => {
        console.error(`call outgoing webhook error: ${callbackURL}`, error);
        throw new ServerError(errors.space.call_outgoing_webhook_error, {
          error,
        });
      });
    return this.handleOutgoingWebhookResponse(response);
  }

  static async findOutgoingWebhook(type: ServerEventType, spaceId: string): Promise<OutgoingWebhookPO[]> {
    const outgoingWebhooks = await OutgoingWebhookSO.listPOByEventType(type);
    const appEnv = getAppEnv();
    const filteredOutgoingWebhooks = outgoingWebhooks.filter((item) => {
      if (item.relationType === 'SPACE' && item.relationId === spaceId) {
        return true;
      }
      // isInCI() use for test
      if (item.relationType === 'SITE_ADMIN' && (appEnv === 'SELF-HOSTED' || appEnv === 'LOCAL' || isInCI())) {
        return true;
      }
      return false;
    });
    if (!filteredOutgoingWebhooks.length) {
      throw new ServerError(errors.space.outgoing_webhook_not_found, {
        webhookType: type,
      });
    }
    return filteredOutgoingWebhooks;
  }

  private static handleOutgoingWebhookResponse(response: AxiosResponse) {
    if (!response.status || response.status !== 200) {
      throw new ServerError(errors.space.call_outgoing_webhook_error, {
        error: response.data,
      });
    }
    return response.data;
  }
}
