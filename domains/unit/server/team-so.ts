import _ from 'lodash';
import { generateNanoID } from 'basenext/utils/nano-id';
import { ServerError, errors } from '@bika/contents/config/server/error';
import { SpaceSO } from '@bika/domains/space/server/space-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { db, Prisma, PrismaPromise, PrismaTransactionFn, UnitType } from '@bika/server-orm';
import { MemberCellValue } from '@bika/types/database/vo';
import { OpenAPIMemberCellValue } from '@bika/types/openapi/vo';
import { Pagination, PaginationInfo, PaginationSchema } from '@bika/types/shared';
import { defaultGuestRootTeamName } from '@bika/types/space/default';
import { iStringParse } from '@bika/types/system';
import { TeamVO, UnitRenderOpts } from '@bika/types/unit/vo';
import { MemberSO } from './member-so';
import { teamIncludeUnitOnly, TeamIncludeUnitModel, unitMembersOnTeamsIncludeMemberOnly, TeamModel } from './types';
import { UnitFactory } from './unit-factory';
import { UnitSO } from './unit-so';

const { uniq } = _;

export const ROOT_TEAM_NAME = 'ROOT TEAM';

export class TeamSO extends UnitSO {
  private _children?: TeamSO[];

  private _model: TeamIncludeUnitModel;

  /**
   * constructor.
   * @param model unit team model
   * @private private constructor
   */
  private constructor(model: TeamIncludeUnitModel) {
    super(model.unit);
    this._model = model;
  }

  private get model() {
    return this._model;
  }

  // override get name() {
  //   return this.model.name;
  // }

  override getName(): string {
    return iStringParse(this.model.name);
  }

  get parentId(): string | null {
    return this.model.parentId;
  }

  get isRootTeam() {
    return this.parentId === null;
  }

  /**
   * 是否是访客所属部门
   */
  get isGuest(): boolean {
    return this.model.isGuest;
  }

  async toVO(opts?: UnitRenderOpts): Promise<TeamVO> {
    let memberCount: number | undefined;
    let path: string | undefined;
    if (opts?.withDetail) {
      [memberCount, path] = await Promise.all([this.countMemberCounts(), this.getFullPathName()]);
    }
    if (this.isRootTeam) {
      this.model.name = this.isGuest ? defaultGuestRootTeamName(opts?.locale) : await SpaceSO.getNameById(this.spaceId);
    }
    const children: TeamVO[] | undefined = this._children
      ? await Promise.all(
          this._children.map(async (child) => ({
            id: child.id,
            name: child.getName(),
            type: 'Team',
            isGuest: child.isGuest,
            children: [],
            createdAt: child.model.createdAt.toISOString(),
          })),
        )
      : undefined;

    return {
      id: this.id,
      name: this.getName(),
      type: 'Team',
      parentId: this.parentId ?? undefined,
      memberCount,
      path,
      isGuest: this.isGuest,
      createdAt: this.model.createdAt.toISOString(),
      children,
    };
  }

  async toCellValue(): Promise<MemberCellValue> {
    return {
      id: this.id,
      name: this.getName(),
    };
  }

  toOpenAPICellValue(_opts?: UnitRenderOpts): OpenAPIMemberCellValue {
    return {
      id: this.id,
      type: 'Team',
      name: this.getName(),
    };
  }

  /**
   * init with model
   * @param model unit team model
   */
  static initWithModel(model: TeamIncludeUnitModel): TeamSO {
    return new TeamSO(model);
  }

  /**
   * init with id
   * @param teamId team id
   */
  static async init(teamId: string): Promise<TeamSO> {
    const teamPO = await db.prisma.unitTeam.findUnique({
      where: {
        id: teamId,
      },
      include: teamIncludeUnitOnly,
    });
    if (!teamPO) {
      throw new ServerError(errors.unit.team_not_found);
    }
    if (!teamPO.parentId) {
      // 根部门,将空间站名称同步给当前对象的部门名称
      teamPO.name = await SpaceSO.getNameById(teamPO.unit.spaceId);
    }
    return this.initWithModel(teamPO);
  }

  static async initMany(teamIds: string[]): Promise<TeamSO[]> {
    const teamPOs = await db.prisma.unitTeam.findMany({
      where: {
        id: {
          in: teamIds,
        },
      },
      include: teamIncludeUnitOnly,
    });
    return teamPOs.map((po) => this.initWithModel(po));
  }

  static async findPOsByIds(teamIds: string[]): Promise<TeamModel[]> {
    const teamPOs = await db.prisma.unitTeam.findMany({
      where: {
        id: {
          in: teamIds,
        },
      },
    });
    return teamPOs;
  }

  private async reloadChildren(): Promise<TeamSO[]> {
    this._children = await this.getChildTeams();
    return this._children;
  }

  async getChildTeams(take?: number, skip?: number): Promise<TeamSO[]> {
    const teamsPOs = await db.prisma.unitTeam.findMany({
      where: { parentId: this.id },
      include: teamIncludeUnitOnly,
      orderBy: { createdAt: 'asc' },
      take,
      skip,
    });
    return teamsPOs.map((po) => TeamSO.initWithModel(po));
  }

  async hasChildren(): Promise<boolean> {
    const count = await db.prisma.unitTeam.count({
      where: {
        parentId: this.id,
      },
    });
    return count > 0;
  }

  async getChildren(useCache: boolean = true): Promise<TeamSO[]> {
    if (this._children && useCache) {
      return this._children;
    }
    return this.reloadChildren();
  }

  async addMembers(user: UserSO, memberIds: string[]) {
    if (this.isRootTeam) {
      throw new Error('Root team cannot add member.');
    }
    // check member is in same space
    const space = await this.getSpace();
    const members = await space.getMembers(memberIds);
    if (members.length !== memberIds.length) {
      throw new Error('some member are not in the this space.');
    }
    const isTypeMatch = members.every((m) => m.isGuest === this.isGuest);
    if (!isTypeMatch) {
      throw new Error('Member type does not match team type.');
    }
    const rootTeam = await space.getRootTeam(this.isGuest);
    const membersOnTeams = await db.prisma.unitMembersOnTeams.findMany({
      where: {
        memberId: {
          in: memberIds,
        },
      },
    });
    const operations: PrismaPromise<unknown>[] = [];
    // 当前部门下的成员
    const originMembers = await this.getDirectMembers();
    for (const memberId of memberIds) {
      const exist = originMembers.some((m) => m.id === memberId);
      if (!exist) {
        // 不存在才需要添加进去
        operations.push(this.addMemberOperation(user, memberId));
        const existsInRootTeam = membersOnTeams
          .filter((membersOnTeam) => membersOnTeam.memberId === memberId)
          .some((membersOnTeam) => membersOnTeam.teamId === rootTeam.id);
        if (existsInRootTeam) {
          // 如果成员原来只在根部门下，需要解除关联
          operations.push(rootTeam.deleteMemberOperation(memberId));
        }
      }
    }
    await db.prisma.$transaction(operations);
  }

  async removeMembers(user: UserSO, memberIds: string[]) {
    if (this.isRootTeam) {
      throw new Error('Root team cannot remove member.');
    }
    // check member is in same space
    const space = await this.getSpace();
    const members = await space.getMembers(memberIds);
    const memberNotExistThisSpace = members.find((m) => m.spaceId !== this.spaceId);
    if (memberNotExistThisSpace) {
      throw new Error(`Member ${memberNotExistThisSpace.id} is not in this space.`);
    }
    if (members.length !== memberIds.length) {
      memberIds.forEach((memberId) => {
        if (!members.some((m) => m.id === memberId)) {
          throw new Error(`Member ${memberId} does not exist.`);
        }
      });
    }

    const pos = await db.prisma.unitMembersOnTeams.findMany({
      where: {
        memberId: {
          in: memberIds,
        },
      },
    });
    const rootTeam = await space.getRootTeam(this.isGuest);
    const operations = [];
    for (const memberId of memberIds) {
      const memberTeamIds = pos.filter((po) => po.memberId === memberId).map((po) => po.teamId);
      // 不在该部门中，不需要删除
      if (memberTeamIds.includes(this.id)) {
        operations.push(this.deleteMemberOperation(memberId));
        // 如果成员原来只有一个部门，需要重新关联到根部门
        if (memberTeamIds.length <= 1) {
          operations.push(rootTeam.addMemberOperation(user, memberId));
        }
      }
    }
    await db.prisma.$transaction(operations);
  }

  /**
   * create team under this team
   * @param userId user id
   * @param name team name
   * @returns Team object
   */
  async createTeam(userId: string, name: string): Promise<TeamSO> {
    const id = UnitFactory.generateUnitId(UnitType.TEAM);
    const subTeamModel = await db.prisma.unitTeam.create({
      data: {
        parent: {
          connect: {
            id: this.id,
          },
        },
        name,
        isGuest: this.isGuest,
        createdBy: userId,
        updatedBy: userId,
        unit: {
          create: {
            id,
            type: UnitType.TEAM,
            spaceId: this.spaceId,
            createdBy: userId,
            updatedBy: userId,
          },
        },
      },
      include: teamIncludeUnitOnly,
    });
    if (!subTeamModel) {
      throw new Error('create team failed.');
    }
    await this.reloadChildren();
    return TeamSO.initWithModel(subTeamModel);
  }

  async createSubTeam(data: { userId: string; id?: string; name: string }): Promise<TeamSO> {
    const { userId, id, name } = data;
    const teamId = id ?? UnitFactory.generateUnitId(UnitType.TEAM);
    const subTeamModel = await db.prisma.unitTeam.create({
      data: {
        parent: {
          connect: {
            id: this.id,
          },
        },
        name,
        isGuest: this.isGuest,
        createdBy: userId,
        updatedBy: userId,
        unit: {
          create: {
            id: teamId,
            type: UnitType.TEAM,
            spaceId: this.spaceId,
            createdBy: userId,
            updatedBy: userId,
          },
        },
      },
      include: teamIncludeUnitOnly,
    });
    if (!subTeamModel) {
      throw new Error('create team failed.');
    }
    await this.reloadChildren();
    return TeamSO.initWithModel(subTeamModel);
  }

  async update(data: Pick<Prisma.UnitTeamUpdateInput, 'name' | 'updatedBy'>): Promise<void> {
    this._model = await db.prisma.unitTeam.update({
      where: {
        id: this.id,
      },
      data,
      include: teamIncludeUnitOnly,
    });
  }

  protected async deleteInternal(): Promise<PrismaTransactionFn> {
    return async (transaction) => {
      await transaction.unitMembersOnTeams.deleteMany({ where: { teamId: this.id } });
      await transaction.unitOnRoles.deleteMany({ where: { unitId: this.id } });
      await transaction.unitTeam.delete({ where: { id: this.id } });
      await transaction.spaceLinkInvitation.deleteMany({ where: { teamId: this.id } });
    };
  }

  protected beforeDelete(): void {
    if (this.isRootTeam) {
      throw new Error('Root team cannot be deleted.');
    }
  }

  async afterDelete(): Promise<void> {
    await this.reloadChildren();
  }

  /**
   * 获取部门的显示名称
   * 1. 如果是 ROOT_TEAM，返回 Space 名称
   * 2. 否则，返回包含完整路径的 team j名称
   */
  async getFullPathName() {
    const names = [await this.getName()];

    let { parentId } = this;
    while (parentId !== null) {
      const parent = await db.prisma.unitTeam.findUniqueOrThrow({
        where: {
          id: parentId,
        },
      });
      names.push(parent.name);
      parentId = parent.parentId;
    }

    // 删除根部门名称
    names.pop();

    // 如果没有父部门（则为 TEAM_ROOT），返回 Space 名称
    if (!names.length) {
      const space = await SpaceSO.init(this.spaceId);
      return space.model.name;
    }

    // 拼接 names 路径
    return [...names].reverse().join('/');
  }

  /**
   * 获取部门下的所有成员和子部门(递归查找)
   * @returns { members: MemberSO[], subTeams: TeamSO[] }
   */
  async getMembersAndSubTeams(): Promise<{ members: MemberSO[]; subTeams: TeamSO[] }> {
    const members = await this.getDirectMembers();
    const memberIds = members.map((member) => member.id);

    const subTeams = [];
    const children = await this.getChildren();
    let subTeamIds = children.map((child) => child.id);
    while (subTeamIds.length > 0) {
      const memberSOs = await this.getMembersByTeamIds(subTeamIds);
      if (memberSOs.length > 0) {
        memberSOs
          .filter((member) => !memberIds.includes(member.id))
          .forEach((member) => {
            members.push(member);
            memberIds.push(member.id);
          });
      }
      // 递归查找再下一的子部门
      const teams = await TeamSO.initMany(subTeamIds);
      subTeams.push(...teams);
      const allChildren = await Promise.all(teams.map(async (team) => team.getChildren()));
      subTeamIds = allChildren.map((t) => t.map((child) => child.id)).flatMap((i) => i);
    }
    return { members, subTeams };
  }

  async getMembers(includeSubTeam: boolean = false): Promise<MemberSO[]> {
    const memberIds = await this.getMemberIds(includeSubTeam);
    return UnitFactory.getMembersOnSpace(this.spaceId, memberIds);
  }

  /**
   * 获取部门下的所有成员 （不包含子目录、子部门）
   *
   * @returns
   */
  async getDirectMembers(take?: number, skip?: number): Promise<MemberSO[]> {
    return this.getMembersByTeamIds([this.id], take, skip);
  }

  private async getMembersByTeamIds(teamIds: string[], take?: number, skip?: number): Promise<MemberSO[]> {
    const teamMemberPOs = await db.prisma.unitMembersOnTeams.findMany({
      where: {
        teamId: {
          in: teamIds,
        },
      },
      include: unitMembersOnTeamsIncludeMemberOnly,
      orderBy: {
        createdAt: 'asc',
      },
      take,
      skip,
    });
    return Promise.all(teamMemberPOs.map((teamMemberPO) => MemberSO.initWithModel(teamMemberPO.member)));
  }

  /**
   * 获取部门下的直属成员 id 列表
   * @returns member id list
   */
  async getDirectMemberIds(): Promise<string[]> {
    const models = await db.prisma.unitMembersOnTeams.findMany({
      where: {
        teamId: this.id,
      },
      select: { memberId: true },
    });
    return models.map((m) => m.memberId);
  }

  /**
   * 递归查询部门下的所有成员 id 列表
   * @returns member id list
   */
  private async getMemberIdsOnSubTeam(): Promise<string[]> {
    let children = await db.prisma.unitTeam.findMany({
      where: {
        parentId: this.id,
      },
      select: {
        id: true,
        members: {
          select: {
            memberId: true,
          },
        },
      },
    });
    const allMemberIds: string[] = [];
    while (children.length > 0) {
      const subTeamIds = children.map((child) => child.id);
      const memberIds = children.map((child) => child.members.map((m) => m.memberId)).flat();
      allMemberIds.push(...memberIds);
      const allChildren = await db.prisma.unitTeam.findMany({
        where: {
          parentId: {
            in: subTeamIds,
          },
        },
        select: {
          id: true,
          members: {
            select: {
              memberId: true,
            },
          },
        },
      });
      children = allChildren;
    }
    return uniq(allMemberIds);
  }

  /**
   * 获取部门下的成员ID列表
   * @param includeSubTeam 是否递归查询子部门
   * @returns member id list
   */
  async getMemberIds(includeSubTeam: boolean = false): Promise<string[]> {
    const directMemberIds = await this.getDirectMemberIds();
    if (includeSubTeam) {
      const memberIdsOnSubTeam = await this.getMemberIdsOnSubTeam();
      return uniq([...directMemberIds, ...memberIdsOnSubTeam]);
    }
    return directMemberIds;
  }

  async getSubTeamIds(withRecursion: boolean = false): Promise<string[]> {
    let children = await db.prisma.unitTeam.findMany({
      where: {
        parentId: this.id,
      },
      select: {
        id: true,
      },
    });
    if (!withRecursion) {
      return children.map((child) => child.id);
    }
    const allSubTeamIds = [];
    while (children.length > 0) {
      const subTeamIds = children.map((child) => child.id);
      allSubTeamIds.push(...subTeamIds);
      const allChildren = await db.prisma.unitTeam.findMany({
        where: {
          parentId: {
            in: subTeamIds,
          },
        },
        select: {
          id: true,
        },
      });
      children = allChildren;
    }
    return allSubTeamIds;
  }

  /**
   * 获取部门下的直属成员数量
   */
  async getDirectMembersCount(): Promise<number> {
    return db.prisma.unitMembersOnTeams.count({
      where: {
        teamId: this.id,
      },
    });
  }

  /**
   * 具有去重效果的统计成员数量
   * 统计部门下的所有成员数量，包括子部门
   */
  async countMemberCounts(): Promise<number> {
    if (this.isRootTeam) {
      return UnitFactory.getMemberCount(this.spaceId, this.isGuest);
    }
    const memberIds = await this.getMemberIds(true);
    return memberIds.length;
  }

  static async getRootTeamId(spaceId: string, isGuest: boolean = false): Promise<string> {
    const team = await db.prisma.unitTeam.findFirst({
      where: {
        unit: {
          spaceId,
          type: UnitType.TEAM,
        },
        parentId: null,
        isGuest,
      },
      select: { id: true },
    });
    if (!team) {
      throw new Error(`Root Team not found for space:${spaceId}`);
    }
    return team.id;
  }

  static async getRootTeam(spaceId: string, isGuest: boolean = false): Promise<TeamSO> {
    const rootTeamsPOs = await db.prisma.unitTeam.findMany({
      where: {
        unit: {
          spaceId,
          type: UnitType.TEAM,
        },
        parentId: null,
        isGuest,
      },
      include: teamIncludeUnitOnly,
    });
    if (rootTeamsPOs.length > 1) {
      throw new Error(`Root team count error in space ${spaceId}`);
    }

    let rootTeamPO = rootTeamsPOs[0];
    if (!rootTeamPO) {
      if (!isGuest) {
        throw new Error('Root Team not found.');
      }
      // 补偿数据创建访客根部门
      const space = await SpaceSO.init(spaceId);
      rootTeamPO = await this.createRootTeamOperation(space.model.createdBy ?? '', spaceId, true).operation;
    }
    return this.initWithModel(rootTeamPO);
  }

  /**
   * retrieves all teams that a member belongs to
   * @param memberId
   */
  static async findTeamsByMemberId(memberId: string): Promise<TeamSO[]> {
    const pos = await db.prisma.unitMembersOnTeams.findMany({
      where: {
        memberId: {
          in: [memberId],
        },
      },
    });
    const teamIds = pos.map((po) => po.teamId);
    return this.initMany(teamIds);
  }

  /**
   * 查找部门
   */
  static async find(
    q: { spaceId: string; name?: string; parentId?: string; isGuest?: boolean },
    pagination?: Pagination,
  ): Promise<{ pagination: PaginationInfo; list: TeamSO[] }> {
    const { spaceId, name, parentId, isGuest } = q;
    const { pageNo, pageSize } = PaginationSchema.parse(pagination ?? {});
    let where: Prisma.UnitTeamWhereInput = {
      unit: {
        spaceId,
      },
    };
    if (parentId) {
      where = { ...where, parentId };
    } else {
      where = { ...where, parentId: { not: null } };
    }
    if (name) {
      where = { ...where, name: { contains: name, mode: 'insensitive' } };
    }
    if (isGuest) {
      where = { ...where, isGuest };
    }
    const [rows, total] = await Promise.all([
      db.prisma.unitTeam.findMany({
        where,
        skip: pageSize * (pageNo - 1),
        take: pageSize,
        orderBy: { createdAt: 'asc' },
        include: teamIncludeUnitOnly,
      }),
      db.prisma.unitTeam.count({ where }),
    ]);
    return {
      pagination: { pageNo, pageSize, total },
      list: rows.map((row) => this.initWithModel(row)),
    };
  }

  addMemberOperation(user: UserSO, memberId: string) {
    return TeamSO.createMemberOnTeamOperation(user.id, this.id, memberId);
  }

  static createMemberOnTeamOperation(userId: string, teamId: string, memberId: string) {
    return db.prisma.unitMembersOnTeams.create({
      data: {
        member: {
          connect: {
            id: memberId,
          },
        },
        team: {
          connect: {
            id: teamId,
          },
        },
        createdBy: userId,
      },
    });
  }

  deleteMemberOperation(memberId: string) {
    return TeamSO.deleteMemberOnTeamOperation(this.id, memberId);
  }

  static deleteMemberOnTeamOperation(teamId: string, memberId: string) {
    return db.prisma.unitMembersOnTeams.deleteMany({
      where: {
        teamId,
        memberId,
      },
    });
  }

  /**
   * create root team in space
   * 注意: 此方法只有在创建空间站时调用，而不需要是否校验是否有没有根部门，并且返回的是一个db操作
   * @param userId user id
   * @param spaceId space id
   */
  static createRootTeamOperation(
    userId: string,
    spaceId: string,
    isGuest: boolean = false,
  ): { id: string; operation: PrismaPromise<TeamIncludeUnitModel> } {
    const id = generateNanoID('root');
    const operation = db.prisma.unitTeam.create({
      data: {
        parent: undefined, // 没有父节点
        name: ROOT_TEAM_NAME,
        isGuest,
        unit: {
          create: {
            id,
            type: UnitType.TEAM,
            spaceId,
            createdBy: userId,
            updatedBy: userId,
          },
        },
        createdBy: userId,
        updatedBy: userId,
      },
      include: teamIncludeUnitOnly,
    });
    return {
      id,
      operation,
    };
  }
}
