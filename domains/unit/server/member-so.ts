import { generateNanoID } from 'basenext/utils/nano-id';
import { errors, ServerError } from '@bika/contents/config/server/error';
import { EventSO } from '@bika/domains/event/server/event/event-so';
import { MissionSO } from '@bika/domains/mission/server/mission-so';
import { NodeSO } from '@bika/domains/node/server/node-so';
import { PermissionSO } from '@bika/domains/permission/server/permission-so';
import { ReminderSO } from '@bika/domains/reminder/server/reminder-so';
import { EmailInvitationSO } from '@bika/domains/space/server/invitation/email-invitation-so';
import { LinkInvitationSO } from '@bika/domains/space/server/invitation/link-invitation-so';
import { SpaceMySO } from '@bika/domains/space/server/space-my-so';
import type { SpaceSO } from '@bika/domains/space/server/space-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { $Enums, db, Prisma, PrismaPromise, PrismaTransactionFn, UnitType } from '@bika/server-orm';
import { MemberCellValue, UserCellValue } from '@bika/types/database/vo';
import type { Locale } from '@bika/types/i18n/bo';
import { iStringParse } from '@bika/types/i18n/bo';
import { MissionType, Mission } from '@bika/types/mission/bo';
import { MissionQuerySelectorType } from '@bika/types/mission/vo';
import { OpenAPIMemberCellValue, OpenAPIUserCellValue } from '@bika/types/openapi/vo';
import { Pagination, PaginationInfo, PaginationSchema } from '@bika/types/shared';
import { InvitationType } from '@bika/types/space/bo';
import { EmailInvite } from '@bika/types/space/dto';
import { LocaleType, AvatarLogo } from '@bika/types/system';
import { AnonymousAvatar, AnonymousName } from '@bika/types/unit/default';
import { MemberUpdate } from '@bika/types/unit/dto';
import { MemberVO, RoleVO, TeamVO, UnitRenderOpts } from '@bika/types/unit/vo';
import { RoleSO } from './role-so';
import { TeamSO } from './team-so';
import {
  memberIncludeUserAndUnit,
  MemberIncludeUserAndUnitModel,
  unitMembersOnTeamsIncludeTeamOnly,
  unitOnRolesIncludeRoleOnly,
  MemberFilter,
  MemberCreateProps,
  MemberModel,
  MemberCreateBO,
} from './types';
import { UnitSO } from './unit-so';
/**
 * 空间内正式成员业务对象
 */
export class MemberSO extends UnitSO {
  private _model: MemberIncludeUserAndUnitModel;

  private _user: UserSO = null!; // 肯定被提前赋值

  private _aiNode: NodeSO | null = null;

  // 缓存成员关联的Id, 在构造批量节点权限时有奇效
  private _relationUnitIds: string[] = [];

  private constructor(model: MemberIncludeUserAndUnitModel, user?: UserSO) {
    super(model.unit);
    this._model = model;
    if (user) {
      this._user = user;
    }
  }

  private async doInit() {
    if (this.isUser) {
      this._user = await UserSO.init(this.model.relationId);
    } else if (this.isAI) {
      this._aiNode = await NodeSO.initMaybeNull(this.model.relationId);
    }
  }

  public get model() {
    return this._model;
  }

  get userId() {
    return this.model.relationId;
  }

  /**
   *  尽量用 getName();
   * @deprecated
   */
  get name(): string {
    return this.getName();
  }

  get isAI(): boolean {
    return this.model.relationType === 'AI';
  }

  get isUser(): boolean {
    return this.model.relationType === 'USER' || this.model.relationId.startsWith('usr');
  }

  override getName(locale?: LocaleType): string {
    if (this.model.name) {
      return this.model.name;
    }
    if (this.isUser) {
      const user = this._user;
      return iStringParse(user.name ?? user.email ?? '', locale);
    }

    if (this.isAI) {
      return iStringParse(this._aiNode?.name ?? '', locale);
    }

    return '';
  }

  get avatar(): AvatarLogo | undefined {
    if (this.isUser) {
      return this._user?.avatar;
    }
    if (this.isAI) {
      return this._aiNode?.icon ? (this._aiNode.icon as AvatarLogo) : undefined;
    }
    return undefined;
  }

  get email(): string | null {
    if (this.isUser) {
      return this._user?.email || null;
    }
    return null;
  }

  /**
   * 是否是访客
   */
  get isGuest(): boolean {
    return this.model.isGuest;
  }

  /**
   * 获取多语言
   */
  get locale(): Locale {
    if (this.isUser) {
      return this._user?.locale ?? 'en';
    }
    return 'en';
  }

  /**
   * 我所在的空间站对象
   * @param member member so
   */
  getMy(spaceSO: SpaceSO): SpaceMySO {
    return SpaceMySO.initWithSpaceSO(spaceSO, this);
  }

  async getUser(): Promise<UserSO> {
    return UserSO.init(this.userId);
  }

  /**
   * 是否是空间所有者
   */
  get isSpaceOwner(): boolean {
    return this.model.isOwner;
  }

  async reload(): Promise<void> {
    this._model = await db.prisma.unitMember.findUniqueOrThrow({
      where: {
        id: this.id,
      },
      include: memberIncludeUserAndUnit,
    });
  }

  async toVO(opts?: UnitRenderOpts): Promise<MemberVO> {
    let teams: TeamVO[] | undefined;
    let roles: RoleVO[] | undefined;
    if (opts?.withDetail) {
      const [teamList, roleList] = await Promise.all([this.getTeams(), this.getRoles()]);
      teams = await Promise.all(teamList.map((team) => team.toVO(opts)));
      roles = await Promise.all(roleList.map((role) => role.toVO(opts)));
    }

    return {
      id: this.id,
      name: this.getName(opts?.locale),
      type: 'Member',
      avatar: this.avatar,
      email: this.email,
      userId: this.userId,
      isSpaceOwner: this.isSpaceOwner,
      isGuest: this.isGuest,
      teams,
      roles,
      relationType: this.model.relationType || $Enums.UnitMemberRelationType.USER,
      createdAt: this.model.createdAt?.toISOString(),
    };
  }

  async toCellValue(): Promise<MemberCellValue> {
    return {
      id: this.id,
      name: this.getName(),
      avatar: this.avatar,
    };
  }

  toOpenAPICellValue(_opts?: UnitRenderOpts): OpenAPIMemberCellValue {
    return {
      id: this.id,
      type: 'Member',
      name: this.getName(),
    };
  }

  static async initWithModel(model: MemberIncludeUserAndUnitModel) {
    const mem = new MemberSO(model);
    await mem.doInit();
    return mem;
  }

  static async init(memberId: string): Promise<MemberSO> {
    const member = await db.prisma.unitMember.findUnique({
      where: {
        id: memberId,
      },
      include: memberIncludeUserAndUnit,
    });
    if (!member) {
      throw new ServerError(errors.common.not_found);
    }
    const memberSO = new MemberSO(member);
    await memberSO.doInit();
    return memberSO;
  }

  static async findById(memberId: string): Promise<MemberSO | null> {
    const member = await db.prisma.unitMember.findUnique({
      where: {
        id: memberId,
      },
      include: memberIncludeUserAndUnit,
    });
    return member && new MemberSO(member);
  }

  static async findByUserIds(spaceId: string, userIds: string[]): Promise<MemberSO[]> {
    if (!userIds.length) {
      return [];
    }
    const members = await db.prisma.unitMember.findMany({
      where: {
        spaceId,
        relationId: { in: userIds },
      },
      include: {
        unit: true,
      },
    });
    const users = await UserSO.findByIds(userIds);
    const memberMap = new Map(members.map((member) => [member.relationId, member]));
    return users.map((user) => {
      const member = memberMap.get(user.id);

      if (!member) {
        // Create temporary member model for users not in this space
        const tempMemberModel: MemberIncludeUserAndUnitModel = {
          id: null,
          name: user.name,
          relationId: user.id,
          relationType: 'USER',
          spaceId,
          isActive: false,
          unit: {
            id: null,
          },
        } as unknown as MemberIncludeUserAndUnitModel;
        return new MemberSO(tempMemberModel, user);
      }

      return new MemberSO(member, user);
    });
  }

  static async getMembersByIds(memberIds: string[]): Promise<MemberSO[]> {
    const members = await db.prisma.unitMember.findMany({
      where: {
        id: {
          in: memberIds,
        },
      },
      include: memberIncludeUserAndUnit,
    });
    const userIds: string[] = members
      .filter((i) => i.relationId && (i.relationType === 'USER' || i.relationId.startsWith('usr')))
      .map((i) => i.relationId);
    const users = await UserSO.findByIds(userIds);
    const userMap = new Map(users.map((user) => [user.id, user]));
    return members.map((member) => new MemberSO(member, userMap.get(member.relationId)));
  }

  /**
   * 是否管理员
   * 判断依据: 是否属于管理员角色
   * @returns true or false
   */
  async isAdmin(): Promise<boolean> {
    const count = await db.prisma.unitRole.count({
      where: {
        unit: {
          spaceId: this.spaceId,
        },
        units: {
          some: {
            unitId: this.id,
          },
        },
        manageSpace: true,
      },
    });
    return count > 0;
  }

  /**
   * 分配的角色
   */
  async getRoles(): Promise<RoleSO[]> {
    const units = await db.prisma.unitOnRoles.findMany({
      where: {
        unit: {
          spaceId: this.spaceId,
        },
        unitId: this.id,
      },
      include: unitOnRolesIncludeRoleOnly,
    });
    return units.map((unit) => RoleSO.initWithModel(unit.role));
  }

  async getRoleIds(): Promise<string[]> {
    const unitOnRoles = await db.prisma.unitOnRoles.findMany({
      where: {
        unit: {
          spaceId: this.spaceId,
        },
        unitId: this.id,
      },
      select: {
        roleId: true,
      },
    });
    return unitOnRoles.map((unitOnRole) => unitOnRole.roleId);
  }

  /**
   * 获取所属角色名称
   */
  async getRoleNames(): Promise<string[]> {
    const roles = await this.getRoles();
    return roles.map((role) => role.getName());
  }

  /**
   * 获取管理员角色
   * @returns role so list
   */
  async getAdminRoles(): Promise<RoleSO[]> {
    const roles = await this.getRoles();
    return roles.filter((role) => role.isAdminRole);
  }

  private async updateTeamsOperation(user: UserSO, teamIds: string[]) {
    // 更新部门
    const operations: PrismaPromise<unknown>[] = [];
    const space = await this.getSpace();
    const originTeamIds = await this.getDirectTeamIds();
    if (teamIds.length === 0) {
      // remove all teams relation
      const rootTeam = await space.getRootTeam(this.isGuest);
      if (originTeamIds.length === 1 && originTeamIds[0] === rootTeam.id) {
        return operations;
      }
      operations.push(TeamSO.createMemberOnTeamOperation(user.id, rootTeam.id, this.id));
      operations.push(...originTeamIds.map((teamId) => TeamSO.deleteMemberOnTeamOperation(teamId, this.id)));
    } else {
      // relation to target teams
      const targetTeams = await space.getTeams(teamIds);
      // created teams relation operation
      const createdTeamRelOperates = targetTeams
        .filter((team) => !originTeamIds.includes(team.id))
        .map((team) => TeamSO.createMemberOnTeamOperation(user.id, team.id, this.id));
      operations.push(...createdTeamRelOperates);
      // deleted teams relation operation
      const deletedTeamRelOperates = originTeamIds
        .filter((teamId) => !targetTeams.some((team) => team.id === teamId))
        .map((teamId) => TeamSO.deleteMemberOnTeamOperation(teamId, this.id));
      operations.push(...deletedTeamRelOperates);
    }
    return operations;
  }

  private async updateRolesOperation(user: UserSO, roleIds: string[]) {
    const operations: PrismaPromise<unknown>[] = [];
    const space = await this.getSpace();
    const originRoles = await this.getRoles();
    if (roleIds.length === 0) {
      // remove all roles relation
      operations.push(...originRoles.map((role) => role.deleteUnitOperation(this.id)));
    } else {
      // relation to target roles
      const targetRoles = await space.getRoles(roleIds);
      // created teams relation operation
      const createdTeamRelOperates = targetRoles
        .filter((role) => !originRoles.some((r) => r.id === role.id))
        .map((role) => role.addUnitOperation({ unitId: this.id, createdBy: user.id }));
      operations.push(...createdTeamRelOperates);
      // deleted teams relation operation
      const deletedTeamRelOperates = originRoles
        .filter((role) => !targetRoles.some((r) => r.id === role.id))
        .map((role) => role.deleteUnitOperation(this.id));
      operations.push(...deletedTeamRelOperates);
    }
    return operations;
  }

  /**
   * 修改用户
   * @param user 修改用户
   * @param param 修改参数
   */
  async update(user: UserSO, param: MemberUpdate): Promise<void> {
    const { name, teamIds, roleIds } = param;
    const operations: PrismaPromise<unknown>[] = [];
    let data: Prisma.UnitMemberUpdateInput = {};
    if (name) {
      data = { ...data, name };
    }
    if (Object.keys(data).length !== 0) {
      operations.push(
        db.prisma.unitMember.update({
          where: {
            id: this.id,
          },
          data: {
            ...data,
            updatedBy: user.id,
            updatedAt: new Date(),
          },
        }),
      );
    }
    if (teamIds) {
      // 更新部门
      const updateTeamsOperation = await this.updateTeamsOperation(user, teamIds);
      operations.push(...updateTeamsOperation);
    }
    if (roleIds) {
      // 更新角色
      const updateRolesOperation = await this.updateRolesOperation(user, roleIds);
      operations.push(...updateRolesOperation);
    }
    await db.prisma.$transaction(operations);
    // 刷新本对象
    await this.reload();
  }

  async transferOwner(acceptMember: MemberSO) {
    await db.prisma.$transaction(async (tx) => {
      await tx.unitMember.update({
        where: {
          id: this.id,
        },
        data: {
          isOwner: false,
        },
      });
      await tx.unitMember.update({
        where: {
          id: acceptMember.id,
        },
        data: {
          isOwner: true,
        },
      });
      await tx.space.update({
        where: {
          id: this.spaceId,
        },
        data: {
          ownerUser: {
            connect: {
              id: acceptMember.model.relationId,
            },
          },
        },
      });
    });
    this._model.isOwner = false;
    await this.delete();
  }

  /**
   * 删除之前的操作
   */
  protected beforeDelete(): void {
    if (this.isSpaceOwner) {
      throw new ServerError(errors.space.cannot_delete_owner);
    }
  }

  static deleteMembersTransaction(memberIds: string[]): PrismaTransactionFn {
    return async (transaction) => {
      await transaction.unitMembersOnTeams.deleteMany({ where: { memberId: { in: memberIds } } });
      await transaction.unitOnRoles.deleteMany({ where: { unitId: { in: memberIds } } });
      await transaction.unitMember.deleteMany({ where: { id: { in: memberIds } } });
      await transaction.spaceLinkInvitation.deleteMany({ where: { memberId: { in: memberIds } } });
      await transaction.permission.deleteMany({ where: { unitId: { in: memberIds } } });
      await transaction.unit.deleteMany({ where: { id: { in: memberIds } } });
    };
  }

  protected async deleteInternal(): Promise<PrismaTransactionFn> {
    return async (transaction) => {
      await transaction.unitMembersOnTeams.deleteMany({ where: { memberId: this.id } });
      await transaction.unitOnRoles.deleteMany({ where: { unitId: this.id } });
      await transaction.unitMember.delete({ where: { id: this.id } });
      await transaction.spaceLinkInvitation.deleteMany({ where: { memberId: this.id } });
    };
  }

  protected afterDelete(): void {
    // 记录日志
    EventSO.usageLog.triggerUsageLogCreated(this.spaceId, { type: 'SEATS', value: -1 });
  }

  /**
   * 直属部门ID列表
   * @returns team id list
   */
  protected async getDirectTeamIds(): Promise<string[]> {
    const memberOnTeams = await db.prisma.unitMembersOnTeams.findMany({
      where: {
        memberId: this.id,
      },
      select: {
        teamId: true,
      },
    });
    return memberOnTeams.map((memberOnTeam) => memberOnTeam.teamId);
  }

  /**
   * 获取所在团队
   * @param recursiveParent 是否递归获取所有的团队(parent's parent's parent...)，默认是false，只获取直属子部门
   */
  async getTeams(recursiveParent: boolean = false): Promise<TeamSO[]> {
    const unitMembersOnTeamsPOs = await db.prisma.unitMembersOnTeams.findMany({
      where: {
        memberId: this.id,
      },
      include: unitMembersOnTeamsIncludeTeamOnly,
      orderBy: { team: { createdAt: 'asc' } },
    });

    // 直属子团队
    const directTeams = unitMembersOnTeamsPOs.map((teamMemberPO) => TeamSO.initWithModel(teamMemberPO.team));

    // 递归获取所有父节点团队
    if (recursiveParent) {
      // 递归获取所有上级团队
      const recursiveParentTeam = async (teamId: string): Promise<TeamSO[]> => {
        const team = await TeamSO.init(teamId);
        const parentTeams = [];
        if (team.parentId) {
          parentTeams.push(...(await recursiveParentTeam(team.parentId)));
        }
        return [team, ...parentTeams];
      };

      const recursiveParentTeams = await Promise.all(
        unitMembersOnTeamsPOs.map((teamMemberPO) => recursiveParentTeam(teamMemberPO.team.id)),
      );
      directTeams.push(...recursiveParentTeams.flat());
    }
    // 根据ID去重
    const uniqueTeamsMap = directTeams.reduce<Map<string, TeamSO>>((map, team) => {
      map.set(team.id, team);
      return map;
    }, new Map<string, TeamSO>());
    return Array.from(uniqueTeamsMap.values());
  }

  /**
   * 一个成员映射出所有关联的unit id
   */
  async relationUnitIds(): Promise<string[]> {
    if (this._relationUnitIds.length > 0) {
      return this._relationUnitIds;
    }
    this._relationUnitIds.push(this.id);
    // 所属部门，包括所有上级部门
    const teams = await this.getTeams(true);
    this._relationUnitIds.push(...teams.map((team) => team.id));
    if (!this.isGuest) {
      // 所属角色标签
      const roles = await this.getRoles();
      this._relationUnitIds.push(...roles.map((role) => role.id));
    }
    return this._relationUnitIds;
  }

  /**
   * retrieve granted node id list
   * @returns node id list
   */
  async grantedNodes(): Promise<string[]> {
    const relationUnitIds = await this.relationUnitIds();
    const permissions = await PermissionSO.findByUnitIds(relationUnitIds);
    return permissions
      .filter((permission) => permission.isResource('NODE') && permission.privilege !== 'NO_ACCESS')
      .map((p) => p.resourceId);
  }

  async getSpaceLinkInvitations(): Promise<LinkInvitationSO[]> {
    return LinkInvitationSO.getByMemberId(this.id);
  }

  async createSpaceLinkInvitation(params: {
    invitationType?: InvitationType;
    teamId: string;
    roleIds?: string[];
  }): Promise<LinkInvitationSO> {
    const { invitationType = 'MEMBER', teamId, roleIds } = params;
    return LinkInvitationSO.create(this, { invitationType, teamId, roleIds });
  }

  async invite(invites: EmailInvite[]): Promise<EmailInvitationSO[]> {
    return EmailInvitationSO.create(this, invites);
  }

  /**
   * 获取我的Missions
   * 默认是未完成的
   */
  async getMissions(options?: {
    type?: MissionType;
    queryFilterType?: MissionQuerySelectorType;
  }): Promise<MissionSO[]> {
    const { queryFilterType, ...body } = options || {};
    return MissionSO.getMemberMissions(this, { queryFilterType: queryFilterType || 'PENDING', ...body });
  }

  async completeMissionManually(missionId: string) {
    const mission = await MissionSO.init(missionId);
    return mission.completeManually(this);
  }

  async createMission(mission: Mission): Promise<MissionSO[]> {
    return MissionSO.createMission({
      user: this._user,
      spaceId: this.spaceId,
      missionTemplate: mission,
    });
  }

  async rejectMission(missionId: string): Promise<boolean> {
    const mission = await MissionSO.init(missionId);
    return mission.reject(this);
  }

  async deleteMission(missionId: string) {
    const mission = await MissionSO.init(missionId);
    return mission.delete(this);
  }

  async getReminders(isActive?: boolean) {
    return ReminderSO.getRemindersByMemberId(this.id, isActive);
  }

  static async deleteMany(memberIds: string[]): Promise<void> {
    // 此方法有跳过检查的嫌疑, 调用前请完成必要的逻辑检查
    await db.prisma.$transaction([
      db.prisma.unitMembersOnTeams.deleteMany({ where: { memberId: { in: memberIds } } }),
      db.prisma.unitOnRoles.deleteMany({ where: { unitId: { in: memberIds } } }),
      db.prisma.unitMember.deleteMany({ where: { id: { in: memberIds } } }),
      db.prisma.spaceLinkInvitation.deleteMany({ where: { memberId: { in: memberIds } } }),
      db.prisma.permission.deleteMany({ where: { unitId: { in: memberIds } } }),
      db.prisma.unit.deleteMany({ where: { id: { in: memberIds } } }),
    ]);

    EventSO.usageLog.triggerUsageLogCreated(memberIds[0], { type: 'SEATS', value: -memberIds.length });
  }

  /**
   * query members of space with pagination
   * @param q query selection option
   * @param pagination pagination option
   * @returns member pagination
   */
  static async find(
    q: { spaceId: string; and?: MemberFilter; or?: MemberFilter },
    pagination?: Pagination,
  ): Promise<{ pagination: PaginationInfo; list: MemberSO[] }> {
    const { pageNo, pageSize } = PaginationSchema.parse(pagination ?? {});

    // const buildQuery = (p: { spaceId: string; and?: MemberFilter; or?: MemberFilter }): Prisma.UnitMemberWhereInput => {
    //   const { spaceId, and, or } = p;
    //   const { name, isGuest, ids, teamIds, roleIds } = and || or || {};
    //   let where: Prisma.UnitMemberWhereInput = { spaceId };
    //   if (!and && !or) {
    //     return where;
    //   }
    //   const queries: Prisma.UnitMemberWhereInput[] = [];
    //   // 过滤ID
    //   if (ids) {
    //     // where = { ...where, id: { in: ids } };
    //     queries.push({ id: { in: ids } });
    //   }
    //   // 过滤名称
    //   if (name) {
    //     const OR: Prisma.UnitMemberWhereInput[] = [];
    //     OR.push({ name: { contains: name, mode: 'insensitive' } });
    //     // OR.push({
    //     //   user: {
    //     //     name: { contains: name, mode: 'insensitive' },
    //     //   },
    //     // });
    //     where = { ...where, OR };
    //     queries.push({ OR });
    //   }
    //   // 过滤类型
    //   if (isGuest !== undefined) {
    //     // where = { ...where, isGuest };
    //     queries.push({ isGuest });
    //   }
    //   // 过滤小组
    //   if (teamIds !== undefined) {
    //     // where = { ...where, teams: { some: { teamId: { in: teamIds } } } };
    //     queries.push({ teams: { some: { teamId: { in: teamIds } } } });
    //   }
    //   // 过滤角色
    //   if (roleIds !== undefined) {
    //     // where = { ...where, unit: { roles: { some: { roleId: { in: roleIds } } } } };
    //     queries.push({ unit: { roles: { some: { roleId: { in: roleIds } } } } });
    //   }
    //   const condition = and ? 'AND' : 'OR';
    //   return { ...where, [condition]: queries };
    // };

    // const where = buildQuery(q);

    const { spaceId, and, or } = q;
    const { name, isGuest, ids, teamIds, roleIds } = and || or || {};
    const condition = and ? 'AND' : 'OR';
    const sqlInQuery = `
      FROM "public"."UnitMember" um 
      LEFT JOIN "public"."User" u ON u."id" = um."userId"
      LEFT JOIN "public"."Unit" un ON um."id" = un."id"
      LEFT JOIN "public"."UnitMembersOnTeams" unt ON um."id" = unt."memberId"
      LEFT JOIN "public"."UnitOnRoles" uor ON um."id" = uor."unitId" 
      WHERE um."spaceId" = '${spaceId}'
      AND (
        TRUE
        -- 过滤名称
        ${name ? `${condition} (um."name" ILIKE '%${name}%' OR u."name" ILIKE '%${name}%')` : ''}
        -- 过滤ID
        ${ids ? `${condition} um.id IN (${ids.map((id) => `'${id}'`).join(', ')})` : ''}
        -- 过滤访客
        ${isGuest !== undefined ? `${condition} um."isGuest" = ${isGuest}` : ''}
        -- 过滤小组
        ${teamIds ? `${condition} unt."teamId" IN (${teamIds.map((id) => `'${id}'`).join(', ')})` : ''}
        -- 过滤角色
        ${roleIds ? `${condition} uor."roleId" IN (${roleIds.map((id) => `'${id}'`).join(', ')})` : ''}
        ${
          roleIds
            ? `
          -- ${condition} (SELECT "uor"."unitId" FROM "public"."UnitOnRoles" AS "uor" WHERE ( "uor"."roleId" IN (${roleIds.map((id) => `'${id}'`).join(', ')})))
        `
            : ''
        }
      )`;

    // 过滤类型
    const dataSql = `SELECT 
      um.*, 
      u.*,
      un.*,
      uor.*,
      unt.*
      ${sqlInQuery}
      ORDER BY um."createdAt" ASC
      LIMIT ${pageSize} OFFSET ${pageSize * (pageNo - 1)};
      ;
    `;
    const countSql = `SELECT 
    COUNT(um.id) AS count
    ${sqlInQuery}
    ;
  `;

    // 执行分页查询
    const [rows, countQuery] = await Promise.all([
      //   // db.prisma.unitMember.findMany({
      //   //   where,
      //   //   skip: pageSize * (pageNo - 1),
      //   //   take: pageSize,
      //   //   orderBy: { createdAt: 'asc' },
      //   //   include: memberIncludeUserAndUnit,
      //   // }),
      //     // Prisma.sql
      //     // -- relationId @map 了 userId
      //     sql,
      //   ),
      //   // db.prisma.unitMember.count({ where }),
      db.prisma.$queryRawUnsafe<(MemberIncludeUserAndUnitModel & { userId: string })[]>(dataSql), // user id 是@map 数据库
      db.prisma.$queryRawUnsafe<(MemberIncludeUserAndUnitModel & { userId: string; count: number })[]>(countSql),
    ]);
    const total = Number(countQuery[0].count);

    const data: MemberIncludeUserAndUnitModel[] = [];
    for (const row of rows) {
      const md: MemberIncludeUserAndUnitModel = {
        id: row.id,
        spaceId: row.spaceId,
        relationType: row.relationType,
        relationId: row.userId,
        name: row.name,
        isGuest: row.isGuest,
        isOwner: row.isOwner,
        createdAt: row.createdAt,
        updatedAt: row.updatedAt,
        createdBy: row.createdBy,
        updatedBy: row.updatedBy,
        isActive: row.isActive,
        unit: {
          id: row.id,
          spaceId: row.spaceId,
          type: UnitType.MEMBER,
          createdAt: row.createdAt,
          updatedAt: row.updatedAt,
          createdBy: row.createdBy,
          updatedBy: row.updatedBy,
        },
      };
      data.push(md);
    }

    return {
      pagination: { pageNo, pageSize, total },
      list: await Promise.all(data.map((d) => this.initWithModel(d))),
    };
  }

  static async getNameByUserId(userId: string, spaceId: string): Promise<string | null> {
    const member = await db.prisma.unitMember.findFirst({
      where: {
        relationId: userId,
        spaceId,
      },
      // include: {
      //   user: true,
      // },
    });
    const user = await UserSO.init(userId);
    return member?.name ?? user.name ?? user.email ?? null;
  }

  /**
   * 获取用户在空间站的名称,不属于空间站则返回匿名
   * @param spaceId space id
   */
  static async buildUserCellValue(userId: string, spaceId: string): Promise<UserCellValue> {
    const user = await UserSO.init(userId);
    const member = await db.prisma.unitMember.findFirst({
      where: {
        relationId: userId,
        spaceId,
      },
    });
    if (member) {
      return {
        id: user.id,
        avatar: user.avatar ? (user.avatar as AvatarLogo) : undefined,
        name: member.name ?? user.name ?? user.email ?? '',
      };
    }
    return {
      id: userId,
      name: iStringParse(AnonymousName),
      avatar: AnonymousAvatar,
      deleted: true,
    };
  }

  /**
   * 批量获取用户在空间站的名称
   * 如果用户不属于空间站，则返回匿名用户
   * @param userIds user ids
   * @param spaceId space id
   * @returns UserCellValue[]
   */
  static async buildUserCellValues(userIds: string[], spaceId: string): Promise<UserCellValue[]> {
    const members = await db.prisma.unitMember.findMany({
      where: {
        relationId: { in: userIds },
        spaceId,
      },
    });

    const memberEntries: [string, UserCellValue][] = await Promise.all(
      members.map(async (member) => {
        const user = await UserSO.init(member.relationId);
        return [
          member.relationId,
          {
            id: user.id,
            avatar: user.avatar ? user.avatar : undefined,
            name: member.name ?? user.name ?? user.email ?? '',
          },
        ];
      }),
    );
    const memberMap = Object.fromEntries(memberEntries);
    // for (const member of members) {
    //   const user = await UserSO.init(member.relationId);

    //   memberMap.set(user.id, {
    //     id: user.id,
    //     avatar: user.avatar ? user.avatar : undefined,
    //     name: member.name ?? user.name ?? user.email ?? '',
    //   });
    // }

    return userIds.map(
      (userId) => memberMap[userId] ?? { id: userId, name: iStringParse(AnonymousName), avatar: AnonymousAvatar },
    );
  }

  static async buildOpenAPIUserCellValue(
    userId: string,
    spaceId: string,
    locale?: Locale,
  ): Promise<OpenAPIUserCellValue> {
    const user = await UserSO.init(userId);
    const member = await db.prisma.unitMember.findFirst({
      where: {
        relationId: userId,
        spaceId,
      },
    });
    if (member) {
      return {
        id: member.id,
        name: member.name ?? user.name ?? user.email ?? '',
      };
    }
    return {
      id: '',
      name: iStringParse(AnonymousName, locale),
      deleted: true,
    };
  }

  static async buildOpenAPIUserCellValues(
    userIds: string[],
    spaceId: string,
    locale?: Locale,
  ): Promise<OpenAPIUserCellValue[]> {
    const members = await db.prisma.unitMember.findMany({
      where: {
        relationId: { in: userIds },
        spaceId,
      },
    });
    const memberEntries: [string, OpenAPIUserCellValue][] = await Promise.all(
      members.map(async (member) => {
        const user = await UserSO.init(member.relationId);
        return [
          member.relationId,
          {
            id: member.id,
            name: member.name ?? user.name ?? user.email ?? '',
          },
        ];
      }),
    );
    const memberMap = Object.fromEntries(memberEntries);
    return userIds.map(
      (userId) => memberMap[userId] ?? { id: '', name: iStringParse(AnonymousName, locale), deleted: true },
    );
  }

  /**
   * create a member, which will be related to a team
   * @param props member create props
   */
  static createMemberOperation({
    id,
    name,
    userId,
    spaceId,
    teamId,
    isGuest,
    isOwner,
    relationType,
    relationId,
  }: MemberCreateProps): {
    id: string;
    operation: PrismaPromise<MemberModel>;
  } {
    const memberId = id ?? generateNanoID('meb');
    const operation = db.prisma.unitMember.create({
      data: {
        spaceId,
        relationType: relationType ?? 'USER',
        relationId: relationId ?? userId,
        // user: {
        //   connect: {
        //     id: userId,
        //   },
        // },
        unit: {
          create: {
            id: memberId,
            spaceId,
            type: UnitType.MEMBER,
            createdBy: userId,
            updatedBy: userId,
          },
        },
        teams: {
          create: {
            teamId,
            createdBy: userId,
          },
        },
        name,
        isOwner: isOwner || false,
        isGuest: isGuest || false,
        createdBy: userId,
        updatedBy: userId,
      },
    });
    return {
      id: memberId,
      operation,
    };
  }

  static createMemberOperations({ id, name, userId, spaceId, teamIds, isGuest, isOwner, createdBy }: MemberCreateBO): {
    id: string;
    operation: PrismaPromise<MemberModel>;
  } {
    const memberId = id ?? generateNanoID('meb');
    const operation = db.prisma.unitMember.create({
      data: {
        spaceId,
        relationType: 'USER',
        relationId: userId,
        // user: {
        //   connect: {
        //     id: userId,
        //   },
        // },
        unit: {
          create: {
            id: memberId,
            spaceId,
            type: UnitType.MEMBER,
            createdBy: createdBy ?? userId,
          },
        },
        teams: {
          createMany: {
            data: teamIds.map((teamId) => ({
              teamId,
              createdBy: createdBy ?? userId,
            })),
          },
        },
        name,
        isOwner: isOwner || false,
        isGuest: isGuest || false,
        createdBy: createdBy ?? userId,
      },
    });
    return {
      id: memberId,
      operation,
    };
  }

  static async getSpaceIdsByUserId(userId: string): Promise<string[]> {
    return db.prisma.unitMember
      .findMany({ where: { relationId: userId }, select: { spaceId: true } })
      .then((results) => results.map((i) => i.spaceId));
  }

  static async findUserIdsByIds(memberIds: string[]): Promise<string[]> {
    return db.prisma.unitMember
      .findMany({ where: { id: { in: memberIds } }, select: { relationId: true, relationType: true } })
      .then((results) =>
        results
          .filter((i) => i.relationId && (i.relationType === 'USER' || i.relationId.startsWith('usr')))
          .map((i) => i.relationId),
      );
  }

  static async countByRelationId(relationId: string, spaceId: string): Promise<number> {
    return db.prisma.unitMember.count({
      where: { relationId, spaceId },
    });
  }

  static async findIdsByRelationId(
    relationId: string,
    relationType: $Enums.UnitMemberRelationType,
    spaceId: string,
  ): Promise<string[]> {
    return db.prisma.unitMember
      .findMany({ where: { relationId, spaceId, relationType }, select: { id: true } })
      .then((results) => results.map((i) => i.id));
  }
}
