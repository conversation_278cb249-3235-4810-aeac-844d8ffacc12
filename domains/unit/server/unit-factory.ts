import _ from 'lodash';
import { generateNanoID } from 'basenext/utils/nano-id';
import { db, Prisma, UnitType } from '@bika/server-orm';
import { Pagination, PaginationInfo, PaginationSchema } from '@bika/types/shared';
import { UnitSearch } from '@bika/types/unit/dto';
import { MemberSO } from './member-so';
import { RoleSO } from './role-so';
import { TeamSO } from './team-so';
import { memberIncludeUserAndUnit, teamIncludeUnitOnly, UnitModel } from './types';
import { UnitSO } from './unit-so';

const { uniq } = _;

export class UnitFactory {
  static generateUnitId(type: UnitType): string {
    switch (type) {
      case UnitType.MEMBER:
        return generateNanoID('mem');
      case UnitType.TEAM:
        return generateNanoID('tem');
      case UnitType.ROLE:
        return generateNanoID('rol');
      default:
        throw new Error('Invalid unit type');
    }
  }

  /**
   * 识别哪一种组织单元实例
   * @param type unit type
   * @param unitId unit id
   * @returns Unit instance
   */
  static async determineUnit(type: UnitType, unitId: string): Promise<UnitSO> {
    switch (type) {
      case UnitType.MEMBER:
        return MemberSO.init(unitId);
      case UnitType.TEAM:
        return TeamSO.init(unitId);
      case UnitType.ROLE:
        return RoleSO.init(unitId);
      default:
        throw new Error('Invalid unit type');
    }
  }

  static async getUnitOnSpace(spaceId: string, unitId: string): Promise<UnitSO> {
    const unitModel = await db.prisma.unit.findFirst({ where: { id: unitId, spaceId } });
    if (!unitModel) {
      throw new Error('unit not found in the space');
    }
    return this.determineUnit(unitModel.type, unitModel.id);
  }

  static async getUnitsOnSpace(spaceId: string, unitIds: string[], throwCrossSpaceError?: boolean): Promise<UnitSO[]> {
    if (unitIds.length === 0) {
      return [];
    }
    const unitModels = await db.prisma.unit.findMany({
      where: { id: { in: unitIds }, spaceId },
    });
    const foundUnitIds = unitModels.map((unit) => unit.id);
    if (throwCrossSpaceError && unitIds.some((id) => !foundUnitIds.includes(id))) {
      throw new Error('units not match in the space');
    }
    // 查找到的数据按照unitIds的顺序重新排序
    const orderUnitModels = unitIds.reduce<UnitModel[]>((cur, next) => {
      const unit = unitModels.find((unitModel) => unitModel.id === next);
      if (unit) {
        cur.push(unit);
      }
      return cur;
    }, []);
    return this.buildUnits(orderUnitModels);
  }

  static async getUnitsByUnitIds(spaceId: string, unitIds: string[]): Promise<UnitSO[]> {
    if (unitIds.length === 0) {
      return [];
    }
    const unitModels = await db.prisma.unit.findMany({
      where: { id: { in: unitIds }, spaceId },
    });
    // 查找到的数据按照unitIds的顺序重新排序
    const orderUnitModels = unitIds.reduce<UnitModel[]>((cur, next) => {
      const unit = unitModels.find((unitModel) => unitModel.id === next);
      if (unit) {
        cur.push(unit);
      }
      return cur;
    }, []);
    return this.buildUnits(orderUnitModels);
  }

  private static async buildUnits(unitModels: UnitModel[]): Promise<UnitSO[]> {
    return Promise.all(unitModels.map((unitModel) => this.determineUnit(unitModel.type, unitModel.id)));
  }

  static async findMember(userId: string, spaceId: string): Promise<MemberSO | null> {
    const member = await db.prisma.unitMember.findFirst({
      where: {
        relationId: userId,
        spaceId,
      },
      include: memberIncludeUserAndUnit,
    });
    return member && MemberSO.initWithModel(member);
  }

  static async findMembers(userIds: string[], spaceId: string): Promise<MemberSO[]> {
    const members = await db.prisma.unitMember.findMany({
      where: {
        relationId: { in: userIds },
        spaceId,
      },
      include: memberIncludeUserAndUnit,
    });
    return Promise.all(members.map((member) => MemberSO.initWithModel(member)));
  }

  static async findMembersByUserId(userId: string): Promise<MemberSO[]> {
    const members = await db.prisma.unitMember.findMany({
      where: {
        relationId: userId,
      },
      include: memberIncludeUserAndUnit,
    });
    return Promise.all(members.map((m) => MemberSO.initWithModel(m)));
  }

  static async findNormalMember(spaceId: string): Promise<MemberSO | null> {
    const member = await db.prisma.unitMember.findFirst({
      where: {
        spaceId,
        isGuest: false,
        isOwner: false,
      },
      include: memberIncludeUserAndUnit,
    });
    return member && MemberSO.initWithModel(member);
  }

  static async userExistOnSpace(userId: string, spaceId: string): Promise<boolean> {
    const count = await db.prisma.unitMember.count({
      where: {
        relationId: userId,
        spaceId,
      },
    });
    return count === 1;
  }

  static async checkMembersOnSpace(spaceId: string, memberIds: string[]): Promise<boolean> {
    const count = await db.prisma.unitMember.count({
      where: {
        id: {
          in: memberIds,
        },
        spaceId,
      },
    });
    return count === memberIds.length;
  }

  static async getMembersOnSpace(spaceId: string, memberIds: string[]): Promise<MemberSO[]> {
    const pos = await db.prisma.unitMember.findMany({
      where: {
        id: {
          in: memberIds,
        },
        spaceId,
      },
      include: memberIncludeUserAndUnit,
    });
    return Promise.all(pos.map((po) => MemberSO.initWithModel(po)));
  }

  static async getTeamOnSpace(spaceId: string, teamId: string): Promise<TeamSO> {
    const model = await db.prisma.unitTeam.findFirst({
      where: {
        id: teamId,
        unit: {
          spaceId,
        },
      },
      include: teamIncludeUnitOnly,
    });
    if (!model) {
      throw new Error('team not found in the space');
    }
    return TeamSO.initWithModel(model);
  }

  static async getTeamsOnSpace(spaceId: string, teamIds: string[], verify: boolean = true): Promise<TeamSO[]> {
    const models = await db.prisma.unitTeam.findMany({
      where: {
        id: {
          in: teamIds,
        },
        unit: {
          spaceId,
        },
      },
      include: teamIncludeUnitOnly,
    });
    if (verify && models.length !== teamIds.length) {
      throw new Error('Some teams not found');
    }
    return models.map((model) => TeamSO.initWithModel(model));
  }

  static async extractMemberIdsFromUnits(units: UnitSO[]): Promise<string[]> {
    const memberIds: string[] = [];
    for (const unit of units) {
      switch (unit.type) {
        case UnitType.MEMBER: {
          memberIds.push(unit.id);
          break;
        }
        case UnitType.TEAM: {
          const teamSO = unit as TeamSO;
          const members = await teamSO.getMemberIds(true);
          memberIds.push(...members);
          break;
        }
        case UnitType.ROLE: {
          const roleSO = unit as RoleSO;
          const members = await roleSO.getMemberIds();
          memberIds.push(...members);
          break;
        }
        default:
          throw new Error(`Not Implement UnitType: ${unit.type}`);
      }
    }
    return uniq(memberIds);
  }

  /**
   * 从多个Unit中提取成员
   * @param unitIds unit id list
   */
  static async extractMembersFromUnits(spaceId: string, unitIds: string[]): Promise<MemberSO[]> {
    const members: MemberSO[] = [];
    const units = await this.getUnitsOnSpace(spaceId, unitIds);
    for (const unit of units) {
      switch (unit.type) {
        case UnitType.MEMBER: {
          const member = unit as MemberSO;
          members.push(member);
          break;
        }
        case UnitType.TEAM: {
          const team = unit as TeamSO;
          const membersOnTeam = await team.getMembers(true);
          members.push(...membersOnTeam);
          break;
        }
        case UnitType.ROLE: {
          const role = unit as RoleSO;
          const membersOnRole = await role.getMembers();
          members.push(...membersOnRole);
          break;
        }
        default:
          throw new Error(`Not Implement UnitType: ${unit.type}`);
      }
    }
    return members;
  }

  /**
   * 查询组织单元
   * @param q 查询条件
   * @param pagination 分页参数
   * @returns
   */
  static async find(
    q: { spaceId: string } & UnitSearch,
    pagination?: Pagination,
  ): Promise<{ pagination: PaginationInfo; list: UnitSO[] }> {
    const { spaceId, name, ids, isGuest } = q;
    const { pageNo, pageSize } = PaginationSchema.parse(pagination ?? {});
    // let where: Prisma.UnitWhereInput = { spaceId };

    // const guestTeamWhereInput: Prisma.UnitTeamWhereInput = isGuest !== undefined ? { isGuest } : {};
    // const guestMemberWhereInput: Prisma.UnitMemberWhereInput = isGuest !== undefined ? { isGuest } : {};

    // 过滤名称
    // if (name) {
    //   where = {
    //     ...where,
    //     OR: [
    //       {
    //         team: {
    //           AND: [
    //             { name: { contains: name, mode: 'insensitive' } },
    //             { parentId: { not: null } },
    //             guestTeamWhereInput,
    //           ],
    //         },
    //       },
    //       {
    //         member: {
    //           ...guestMemberWhereInput,
    //           OR: [
    //             { name: { contains: name, mode: 'insensitive' } },
    //             // { user: { name: { contains: name, mode: 'insensitive' } } },
    //             // { user: { email: { contains: name, mode: 'insensitive' } } },
    //           ],
    //         },
    //       },
    //       isGuest !== undefined && isGuest ? {} : { role: { name: { string_contains: name } } },
    //     ],
    //   };
    // } else {
    //   where = {
    //     ...where,
    //     OR: [
    //       { team: { parentId: { not: null }, ...guestTeamWhereInput } },
    //       { member: { id: { not: '' }, ...guestMemberWhereInput } },
    //       isGuest !== undefined && isGuest ? {} : { role: { id: { not: '' } } },
    //     ],
    //   };
    // }

    // if (ids && ids.length > 0) {
    //   where = {
    //     ...where,
    //     id: { in: ids },
    //   };
    // }

    let isGuestSql;
    if (isGuest === undefined) {
      isGuestSql = '';
    } else if (isGuest === true) {
      isGuestSql = `AND (um."isGuest" = true OR ut."isGuest" = true)`;
    } else if (isGuest === false) {
      isGuestSql = `AND (um."isGuest" IS NOT true AND ut."isGuest" IS NOT true)`;
    }

    const sqlInQuery = `
      FROM "public"."Unit" un
      LEFT JOIN "public"."UnitMember" um ON un."id" = um."id"
      LEFT JOIN "public"."UnitTeam" ut ON ut."id" = un."id"
      LEFT JOIN "public"."UnitRole" ur ON ur."id" = un."id"
      LEFT JOIN "public"."User" u ON u."id" = um."userId"
      -- LEFT JOIN "public"."UnitMembersOnTeams" unt ON um."id" = unt."memberId"
      -- LEFT JOIN "public"."UnitOnRoles" uor ON um."id" = uor."unitId" 
      WHERE un."spaceId" = '${spaceId}'
      ${isGuestSql}
      AND (
        TRUE
        ${name ? `AND (um."name" ILIKE '%${name}%' OR ut."name" ILIKE '%${name}%') OR u."name" ILIKE '%${name}%' OR ur."name"::text ILIKE '%${name}%'` : ''}
        ${ids && ids.length > 0 ? `AND un."id" IN (${ids.map((id) => `'${id}'`).join(', ')})` : ''}
      )`;

    // 过滤类型
    const dataSql = `SELECT 
      un.id,
      um."isGuest" AS "memberIsGuest",
      ut."isGuest" AS "teamIsGuest"

      ${sqlInQuery}
      ORDER BY un."createdAt" ASC
      LIMIT ${pageSize} OFFSET ${pageSize * (pageNo - 1)};
      ;
    `;
    const countSql = `SELECT 
    COUNT(un.id) AS count 
    ${sqlInQuery}
    ;
  `;

    const [rows, totalQuery] = await Promise.all([
      db.prisma.$queryRawUnsafe<any[]>(dataSql), // user id 是@map 数据库
      db.prisma.$queryRawUnsafe<any[]>(countSql), // user id 是@map 数据库
      // db.prisma.unit.findMany({
      //   where,
      //   select: { id: true },
      //   skip: (pageNo - 1) * pageSize,
      //   take: pageSize,
      // }),
      // db.prisma.unit.count({ where }),
    ]);
    const total = Number(totalQuery[0].count);

    const unitIds = rows.map((row) => row.id);
    const units = await this.getUnitsOnSpace(spaceId, unitIds);
    return {
      pagination: { pageNo, pageSize, total },
      list: units,
    };
  }

  static async getMemberCount(spaceId: string, isGuest: boolean): Promise<number> {
    return db.prisma.unitMember.count({ where: { unit: { spaceId }, isGuest } });
  }

  static async getSpaceMemberCount(
    spaceId: string,
    opts?: { includeGuest?: boolean; specifyRelation?: 'AI' | 'USER' },
  ): Promise<number> {
    const { includeGuest = true, specifyRelation = undefined } = opts ?? {};
    let where: Prisma.UnitMemberWhereInput = { unit: { spaceId } };
    if (!includeGuest) {
      where = { ...where, isGuest: false };
    }
    if (specifyRelation) {
      if (specifyRelation === 'AI') where = { ...where, relationType: 'AI' };
      else {
        where = {
          ...where,
          relationType: {
            not: 'AI',
          },
        };
      }
    }
    return db.prisma.unitMember.count({ where });
  }

  static async selectIdsBySpaceIdAndUnitIds(spaceId: string, unitIds: string[]): Promise<string[]> {
    return db.prisma.unit
      .findMany({
        where: {
          spaceId,
          id: {
            in: unitIds,
          },
        },
        select: {
          id: true,
        },
      })
      .then((results) => results?.map((i) => i.id));
  }
}
