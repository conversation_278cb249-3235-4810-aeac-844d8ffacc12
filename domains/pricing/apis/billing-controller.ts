import { getLatestSpacePlanSku } from '@bika/contents/config/server/pricing/sku/sku';
import { PaymentSO } from '@bika/domains/pricing/server';
import { UserSO } from '@bika/domains/user/server';
import { TRPCError } from '@bika/server-orm/trpc';
import {
  PaymentCreateDTO,
  CheckoutPreviewDTO,
  PaymentInfoDTO,
  SubscriptionInfoDTO,
  SubscriptionUpdateDTO,
  SubscriptionUsageDTO,
} from '@bika/types/pricing/dto';
import { CheckoutVO, EntitlementFeatureVO, SubscriptionVO } from '@bika/types/pricing/vo';

export class BillingController {
  static async createPreviewCheckout(user: UserSO, req: CheckoutPreviewDTO) {
    const { spaceId, plan, interval, currency } = req;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    const sku = getLatestSpacePlanSku(plan, interval);
    if (!sku) {
      throw new TRPCError({ code: 'BAD_REQUEST', message: `Invalid plan: ${plan}` });
    }
    return space.billing.createPreviewCheckout(user.id, sku, currency);
  }

  static async createCheckout(user: UserSO, req: PaymentCreateDTO): Promise<CheckoutVO> {
    const { spaceId, plan, interval, currency, rewardful } = req;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    const sku = getLatestSpacePlanSku(plan, interval);
    if (!sku) {
      throw new TRPCError({ code: 'BAD_REQUEST', message: `Invalid plan: ${plan}` });
    }
    return space.billing.createCheckoutSession({
      userId: user.id,
      sku,
      currencyCode: currency,
      rewardful,
    });
  }

  static async getPayment(_user: UserSO, req: PaymentInfoDTO) {
    const { paymentId } = req;
    const payment = await PaymentSO.init(paymentId);
    if (!payment) {
      throw new Error(`Payment not found: ${paymentId}`);
    }
    return payment.toVO();
  }

  static async getSubscription(user: UserSO, req: SubscriptionInfoDTO): Promise<SubscriptionVO> {
    const { spaceId } = req;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    return space.billing.getSubscriptionVO(user.locale);
  }

  static async updateSubscription(user: UserSO, req: SubscriptionUpdateDTO): Promise<CheckoutVO> {
    const { spaceId, plan, interval } = req;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    const paymentId = await space.billing.changePlanInStripe({ userId: user.id, plan, interval });
    return { paymentId };
  }

  static async cancelSubscription(user: UserSO, req: SubscriptionInfoDTO): Promise<void> {
    const { spaceId } = req;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    await space.billing.cancelSubscription(user.id);
  }

  static async resumeSubscription(user: UserSO, req: SubscriptionInfoDTO): Promise<void> {
    const { spaceId } = req;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    await space.billing.resumeSubscription(user.id);
  }

  static async getEntitlements(user: UserSO, req: SubscriptionUsageDTO): Promise<EntitlementFeatureVO[]> {
    const { spaceId } = req;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    const entitlement = await space.getEntitlement();
    return entitlement.toVO();
  }
}
