import { UserSO } from '@bika/domains/user/server';
import { protectedProcedure, router } from '@bika/server-orm/trpc';
import {
  SubscriptionInfoDTOSchema,
  SubscriptionUsageDTOSchema,
  BillingInfoDTOSchema,
  InvoiceListDTOSchema,
  CheckoutPreviewDTOSchema,
  SubscriptionUpdateDTOSchema,
  PaymentCreateDTOSchema,
  PaymentInfoDTOSchema,
} from '@bika/types/pricing/dto';
import { BillingController } from './billing-controller';

export const billingRouter = router({
  /**
   * 预览付款信息
   */
  checkoutPreview: protectedProcedure.input(CheckoutPreviewDTOSchema).query(async (opts) => {
    const { ctx, input } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    return BillingController.createPreviewCheckout(user, input);
  }),
  /**
   * 创建付款会话
   */
  createCheckout: protectedProcedure.input(PaymentCreateDTOSchema).mutation(async (opts) => {
    const { ctx, input } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    return BillingController.createCheckout(user, input);
  }),
  /**
   * 获取支付信息
   */
  paymentInfo: protectedProcedure.input(PaymentInfoDTOSchema).query(async (opts) => {
    const { ctx, input } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    return BillingController.getPayment(user, input);
  }),
  // /**
  //  * 获取账单信息
  //  */
  // info: protectedProcedure.input(BillingInfoDTOSchema).query(async (opts) => {
  //   const { ctx, input } = opts;
  //   const user = await UserSO.init(ctx.session!.userId);
  // }),
  /**
   * 获取客户门户地址
   */
  customerPortal: protectedProcedure.input(BillingInfoDTOSchema).mutation(async (opts) => {
    const { ctx, input } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    const { spaceId } = input;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    const customer = await space.billing.getOrCreateCustomer(user.id);
    return customer.getPortalUrl();
  }),
  /**
   * 获取订阅信息
   */
  subscription: protectedProcedure.input(SubscriptionInfoDTOSchema).query(async (opts) => {
    const { ctx, input } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    return BillingController.getSubscription(user, input);
  }),
  /**
   * 更新订阅, 用户确认后立即扣款
   */
  updateSubscription: protectedProcedure.input(SubscriptionUpdateDTOSchema).mutation(async (opts) => {
    const { ctx, input } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    return BillingController.updateSubscription(user, input);
  }),
  /**
   * 取消订阅
   */
  cancelSubscription: protectedProcedure.input(SubscriptionInfoDTOSchema).mutation(async (opts) => {
    const { ctx, input } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    await BillingController.cancelSubscription(user, input);
  }),
  /**
   * 恢复订阅
   */
  resumeSubscription: protectedProcedure.input(SubscriptionInfoDTOSchema).mutation(async (opts) => {
    const { ctx, input } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    await BillingController.resumeSubscription(user, input);
  }),
  /**
   * 获取账单列表
   */
  invoices: protectedProcedure.input(InvoiceListDTOSchema).query(async (opts) => {
    const { ctx, input } = opts;
    const user = await UserSO.init(ctx.session!.userId);
  }),

  /**
   * 获取订阅权益(包含用量)
   */
  entitlements: protectedProcedure.input(SubscriptionUsageDTOSchema).query(async (opts) => {
    const { ctx, input } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    return BillingController.getEntitlements(user, input);
  }),
});
