import { z } from 'zod';
import { PricingConfig } from '@bika/contents/config/server';
import { publicProcedure, router } from '@bika/server-orm/trpc';
import { LocaleSchema } from '@bika/types/system';

export const pricingRouter = router({
  getPricing: publicProcedure
    .input(
      z.object({
        lang: LocaleSchema,
      }),
    )
    .query(async (opts) => {
      const { lang } = opts.input;
      return PricingConfig.getPricingConfig(lang);
    }),
});
