import { RemoteStorageSO } from '@bika/domains/system/server';
import { UserSO } from '@bika/domains/user/server/user-so';
import { SessionSO } from '@bika/server-orm/session';
import { parseAttributesFromRequest } from '@bika/server-orm/utils';
import { ActiveLicenseDTO, ActiveLicenseWithEmailDTO } from '@bika/types/pricing/dto';
import { RedeemedVO } from '@bika/types/pricing/vo';
import { ApiFetchRequestContext } from '@bika/types/user/vo';
import { AppsumoClient } from '../server/billing/appsumo/appsumo-client';

export class AppsumoController {
  static async activeLicense(user: UserSO, req: ActiveLicenseDTO): Promise<RedeemedVO> {
    const { code } = req;
    const client = new AppsumoClient();
    const licenseKey = await client.getUserLicenseKey(code);
    const space = await user.redeemCode(licenseKey);
    const entitlement = await space.getEntitlement();
    const subscription = entitlement.getSubscription();
    if (!subscription) {
      throw new Error('Code redeemed failed, please contact support');
    }
    return {
      redeemedSpaceId: space.id,
      redeemedPlan: subscription.skuConfig.plan,
    };
  }

  static async activeLicenseForGuest(
    fetchRequestContext: ApiFetchRequestContext,
    req: ActiveLicenseWithEmailDTO,
  ): Promise<RedeemedVO> {
    const { code, email, verifyCode } = req;
    const client = new AppsumoClient();
    const licenseKey = await client.getUserLicenseKey(code);
    // 校验验证码
    await RemoteStorageSO.matchVerificationCode('MAIL_VERIFICATION_CODE', email, verifyCode);
    const { user, space } = await UserSO.redeemCodeByEmail(email, licenseKey);
    const attributes = parseAttributesFromRequest(fetchRequestContext.req.headers);
    const session = await SessionSO.create(user.id, attributes);

    fetchRequestContext.resHeaders?.append('Set-Cookie', session.toCookie().serialize());
    await RemoteStorageSO.delete('MAIL_VERIFICATION_CODE', email);
    const entitlement = await space.getEntitlement();
    const subscription = entitlement.getSubscription();
    if (!subscription) {
      throw new Error('License activation failed, please contact support');
    }
    return {
      redeemedSpaceId: space.id,
      redeemedPlan: subscription.skuConfig.plan,
    };
  }
}
