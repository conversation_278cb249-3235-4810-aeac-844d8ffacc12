import crypto from 'crypto';
import axios, { AxiosError } from 'axios';
import { generateNanoID } from 'basenext/utils/nano-id';
import { describe, it, expect, vi, Mocked } from 'vitest';
import { AppsumoUserAccessTokenRemoteStorageProperty } from '@bika/types/system/remote-storage';
import { SpaceSO } from '../../space/server';
import { RemoteStorageSO } from '../../system/server/remote-storage/remote-storage-so';
import { UserSO } from '../../user/server';
import { GiftCodeFactory } from '../server';
import { AppsumoClient } from '../server/billing/appsumo/appsumo-client';
import { AppsumoEvent } from '../server/billing/appsumo/appsumo-event';
import { AppsumoEventType } from '../server/billing/appsumo/types';

vi.mock('axios');
// Mock axios.post
const mockedAxios = axios as Mocked<typeof axios>;

describe('Appsumo event test', () => {
  it('should validate signature correctly', () => {
    const event = new AppsumoEvent();
    const timestamp = '1234567890';
    const body = '{"test": "data"}';
    const digest = crypto
      .createHmac('SHA256', process.env.APPSUMO_PRIVATE_KEY!)
      .update(`${timestamp}${body}`)
      .digest('hex');

    expect(event.validate(timestamp, body, digest)).toBe(true);
    expect(event.validate(timestamp, body, 'wrong-digest')).toBe(false);
  });

  it('should handle unknown event type', async () => {
    const event = new AppsumoEvent();
    await expect(
      event.handle({
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        event: 'UNKNOWN_EVENT' as any,
        license_key: '00000000-aaaa-1111-bbbb-abcdef012345',
        event_timestamp: 1739783357612,
        created_at: 1739783357612,
        license_status: 'inactive',
        tier: 1,
        test: true,
        extra: {
          reason: 'Unknown event',
        },
        plan_id: '1',
      }),
    ).rejects.toThrow('Unknown event type');
  });

  it('should handle duplicate events gracefully', async () => {
    const event = new AppsumoEvent();
    const licenseKey = `00000000-aaaa-1111-bbbb-${generateNanoID('_', 10)}`;

    // 处理第一次购买事件
    await event.handle({
      license_key: licenseKey,
      event: AppsumoEventType.Purchase,
      event_timestamp: 1739783357612,
      created_at: 1739783357612,
      license_status: 'inactive',
      tier: 1,
      test: true,
      extra: {
        reason: 'Purchased by customer',
      },
      plan_id: '1',
    });

    // 处理重复的购买事件
    await event.handle({
      license_key: licenseKey,
      event: AppsumoEventType.Purchase,
      event_timestamp: 1739783357612,
      created_at: 1739783357612,
      license_status: 'inactive',
      tier: 1,
      test: true,
      extra: {
        reason: 'Purchased by customer',
      },
      plan_id: '1',
    });
    const license = await GiftCodeFactory.findUniqueCode(licenseKey);
    expect(license).toBeDefined();
  });

  it('a cicle event test', async () => {
    const event = new AppsumoEvent();
    const tier1LicenseKey = `00000000-aaaa-1111-bbbb-${generateNanoID('_', 10)}`;
    const tier2LicenseKey = `00000000-aaaa-1111-bbbb-${generateNanoID('_', 10)}`;
    const tier3LicenseKey = `00000000-aaaa-1111-bbbb-${generateNanoID('_', 10)}`;
    await event.handle({
      license_key: tier1LicenseKey,
      event: AppsumoEventType.Purchase,
      event_timestamp: 1739783357612,
      created_at: 1739783357612,
      license_status: 'inactive',
      tier: 1,
      test: true,
      extra: {
        reason: 'Purchased by customer',
      },
      plan_id: '1',
    });
    const license1 = await GiftCodeFactory.findUniqueCode(tier1LicenseKey);
    expect(license1).toBeDefined();
    expect(license1?.status).toBe('UNUSED');

    // upgrade
    await event.handle({
      license_key: tier3LicenseKey,
      event: AppsumoEventType.Upgrade,
      event_timestamp: 1739783357612,
      created_at: 1739783357612,
      license_status: 'inactive',
      tier: 3,
      test: true,
      prev_license_key: tier1LicenseKey,
      plan_id: '3',
      extra: {
        reason: 'Upgraded by customer',
      },
    });
    const license3 = await GiftCodeFactory.findUniqueCode(tier3LicenseKey);
    expect(license3).toBeDefined();
    expect(license3?.status).toBe('UNUSED');

    const prevLicense1 = await GiftCodeFactory.findUniqueCode(tier1LicenseKey);
    expect(prevLicense1).toBe(null);

    // downgrade
    await event.handle({
      license_key: tier2LicenseKey,
      event: AppsumoEventType.Upgrade,
      event_timestamp: 1739783357612,
      created_at: 1739783357612,
      license_status: 'inactive',
      tier: 2,
      test: true,
      prev_license_key: tier3LicenseKey,
      plan_id: '2',
      extra: {
        reason: 'Upgraded by customer',
      },
    });
    const license2 = await GiftCodeFactory.findUniqueCode(tier2LicenseKey);
    expect(license2).toBeDefined();
    expect(license2?.status).toBe('UNUSED');

    const prevLicense3 = await GiftCodeFactory.findUniqueCode(tier3LicenseKey);
    expect(prevLicense3).toBe(null);

    // deactivate
    await event.handle({
      license_key: tier2LicenseKey,
      event: AppsumoEventType.Deactivate,
      event_timestamp: 1739783328461,
      created_at: 1739783328461,
      license_status: 'active',
      tier: 2,
      test: true,
      extra: {
        reason: 'Deactivated by customer',
      },
      plan_id: '2',
    });

    const license = await GiftCodeFactory.findUniqueCode(tier2LicenseKey);
    expect(license).toBe(null);
  });

  it('user active appsumo tier1 license', async () => {
    const event = new AppsumoEvent();
    // use clikc active button in appsumo site
    const licenseKey = `00000000-aaaa-1111-bbbb-${generateNanoID('_', 10)}`;
    await event.handle({
      license_key: licenseKey,
      event: AppsumoEventType.Activate,
      event_timestamp: 1739783357612,
      created_at: 1739783357612,
      license_status: 'inactive',
      tier: 1,
      test: true,
      plan_id: '1',
      extra: {
        reason: 'Activated by customer',
      },
    });
    // then redirect to our site
    const license = await GiftCodeFactory.findUniqueCode(licenseKey);
    const { user, space } = await UserSO.redeemCodeByEmail(`test${generateNanoID('_', 2)}@aitable.ai`, license!.code);
    console.log('appsumo user', user.email);
    expect(user).toBeDefined();
    expect(space).toBeDefined();
    expect(space.name).toBe('Untitled Space');
    const entitlement = await space.getEntitlement();
    const subscription = entitlement.getSubscription();
    expect(subscription).toBeDefined();
    expect(subscription?.skuId).toBe('202411-space-plan-plus-tier1-appsumo-once-appsumo-seat');
  });

  it('user active appsumo tier2 license', async () => {
    const event = new AppsumoEvent();
    // use clikc active button in appsumo site
    const licenseKey = `00000000-aaaa-1111-bbbb-${generateNanoID('_', 10)}`;
    await event.handle({
      license_key: licenseKey,
      event: AppsumoEventType.Activate,
      event_timestamp: 1739783357612,
      created_at: 1739783357612,
      license_status: 'inactive',
      tier: 2,
      test: true,
      plan_id: '2',
      extra: {
        reason: 'Activated by customer',
      },
    });
    // then redirect to our site
    const license = await GiftCodeFactory.findUniqueCode(licenseKey);
    const { user, space } = await UserSO.redeemCodeByEmail(`test${generateNanoID('_', 2)}@aitable.ai`, license!.code);
    console.log('appsumo user', user.email);
    expect(user).toBeDefined();
    expect(space).toBeDefined();
    const entitlement = await space.getEntitlement();
    const subscription = entitlement.getSubscription();
    expect(subscription).toBeDefined();
    expect(subscription?.skuId).toBe('202411-space-plan-pro-tier2-appsumo-once-appsumo-seat');
  });

  it('user active appsumo tier3 license', async () => {
    const event = new AppsumoEvent();
    // use clikc active button in appsumo site
    const licenseKey = `00000000-aaaa-1111-bbbb-${generateNanoID('_', 10)}`;
    await event.handle({
      license_key: licenseKey,
      event: AppsumoEventType.Activate,
      event_timestamp: 1739783357612,
      created_at: 1739783357612,
      license_status: 'inactive',
      tier: 3,
      test: true,
      plan_id: '3',
      extra: {
        reason: 'Activated by customer',
      },
    });
    // then redirect to our site
    const license = await GiftCodeFactory.findUniqueCode(licenseKey);
    const { user, space } = await UserSO.redeemCodeByEmail(`test${generateNanoID('_', 2)}@aitable.ai`, license!.code);
    console.log('appsumo user', user.email);
    expect(user).toBeDefined();
    expect(space).toBeDefined();
    const entitlement = await space.getEntitlement();
    const subscription = entitlement.getSubscription();
    expect(subscription).toBeDefined();
    expect(subscription?.skuId).toBe('202411-space-plan-team-tier3-appsumo-once-appsumo-seat');
  });

  it('user active appsumo tier4 license', async () => {
    const event = new AppsumoEvent();
    // use clikc active button in appsumo site
    const licenseKey = `00000000-aaaa-1111-bbbb-${generateNanoID('_', 10)}`;
    await event.handle({
      license_key: licenseKey,
      event: AppsumoEventType.Activate,
      event_timestamp: 1739783357612,
      created_at: 1739783357612,
      license_status: 'inactive',
      tier: 4,
      test: true,
      plan_id: '4',
      extra: {
        reason: 'Activated by customer',
      },
    });
    // then redirect to our site
    const license = await GiftCodeFactory.findUniqueCode(licenseKey);
    const { user, space } = await UserSO.redeemCodeByEmail(`test${generateNanoID('_', 2)}@aitable.ai`, license!.code);
    console.log('appsumo user', user.email);
    expect(user).toBeDefined();
    expect(space).toBeDefined();
    const entitlement = await space.getEntitlement();
    const subscription = entitlement.getSubscription();
    expect(subscription).toBeDefined();
    expect(subscription?.skuId).toBe('202411-space-plan-business-tier4-appsumo-once-appsumo-seat');
  });

  it('user refund for appsumo tier1 license', async () => {
    const event = new AppsumoEvent();
    // use clikc active button in appsumo site
    const licenseKey = `00000000-aaaa-1111-bbbb-${generateNanoID('_', 10)}`;
    await event.handle({
      license_key: licenseKey,
      event: AppsumoEventType.Activate,
      event_timestamp: 1739783357612,
      created_at: 1739783357612,
      license_status: 'inactive',
      tier: 1,
      test: true,
      plan_id: '1',
      extra: {
        reason: 'Activated by customer',
      },
    });
    // then redirect to our site
    const license = await GiftCodeFactory.findUniqueCode(licenseKey);
    const { space } = await UserSO.redeemCodeByEmail(`test${generateNanoID('_', 2)}@aitable.ai`, license!.code);
    await event.handle({
      license_key: licenseKey,
      event: AppsumoEventType.Deactivate,
      event_timestamp: 1739783357612,
      created_at: 1739783357612,
      license_status: 'active',
      plan_id: '1',
      test: true,
      extra: {
        reason: 'Refunded by the user',
      },
    });
    const freeSpace = await SpaceSO.init(space.id);
    const entitlement = await freeSpace.getEntitlement();
    const subscription = entitlement.getSubscription();
    expect(subscription).toBe(null);
  });

  it('user refund and buy again for appsumo tier1 license', async () => {
    const event = new AppsumoEvent();
    const licenseKey = `00000000-aaaa-1111-bbbb-${generateNanoID('_', 10)}`;
    await event.handle({
      license_key: licenseKey,
      event: AppsumoEventType.Activate,
      event_timestamp: 1739783357612,
      created_at: 1739783357612,
      license_status: 'inactive',
      tier: 1,
      test: true,
      plan_id: '1',
      extra: {
        reason: 'Activated by customer',
      },
    });
    const license = await GiftCodeFactory.findUniqueCode(licenseKey);
    const { space } = await UserSO.redeemCodeByEmail(`test${generateNanoID('_', 2)}@aitable.ai`, license!.code);
    await event.handle({
      license_key: licenseKey,
      event: AppsumoEventType.Deactivate,
      event_timestamp: 1739783357612,
      created_at: 1739783357612,
      license_status: 'active',
      plan_id: '1',
      test: true,
      extra: {
        reason: 'Refunded by the user',
      },
    });
    const newLicenseKey = `00000000-aaaa-1111-bbbb-${generateNanoID('_', 10)}`;
    await event.handle({
      license_key: newLicenseKey,
      event: AppsumoEventType.Activate,
      event_timestamp: 1739783357612,
      created_at: 1739783357612,
      license_status: 'inactive',
      tier: 1,
      test: true,
      plan_id: '1',
      extra: {
        reason: 'Activated by customer',
      },
    });
    const newLicense = await GiftCodeFactory.findUniqueCode(newLicenseKey);
    const { space: newSpace } = await UserSO.redeemCodeByEmail(
      `test${generateNanoID('_', 2)}@aitable.ai`,
      newLicense!.code,
    );
    let entitlement = await newSpace.getEntitlement();
    const newSubscription = entitlement.getSubscription();
    expect(newSubscription).toBeDefined();
    expect(newSubscription?.skuId).toBe('202411-space-plan-plus-tier1-appsumo-once-appsumo-seat');

    const freeSpace = await SpaceSO.init(space.id);
    entitlement = await freeSpace.getEntitlement();
    const subscription = entitlement.getSubscription();
    expect(subscription).toBe(null);
  });

  it('user upgrade lisence to tier2', async () => {
    const event = new AppsumoEvent();
    const licenseKey = `00000000-aaaa-1111-bbbb-${generateNanoID('_', 10)}`;
    await event.handle({
      license_key: licenseKey,
      event: AppsumoEventType.Activate,
      event_timestamp: 1739783357612,
      created_at: 1739783357612,
      license_status: 'inactive',
      tier: 1,
      test: true,
      plan_id: '1',
      extra: {
        reason: 'Activated by customer',
      },
    });
    const license = await GiftCodeFactory.findUniqueCode(licenseKey);
    const { space } = await UserSO.redeemCodeByEmail(`test${generateNanoID('_', 2)}@aitable.ai`, license!.code);
    const newLicenseKey = `00000000-aaaa-1111-bbbb-${generateNanoID('_', 10)}`;
    await event.handle({
      license_key: newLicenseKey,
      prev_license_key: licenseKey,
      event: AppsumoEventType.Upgrade,
      event_timestamp: 1739783357612,
      created_at: 1739783357612,
      license_status: 'active',
      plan_id: '2',
      tier: 2,
      test: true,
      extra: {
        reason: 'Upgrade by the user',
      },
    });
    await event.handle({
      license_key: licenseKey,
      event: AppsumoEventType.Deactivate,
      event_timestamp: 1739783357612,
      created_at: 1739783357612,
      license_status: 'inactive',
      tier: 1,
      test: true,
      plan_id: '1',
      extra: {
        reason: 'Upgrade by customer',
      },
    });

    const newSpace = await SpaceSO.init(space.id);
    const newSubscription = await newSpace.billing.getCurrentSubscription();
    expect(newSubscription).toBeDefined();
    expect(newSubscription?.skuId).toBe('202411-space-plan-pro-tier2-appsumo-once-appsumo-seat');

    const oldLicense = await GiftCodeFactory.findUniqueCode(licenseKey);
    expect(oldLicense?.status).toBe('VOID');

    const newLicenseSO = await GiftCodeFactory.findUniqueCode(newLicenseKey);
    expect(newLicenseSO?.status).toBe('USED');
  });

  it('user upgrade lisence to tier2 and refund', async () => {
    const event = new AppsumoEvent();
    const licenseKey = `00000000-aaaa-1111-bbbb-${generateNanoID('_', 10)}`;
    await event.handle({
      license_key: licenseKey,
      event: AppsumoEventType.Activate,
      event_timestamp: 1739783357612,
      created_at: 1739783357612,
      license_status: 'inactive',
      tier: 1,
      test: true,
      plan_id: '1',
      extra: {
        reason: 'Activated by customer',
      },
    });
    const license = await GiftCodeFactory.findUniqueCode(licenseKey);
    const { space } = await UserSO.redeemCodeByEmail(`test${generateNanoID('_', 2)}@aitable.ai`, license!.code);
    const newLicenseKey = `00000000-aaaa-1111-bbbb-${generateNanoID('_', 10)}`;
    await event.handle({
      license_key: newLicenseKey,
      prev_license_key: licenseKey,
      event: AppsumoEventType.Upgrade,
      event_timestamp: 1739783357612,
      created_at: 1739783357612,
      license_status: 'active',
      plan_id: '2',
      tier: 2,
      test: true,
      extra: {
        reason: 'Refunded by the user',
      },
    });
    await event.handle({
      license_key: licenseKey,
      event: AppsumoEventType.Deactivate,
      event_timestamp: 1739783357612,
      created_at: 1739783357612,
      license_status: 'inactive',
      tier: 1,
      test: true,
      plan_id: '1',
      extra: {
        reason: 'upgrade by customer',
      },
    });

    await event.handle({
      license_key: newLicenseKey,
      event: AppsumoEventType.Deactivate,
      event_timestamp: 1739783357612,
      created_at: 1739783357612,
      license_status: 'inactive',
      tier: 2,
      test: true,
      plan_id: '2',
      extra: {
        reason: 'Refunded by customer',
      },
    });

    const newSpace = await SpaceSO.init(space.id);
    const newSubscription = await newSpace.billing.getCurrentSubscription();
    expect(newSubscription).toBe(null);

    const newLicense = await GiftCodeFactory.findUniqueCode(newLicenseKey);
    expect(newLicense?.status).toBe('VOID');
  });
});

describe('Appsumo oauth test', () => {
  it('get access token', async () => {
    const client = new AppsumoClient();
    const code = generateNanoID();
    mockedAxios.request.mockResolvedValue({
      status: 200,
      data: {
        access_token: '82b35f3d810f4cf49dd7a52d4b22a594',
        token_type: 'bearer',
        expires_in: 3600,
        refresh_token: '0bac2d80d75d46658b0b31d3778039bb',
        id_token: 'eyJhbGciOiJSUzI1NiIsImtpZCI6',
        error: '',
      },
    });
    await client.getAccessToken(code);
    const property = await RemoteStorageSO.getProperty<AppsumoUserAccessTokenRemoteStorageProperty>(
      'APPSUMO_USER_ACCESS_TOKEN',
      code,
    );

    expect(property).toBeDefined();
    expect(property?.accessToken).toBe('82b35f3d810f4cf49dd7a52d4b22a594');
  });

  it('refresh access token', async () => {
    const client = new AppsumoClient();
    const code = generateNanoID();
    let retry = 0;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    mockedAxios.request.mockImplementation(async (config: any) => {
      if (config.url === 'https://appsumo.com/openid/token/' && config.data.grant_type === 'authorization_code') {
        return {
          status: 200,
          data: {
            access_token: '82b35f3d810f4cf49dd7a52d4b22a594',
            token_type: 'bearer',
            expires_in: 3600,
            refresh_token: '0bac2d80d75d46658b0b31d3778039bb',
            id_token: 'eyJhbGciOiJSUzI1NiIsImtpZCI6',
            error: '',
          },
        };
      }
      if (config.url === 'https://appsumo.com/openid/token/' && config.data.grant_type === 'refresh_token') {
        return {
          status: 200,
          data: {
            access_token: '82b35f3d810f4cf49dd7a52d4b22a594_refresh_token',
            token_type: 'bearer',
            expires_in: 3600,
            refresh_token: '0bac2d80d75d46658b0b31d3778039bb',
            id_token: 'eyJhbGciOiJSUzI1NiIsImtpZCI6_refresh_token',
            error: '',
          },
        };
      }
      if (config.url === 'https://appsumo.com/openid/license_key/') {
        if (retry === 0) {
          retry += 1;
          const error = new AxiosError('Unauthorized', '401', config, null, {
            status: 401,
            data: null,
            statusText: 'Unauthorized',
            headers: {},
            config,
          });
          error.status = 401;
          throw error;
        }
        return {
          status: 200,
          data: {
            license_key: '00000000-aaaa-1111-bbbb-abcdef012345',
            status: 'active',
            scopes: ['read_license'],
          },
        };
      }
      return {
        status: 401,
        data: null,
      };
    });
    await client.getAccessToken(code);
    const license = await client.getUserLicenseKey(code);
    // expect(mockedAxios.request).toHaveBeenCalledTimes(4);

    const property = await RemoteStorageSO.getProperty<AppsumoUserAccessTokenRemoteStorageProperty>(
      'APPSUMO_USER_ACCESS_TOKEN',
      code,
    );
    expect(property).toBeDefined();
    expect(property?.accessToken).toBe('82b35f3d810f4cf49dd7a52d4b22a594_refresh_token');

    expect(license).toBe('00000000-aaaa-1111-bbbb-abcdef012345');
  });
});
