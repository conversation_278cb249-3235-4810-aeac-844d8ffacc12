import { generateNanoID } from 'basenext/utils/nano-id';
import dayjs from 'dayjs';
import { getPlanConfigFromSkuId } from '@bika/contents/config/server/pricing/sku/sku';
import { SpaceSO } from '@bika/domains/space/server/space-so';
import { db, $Enums, PrismaPromise, SubscriptionState, BillingCustomerRelationType, Prisma } from '@bika/server-orm';
import { Stripe } from '@bika/server-orm/stripe';
import {
  BillingPlatform,
  BillingPlatformSchema,
  BillingRecurringInterval,
  BillingSpacePlanSku,
  CheckoutPrice,
  ISOCurrency,
  ISOCurrencySchema,
  SpacePlanType,
} from '@bika/types/pricing/bo';
import { SubscriptionVO } from '@bika/types/pricing/vo';
import { SubscriptionSO } from './subscription-so';
import { CustomerSO } from '../customer-so';
import { PaymentSO } from '../payment-so';
import { SubscriptionCreate, SubscriptionModel } from '../types';

export class StripeSubscriptionSO extends SubscriptionSO {
  private _model: SubscriptionModel;

  constructor(subPO: SubscriptionModel) {
    super(subPO.skuId);
    this._model = subPO;
  }

  public get model() {
    return this._model;
  }

  get id(): string {
    return this.model.id;
  }

  get platform(): $Enums.BillingPlatform {
    return this.model.platform;
  }

  /**
   * TODO 这个plan在渠道里没细分,需要重构掉或者改掉excel的配置让其唯一
   */
  private get plan(): SpacePlanType {
    return this.skuConfig.plan;
  }

  /**
   * feature.json的key就是这个去识别
   */
  get planSpec(): string {
    return this.skuConfig.spec;
  }

  get planName(): string {
    return this.skuConfig.name;
  }

  get interval(): BillingRecurringInterval {
    return this.skuConfig.interval;
  }

  get expiresAt(): Date | null {
    return this.model.expiresAt;
  }

  private async getSpace(): Promise<SpaceSO> {
    return SpaceSO.init(this._model.customerRelationId);
  }

  private async getCustomer(): Promise<CustomerSO> {
    const customer = await CustomerSO.get({
      platform: this.platform,
      customerRelationType: this.model.customerRelationType,
      customerRelationId: this.model.customerRelationId,
    });
    if (!customer) {
      throw new Error('Customer not found');
    }
    return customer;
  }

  async update(userId: string, updatedTo: BillingSpacePlanSku): Promise<void> {
    await this.updateOperation({
      skuId: updatedTo.id,
      platform: updatedTo.platform,
      interval: updatedTo.interval,
      userId,
    });
  }

  updateOperation(data: {
    skuId: string;
    platform: BillingPlatform;
    interval: BillingRecurringInterval;
    expiresAt?: Date;
    userId: string;
  }): PrismaPromise<unknown> {
    const { skuId, platform, interval, expiresAt, userId } = data;
    return db.prisma.billingSubscription.update({
      where: {
        id: this.id,
      },
      data: {
        skuId,
        platform,
        recurring: interval,
        expiresAt,
        updatedBy: userId,
        state: SubscriptionState.ACTIVE, // 更新订阅状态为激活
      },
    });
  }

  /**
   * 只更改订阅状态
   * @param state 订阅状态
   */
  async updateStatus(state: SubscriptionState) {
    await db.prisma.billingSubscription.update({
      where: {
        id: this.id,
      },
      data: {
        state,
      },
    });
  }

  /**
   * 获取最上一次的支付记录
   */
  async getLastPayment() {
    const lastPaymentModel = await db.prisma.billingSubscription.findUnique({
      where: {
        id: this._model.id,
      },
      include: {
        payments: {
          orderBy: {
            createdAt: 'desc',
          },
          take: 1,
        },
      },
    });
    if (!lastPaymentModel) {
      return null;
    }
    return new PaymentSO(lastPaymentModel.payments[0]);
  }

  /**
   * 是否已经过期了？
   *
   * @returns
   */
  isExpired(): boolean {
    if (this.expiresAt) {
      return this.expiresAt < new Date();
    }
    // 非一次性订阅，没有过期时间就代表已过期, 因为数据错误，不应该出现这种情况
    return this.interval !== 'once';
  }

  /**
   * 还剩多少日期？expiresAt - now
   */
  getRemain(unit: 'day' | 'month' | 'year' = 'day'): number {
    const now = dayjs();
    return dayjs(this.expiresAt).diff(now, unit);
  }

  /**
   * 获取Stripe的Subscription
   */
  async getStripeSubscription(): Promise<Stripe.Subscription | null> {
    if (this.model.platform === 'STRIPE') {
      if (!this._model.platformSubscriptionId) {
        return null;
      }
      return db.stripe.getSubscription(this._model.platformSubscriptionId);
    }
    return null;
  }

  async getStripeCurrency(): Promise<ISOCurrency> {
    const stripeSubscription = await this.getStripeSubscription();
    if (!stripeSubscription) {
      throw new Error('No stripe subscription found');
    }
    return ISOCurrencySchema.parse(stripeSubscription.currency.toUpperCase());
  }

  /**
   * 上报席位数
   */
  async reportSeatsToStripe(): Promise<void> {
    // 得到订阅的当前席位数, 得到空间站的总人数, 比对, 如果不一致, 则更新
    const stripeSubscription = await this.getStripeSubscription();
    if (!stripeSubscription || stripeSubscription.status !== 'active') {
      // 跟数据库的不一致, 短暂时间没有同步到系统, 跳过, 下次扫描到再处理
      return;
    }
    const item = stripeSubscription.items.data[0];
    const space = await this.getSpace();
    const memberCount = await space.getMemberCount({ includeGuest: false });
    if (item.quantity !== memberCount) {
      console.log(`${space.id} reporting seat ${memberCount} ....`);
      await db.stripe.updateSubscription(stripeSubscription.id, {
        items: [
          {
            id: stripeSubscription.items.data[0].id,
            quantity: memberCount,
          },
        ],
        // 下个账单日再扣款
        proration_behavior: 'create_prorations',
      });
    }
  }

  /**
   * 自动检查订阅状态, 提供给定时任务使用
   */
  async autoCheckStatus() {
    if (this.platform === 'STRIPE') {
      // stripe 订阅需要连接 stripe 服务器检查
      const stripeSubscription = await this.getStripeSubscription();
      if (stripeSubscription && stripeSubscription.status !== 'active') {
        // stripe 订阅状态不是 active, 需要处理
        if (stripeSubscription.status === 'canceled') {
          await this.updateStatus('CANCELLED');
        } else if (stripeSubscription.status === 'past_due') {
          await this.updateStatus('PAST_DUE');
        } else if (stripeSubscription.status === 'paused') {
          await this.updateStatus('SUSPENDED');
        }
      }
    } else if (this.interval !== 'once') {
      // 其他渠道, 非一次性订阅需要检查是否日期过期
      if (this.isExpired()) {
        // 从expires_at来看, 到现在已经过期, 需要及时更新状态
        await this.updateStatus('CANCELLED');
      }
    }
  }

  async changeStripePlan(
    userId: string,
    params: {
      checkoutPrice: CheckoutPrice;
      updatedTo: BillingSpacePlanSku;
    },
  ) {
    const { checkoutPrice, updatedTo } = params;
    // 不允许切换到免费, 只能取消
    if (updatedTo.plan === 'FREE') {
      throw new Error('Can not update to FREE plan');
    }
    // 此方法只能用于stripe订阅
    if (this.platform !== 'STRIPE') {
      throw new Error('Only stripe subscription can be changed in this way');
    }
    // 无论升级或降级, 更改订阅的SKU即可
    if (updatedTo.platform !== 'STRIPE') {
      // 只能升级到stripe的订阅
      throw new Error('Invalid SKU type for this subscription');
    }
    // 对应的stripe订阅信息
    const stripeSubscription = await this.getStripeSubscription();
    if (!stripeSubscription) {
      throw new Error('No stripe subscription found');
    }
    if (this.skuConfig.id === updatedTo.id) {
      // 未更改计划
      throw new Error(`switch to the same plan ${updatedTo.id}`);
    }
    // 改变sku,不管是升级还是降级, 或者月付和年付之间的切换, 都是更改订阅的SKU
    const item = stripeSubscription.items.data[0];
    const currencyCode = ISOCurrencySchema.parse(stripeSubscription.currency.toUpperCase());
    const priceData = await db.stripe.skuToPrice(updatedTo, currencyCode);
    // 这里只调用stripe去更改, 然后成功扣款后在回调事件里处理订阅计划
    const paymentId = generateNanoID('pay');
    await db.stripe.updateSubscription(stripeSubscription.id, {
      items: [
        {
          id: item.id,
          deleted: true,
        },
        {
          price_data: priceData,
          quantity: item.quantity,
        },
      ],
      metadata: {
        skuId: updatedTo.id,
        paymentId,
        userId,
      },
      // 立即扣取金额
      proration_behavior: 'always_invoice',
    });
    // 新建一条支付记录, 用于关联更新订阅的支付记录
    const customer = await this.getCustomer();
    const payment = await PaymentSO.createOperation({
      userId,
      paymentId,
      skuConfig: updatedTo,
      checkoutPrice,
      customer,
    });
    return payment.id;
  }

  async updatePlan(userId: string, updatedTo: BillingSpacePlanSku) {
    await this.updateOperation({
      skuId: updatedTo.id,
      platform: updatedTo.platform,
      interval: updatedTo.interval,
      userId,
    });
  }

  /**
   * 用户主动取消订阅
   * @param userId 操作用户ID
   * @param cancelAtTheEndOfPeriod 是否在到期日自动取消
   */
  async cancel(userId: string, cancelAtTheEndOfPeriod?: boolean): Promise<void> {
    let updateData: Prisma.BillingSubscriptionUpdateInput = {
      updatedBy: userId,
      updatedAt: new Date(),
    };
    if (cancelAtTheEndOfPeriod) {
      updateData = {
        ...updateData,
        cancelAt: new Date(),
        cancelAtPeriodEnd: true,
      };
    } else {
      updateData = {
        ...updateData,
        state: SubscriptionState.CANCELLED,
        canceledAt: new Date(),
      };
    }
    if (this.platform === 'STRIPE') {
      // 取消Stripe订阅,让订阅到账单日自动取消, 当前操作会立即触发`customer.subscription.updated`事件
      // 然后在到期日触发`customer.subscription.deleted`事件, 事件里处理取消订阅
      const stripeSubscriptionId = this.model.platformSubscriptionId;
      if (!stripeSubscriptionId) {
        // 无法取消, stripe数据不完整
        throw new Error(`Can't cancel subscription ${this.model.id}, missing stripe subscription id`);
      }
      await db.prisma.$transaction(async (tx) => {
        await tx.billingSubscription.update({
          where: {
            id: this.model.id,
          },
          data: updateData,
        });
        await db.stripe.cancelSubscription(stripeSubscriptionId, cancelAtTheEndOfPeriod);
      });
    } else {
      // 其他渠道没有对接支付平台的订阅，直接取消
      await this.cancelImmediately({ canceledAt: new Date(), canceledBy: userId });
    }
  }

  /**
   * 立即取消订阅, 如果是客户主动取消，使用`cancel`方法
   */
  async cancelImmediately(data?: { canceledAt?: Date; canceledBy?: string }): Promise<void> {
    const { canceledAt = new Date(), canceledBy } = data || {};
    await db.prisma.billingSubscription.update({
      where: {
        id: this._model.id,
      },
      data: {
        state: SubscriptionState.CANCELLED,
        canceledAt,
        updatedBy: canceledBy,
      },
    });
  }

  /**
   * 恢复订阅
   */
  async resume(userId: string): Promise<void> {
    if (this.platform !== 'STRIPE') {
      throw new Error('Only stripe subscription can be resumed');
    }
    if (this.model.cancelAtPeriodEnd !== true) {
      throw new Error('Subscription is not canceled, can not resume');
    }
    const stripeSubscriptionId = this.model.platformSubscriptionId;
    if (!stripeSubscriptionId) {
      throw new Error('No stripe subscription id found');
    }
    // 安排不取消订阅, 这里就不处理stripe的回调事件了, 直接改数据库即可
    await db.prisma.$transaction(async (tx) => {
      await tx.billingSubscription.update({
        where: {
          id: this.model.id,
        },
        data: {
          cancelAt: null,
          cancelAtPeriodEnd: false,
          updatedBy: userId,
        },
      });
      await db.stripe.revokeCancelSubscription(stripeSubscriptionId);
    });
  }

  /**
   * 如果SKU是plan，那么获取订阅类型 PLUS/PRO
   */
  toVO(): SubscriptionVO {
    if (this._model.skuType !== 'SPACE_PLAN') {
      throw new Error('Subscription must be a PLAN');
    }
    return {
      id: this.model.id,
      skuId: this.skuId,
      platform: BillingPlatformSchema.parse(this.platform),
      plan: this.plan,
      planName: this.planName,
      interval: this.interval,
      expireAt: this._model.expiresAt?.toISOString(),
      cancelAtPeriodEnd: this._model.cancelAtPeriodEnd,
      features: this.getPlanFeature().featureMap,
    };
  }

  /**
   * 找到大于指定游标的下一个激活的Stripe订阅
   * @param cursor 游标号码
   */
  static async findNextActiveStripeSubscriptions(cursor: number, limit: number = 1): Promise<StripeSubscriptionSO[]> {
    const subs = await db.prisma.billingSubscription.findMany({
      where: {
        cursor: {
          gt: cursor,
        },
        platform: 'STRIPE',
        platformSubscriptionId: { not: null },
        state: SubscriptionState.ACTIVE,
      },
      orderBy: {
        // 升序查询
        cursor: 'asc',
      },
      take: limit,
    });
    return subs.map((sub) => new StripeSubscriptionSO(sub));
  }

  /**
   * 找到大于指定游标的下一个激活的订阅
   * @param cursor 游标
   * @returns subscription so or null
   */
  static async findNextActiveSubscriptions(cursor: number, limit: number = 1): Promise<StripeSubscriptionSO[]> {
    const subs = await db.prisma.billingSubscription.findMany({
      where: {
        cursor: {
          gt: cursor,
        },
        state: SubscriptionState.ACTIVE,
      },
      orderBy: {
        // 升序查询
        cursor: 'asc',
      },
      take: limit,
    });
    return subs.map((sub) => new StripeSubscriptionSO(sub));
  }

  /**
   * 获取空间站的激活订阅
   */
  static async getSpaceSubscription(spaceId: string): Promise<StripeSubscriptionSO | null> {
    const subs = await db.prisma.billingSubscription.findMany({
      where: {
        customerRelationType: 'SPACE',
        customerRelationId: spaceId,
        skuType: 'SPACE_PLAN',
        state: SubscriptionState.ACTIVE,
      },
    });
    if (subs.length > 1) {
      throw new Error('More than one active subscription found');
    }
    return subs.length === 1 ? new StripeSubscriptionSO(subs[0]) : null;
  }

  /**
   * 获取指定平台的订阅
   */
  static async getByPlatformSubscriptionId(platform: BillingPlatform, platformSubscriptionId: string) {
    const subPO = await db.prisma.billingSubscription.findUnique({
      where: {
        platform_platformSubscriptionId: {
          platform,
          platformSubscriptionId,
        },
      },
    });
    return subPO && new StripeSubscriptionSO(subPO);
  }

  /**
   * 检查是否已经有一个激活的订阅
   */
  static async checkActiveSubscription(customerRelationType: BillingCustomerRelationType, customerRelationId: string) {
    // 创建之前检查一下,保证空间站只有一个激活的订阅
    const count = await db.prisma.billingSubscription.count({
      where: {
        customerRelationType,
        customerRelationId,
        state: SubscriptionState.ACTIVE,
      },
    });
    if (count > 0) {
      throw new Error(`Has already an active subscription for ${customerRelationType}:${customerRelationId}`);
    }
  }

  /**
   * 创建订阅
   * @param createParam 创建订阅的参数
   * @returns 创建订阅的数据库操作
   */
  static createOperation(createParam: SubscriptionCreate): PrismaPromise<SubscriptionModel> {
    const { userId, skuId, customer, subscription, periodStart, periodEnd, payments } = createParam;
    const id = generateNanoID('sub');
    const skuConfig = getPlanConfigFromSkuId(skuId);
    const { interval, intervalCount = 1 } = subscription;
    let expiresAt = periodEnd;
    if (!periodEnd) {
      expiresAt = interval === 'once' ? undefined : dayjs().add(intervalCount, interval).toDate();
    }
    return db.prisma.billingSubscription.create({
      data: {
        id,
        customerRelationType: customer.type,
        customerRelationId: customer.id,
        skuType: skuConfig.skuType,
        skuId,
        platform: subscription.platform, // 注意, 超级管理员强制改订阅可设置BIKA, 这样就不会检查stripe订阅
        platformSubscriptionId: subscription.subscriptionId,
        recurring: subscription.interval,
        state: SubscriptionState.ACTIVE,
        createdBy: userId,
        expiresAt,
        payments: payments ? { connect: payments.map((paymentId) => ({ id: paymentId })) } : undefined,
      },
    });
  }

  /**
   * 创建订阅
   * @param createParam 订阅创建参数
   * @returns subscription so
   */
  static async createSubscription(createParam: SubscriptionCreate): Promise<StripeSubscriptionSO> {
    const PO = await this.createOperation(createParam);
    return new StripeSubscriptionSO(PO);
  }
}
