import { generateNanoID } from 'basenext/utils/nano-id';
import { SpaceSO } from '@bika/domains/space/server/space-so';
import { db, BillingCustomerRelationType, BillingPlatform } from '@bika/server-orm';
import { Stripe } from '@bika/server-orm/stripe';
import { Rewardful } from '@bika/types/pricing/dto';
import { CustomerModel } from './types';

/**
 * 顾客，每一个space，都是一个顾客
 *
 * 即，每个顾客customer = space
 *
 */
export class CustomerSO {
  private _model: CustomerModel;

  private _stripeModel?: Stripe.Customer;

  constructor(cusPO: CustomerModel, stripeModel?: Stripe.Customer) {
    this._model = cusPO;
    this._stripeModel = stripeModel;
  }

  public get model() {
    return this._model;
  }

  get id() {
    return this.model.id;
  }

  get releationType() {
    return this.model.customerRelationType;
  }

  get relationId() {
    return this.model.customerRelationId;
  }

  async getStripeModel() {
    if (!this._stripeModel) {
      this._stripeModel = await db.stripe.getCustomer(this._model.platformCustomerId);
    }
    return this._stripeModel;
  }

  /**
   * 更新stripe customer元数据
   */
  async addStripeCustomerMetadata(metadata: { [key: string]: string }) {
    const stripeCustomer = await this.getStripeModel();
    await db.stripe.addCustomerMetadata(stripeCustomer.id, metadata);
  }

  async getPortalUrl() {
    const stripeModel = await this.getStripeModel();
    const returnUrl = `${process.env.APP_HOSTNAME}/auth`;
    const portal = await db.stripe.createCustomerPortal(stripeModel.id, returnUrl);
    return portal.url;
  }

  /**
   * 获取指定平台的客户对象
   *
   * @returns CustomerSO | null
   */
  public static async getByPlatformCustomerId(platformCustomerId: string, platform: BillingPlatform = 'STRIPE') {
    const customerPO = await db.prisma.billingCustomer.findUnique({
      where: {
        platform_platformCustomerId: {
          platform,
          platformCustomerId,
        },
      },
    });
    return customerPO && new CustomerSO(customerPO);
  }

  private static async buildCustomerInfo(spaceId: string) {
    const space = await SpaceSO.init(spaceId);
    const admin = await space.getOwner();
    // 拿主管理员的邮箱, 不然随便一个成员点了订阅又不支付,就变成他的邮箱, 不合常理
    const email = admin.email;
    const name = `${space.id}`;
    if (!email || email.includes('private')) {
      throw new Error(`Super Admin has no email`);
    }
    return { name, email };
  }

  /**
   * 获取客户
   */
  public static async get(data: {
    platform?: BillingPlatform;
    customerRelationType: BillingCustomerRelationType;
    customerRelationId: string;
  }): Promise<CustomerSO | null> {
    const { platform = 'STRIPE', customerRelationType, customerRelationId } = data;
    const customerPO = await db.prisma.billingCustomer.findUnique({
      where: {
        platform_customerRelationType_customerRelationId: {
          platform,
          customerRelationType,
          customerRelationId,
        },
      },
    });
    return customerPO && new CustomerSO(customerPO);
  }

  /**
   * 创建客户
   */
  public static async create(data: {
    platform?: BillingPlatform;
    customerRelationType: BillingCustomerRelationType;
    customerRelationId: string;
    userId: string;
    rewardful?: Rewardful;
  }): Promise<CustomerSO> {
    const { platform = 'STRIPE', customerRelationType, customerRelationId, userId, rewardful } = data;
    const { name, email } = await this.buildCustomerInfo(customerRelationId);

    return db.stripe
      .createCustomer({
        name,
        email,
        customerRelationType,
        customerRelationId,
        creator: userId,
        referral: rewardful?.referral,
        coupon: rewardful?.coupon,
      })
      .then(async (customer) =>
        db.prisma.billingCustomer
          .create({
            data: {
              id: generateNanoID('cus'),
              platform,
              platformCustomerId: customer.id,
              customerRelationType,
              customerRelationId,
              name,
              email,
            },
          })
          .then((model) => new CustomerSO(model, customer))
          .catch((err) => {
            throw new Error(`Create customer failed: ${err.message}`);
          }),
      )
      .catch((err) => {
        throw new Error(`Create platform customer failed: ${err.message}`);
      });
  }
}
