import assert from 'assert';
import { generateNanoID } from 'basenext/utils/nano-id';
import { UserSO } from '@bika/domains/user/server/user-so';
import { BillingPaymentStatus, db, PrismaPromise } from '@bika/server-orm';
import { Stripe } from '@bika/server-orm/stripe';
import {
  BillingSKUConfig,
  BillingSKUConfigSchema,
  CheckoutPrice,
  CheckoutPriceSchema,
  PaymentStatus,
} from '@bika/types/pricing/bo';
import { PaymentVO } from '@bika/types/pricing/vo';
import { CustomerSO } from './customer-so';
import { PaymentModel } from './types';

export enum PaymentPlatformId {
  ADMIN = 'ADMIN', // 管理员霸道免单支付
  STRIPE = 'STRIPE', // 第三方Stripe支付
}

/**
 * 支付记录
 */
export class PaymentSO {
  private _model: PaymentModel;

  constructor(model: PaymentModel) {
    this._model = model;
  }

  public get model() {
    return this._model;
  }

  get id() {
    return this.model.id;
  }

  get status() {
    return this.model.status;
  }

  get customerId() {
    return this.model.customerId;
  }

  get skuConfig() {
    return BillingSKUConfigSchema.parse(this.model.skuConfig);
  }

  get checkoutPrice() {
    return CheckoutPriceSchema.parse(this.model.checkoutPrice);
  }

  // /**
  //  * 用于确认纯BKC支付的情况，仅仅多了assert判断
  //  *
  //  * @param userId
  //  * @param skuConfig
  //  * @param checkoutPrice
  //  * @returns
  //  */
  // public static async createWithBikaCoin(
  //   userId: string,
  //   customer: CustomerSO,
  //   skuConfig: BillingSKUConfig,
  //   checkoutPrice: CheckoutPrice,
  // ): Promise<{ payment: PaymentSO; stripeCheckoutSession: Stripe.Checkout.Session | null }> {
  //   assert(checkoutPrice.amount === 0, 'Checkout Price money must be 0');
  //   assert(skuConfig.id === checkoutPrice.sku.id, 'SKU Config ID must match checkout price SKU ID');
  //   return this.create(userId, customer, skuConfig, checkoutPrice);
  // }

  /**
   * 根据 ID 找出
   *
   * @returns payment object
   */
  public static async init(id: string): Promise<PaymentSO | null> {
    const paymentPO = await db.prisma.billingPayment.findUnique({
      where: {
        id,
      },
    });
    return paymentPO && new PaymentSO(paymentPO);
  }

  /**
   * 创建支付记录的操作
   * @param data 创建参数
   * @returns 支付记录
   */
  static createOperation(data: {
    userId: string;
    paymentId?: string;
    customer: CustomerSO;
    skuConfig: BillingSKUConfig;
    checkoutPrice: CheckoutPrice;
    session?: Stripe.Checkout.Session;
    status?: BillingPaymentStatus;
  }): PrismaPromise<PaymentModel> {
    const { userId, paymentId, customer, skuConfig, checkoutPrice, session, status = 'PENDING' } = data;
    return db.prisma.billingPayment.create({
      data: {
        id: paymentId ?? generateNanoID('pay'),
        skuConfig,
        checkoutPrice,
        customerId: customer.id,
        metadata: {
          stripeSessionId: session?.id,
        },
        status,
        createdBy: userId,
        updatedBy: userId,
      },
    });
  }

  bindSubscription(subscriptionId: string): PrismaPromise<unknown> {
    return db.prisma.billingPayment.update({
      where: {
        id: this.model.id,
      },
      data: {
        subscriptionId,
      },
    });
  }

  updateStatusOperation(userId: string, status: BillingPaymentStatus) {
    return db.prisma.billingPayment.update({
      where: {
        id: this.id,
      },
      data: {
        status,
        updatedBy: userId,
      },
    });
  }

  /**
   * 设置支付状态，并进行Bika Coin 扣！钱！操作
   *
   * @param newStatus
   * @returns
   */
  public async setStatus(userId: string, newStatus: BillingPaymentStatus) {
    if (newStatus === 'EXPIRED') {
      // 必须是pending状态
      assert(this.model.status === 'PENDING', `Payment must be pending to set expired: ${this.model.id}`);
    } else if (newStatus === 'SUCCESS') {
      assert(this.model.status === 'PENDING', `Payment must be pending to set success: ${this.model.id}`);

      // 先进行真是扣钱
      const userSO = await UserSO.init(this.model.createdBy);
      const userAccount = await userSO.coins.getAccount();
      if (this.checkoutPrice.byCoins > 0) {
        assert(
          this.checkoutPrice.byCoinsCredit > 0 || this.checkoutPrice.byCoinsCurrency > 0,
          'Coins must be credit or currency',
        );
        if (this.checkoutPrice.byCoinsCredit > 0) {
          await userAccount.redeem(
            this.checkoutPrice.byCoinsCredit,
            {
              reason: 'redeem-space-plan',
              paymentId: this.model.id,
            },
            undefined,
            'CREDIT',
          );
        }
        if (this.checkoutPrice.byCoinsCurrency > 0) {
          await userAccount.redeem(
            -this.checkoutPrice.byCoinsCurrency,
            {
              reason: 'redeem-space-plan',
              paymentId: this.model.id,
            },
            undefined,
            'CURRENCY',
          );
        }
      }
    }

    this.model.status = newStatus;
    this.updateStatusOperation(userId, newStatus);
  }

  toVO(): PaymentVO {
    return {
      paymentId: this.model.id,
      subscriptionId: this.model.subscriptionId,
      status: this.model.status as PaymentStatus,
    };
  }
}
