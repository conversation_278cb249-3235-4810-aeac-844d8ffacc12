import assert from 'assert';
import { generateNanoID } from 'basenext/utils/nano-id';
import _ from 'lodash';
import { AttachmentModel } from '@bika/domains/attachment/server/types';
import { NodeSO } from '@bika/domains/node/server/node-so';
import { NodeResourceSO } from '@bika/domains/node/server/types';
import { SpaceSO } from '@bika/domains/space/server/space-so';
import { UserSO } from '@bika/domains/user/server';
import { PrismaPromise, Prisma, db, $Enums } from '@bika/server-orm';
import { AiPageNodeBO, AiPageNodeBOSchema } from '@bika/types/ai/bo';
import type { AIPageNodeUpdateDTO, AIPageNodeCreateDTO } from '@bika/types/ai/dto';
import { AiPageVO } from '@bika/types/ai/vo';
import { CONST_PREFIX_AI_PAGE } from '@bika/types/database/vo';
import type { ToBoOptions, NodeResourceType, ToTemplateOptions } from '@bika/types/node/bo';
import type { NodeRenderOpts } from '@bika/types/node/vo';
import { iString } from '@bika/types/system';

const _AIPageInclude = Prisma.validator<Prisma.AiPageDefaultArgs>()({
  include: {
    node: true,
  },
});

export type AiPageModel = Prisma.AiPageGetPayload<typeof _AIPageInclude>;

export class AIPageSO extends NodeResourceSO {
  private _model: AiPageModel;

  get resourceType(): NodeResourceType {
    return 'PAGE';
  }

  public get id() {
    return this._model.id;
  }

  public get model(): AiPageModel {
    return this._model;
  }

  public get templateId(): string | undefined {
    return this._model.templateId || undefined;
  }

  private constructor(private readonly fileNodePO: AiPageModel) {
    super();
    this._model = fileNodePO;
  }

  public static async init(id: string) {
    const fileNodePO = await db.prisma.aiPage.findUnique({
      where: { id },
      include: {
        node: true,
      },
    });
    assert(fileNodePO, `not found file node by id: ${id}`);

    return new AIPageSO(fileNodePO);
  }

  toNodeSO(): NodeSO {
    return NodeSO.initWithModel(this.model.node!);
  }

  async getSpace(): Promise<SpaceSO> {
    return SpaceSO.init(this._model.spaceId);
  }

  async toBO(_opts?: ToBoOptions): Promise<AiPageNodeBO> {
    const saveBO = this._model.bo as AiPageNodeBO;
    return {
      ...saveBO,
      id: this.id,
      name: this.model.name as iString,
      description: this.model.description as iString,
      icon: this.toNodeSO().icon,
      resourceType: 'PAGE',
      data: saveBO?.data,
      // content: saveBO?.content ?? '',
    };
  }

  async toVO(_opts?: NodeRenderOpts): Promise<AiPageVO> {
    // throw new Error('Method not implemented.');
    // const attachmentSO = await AttachmentSO.init(this._model.attachmentId);

    const saveBO = this._model.bo as AiPageNodeBO;
    // const attachmentVO = attachmentSO.toVO();
    return {
      id: this.id,
      // name: this._model.name as iString,
      // description: this._model.description as iString,
      resourceType: 'PAGE',
      // attachment: attachmentVO,
      // url: attachmentSO.fullPath,

      data: saveBO?.data,
      // content: (this._model.bo as AiPageNodeBO)?.content,
    };
  }

  async toTemplate(_opts?: ToTemplateOptions): Promise<AiPageNodeBO> {
    const file = _.omit(await this.toBO(), 'id');
    if (!file.templateId) {
      file.templateId = this.id;
    }
    return file;
  }

  async setTemplateId(): Promise<PrismaPromise<Prisma.BatchPayload>[]> {
    const operations: PrismaPromise<Prisma.BatchPayload>[] = [];
    if (!this.templateId) {
      operations.push(
        ...[
          db.prisma.aiPage.updateMany({
            where: { id: this.id },
            data: {
              templateId: this.id,
            },
          }),
          db.prisma.node.updateMany({
            where: { id: this.id },
            data: {
              templateId: this.id,
            },
          }),
        ],
      );
    }
    return operations;
  }

  async updateWithNodeInput(
    user: UserSO,
    data: AIPageNodeUpdateDTO,
  ): Promise<{
    operations: PrismaPromise<AiPageModel | AttachmentModel>[];
    // mongoSessions: MongoTransactionCB[];
  }> {
    const { name, description, bo, templateId } = data;
    const realBO: AiPageNodeBO | undefined = bo
      ? {
          ...bo,
          resourceType: 'PAGE',
          name: name || (this._model.name as iString),
          description,
          templateId,
          data: bo.data,
        }
      : undefined;

    // 修改文件的附件
    const operations: PrismaPromise<AiPageModel | AttachmentModel>[] = [
      db.prisma.aiPage.update({
        where: {
          id: this.id,
        },
        data: {
          name,
          description,
          updatedBy: user.id,
          // TODO: 动态更新
          bo: realBO,
          node: {
            update: {
              name,
              description,
              updatedBy: user.id,
            },
          },
        } as Prisma.MirrorUpdateInput,
        include: {
          node: true,
        },
      }),
    ];

    return { operations };
  }

  static async boToCreateInput(
    user: UserSO,
    space: SpaceSO,
    nodeId: string,
    dto: AIPageNodeCreateDTO,
  ): Promise<{
    createInput: Prisma.AiPageUncheckedCreateNestedOneWithoutNodeInput | undefined;
  }> {
    const { resourceType, name, description, templateId, bo } = dto;
    if (resourceType === 'PAGE') {
      const realBO: AiPageNodeBO | undefined = bo
        ? {
            ...bo,
            resourceType,
            name,
            description,
            templateId,
            data: bo.data,
          }
        : undefined;

      const createInput: Prisma.AiPageUncheckedCreateNestedOneWithoutNodeInput = {
        create: {
          spaceId: space.id,
          name,
          description,
          templateId,
          createdBy: user.id,
          updatedBy: user.id,
          bo: realBO || Prisma.JsonNull,
        },
      };
      return { createInput };
    }
    return { createInput: undefined };
  }

  static createWithNodeInput(
    userId: string,
    aiPageNode: AiPageNodeBO,
    params: {
      spaceId: string;
      parentId: string;
      preNodeId?: string;
      nextNodeId?: string;
      unitId?: string;
      teamId?: string;
    },
  ): { id: string; operation: PrismaPromise<AiPageModel> } {
    const id = aiPageNode.id || generateNanoID(CONST_PREFIX_AI_PAGE);
    const operation: PrismaPromise<AiPageModel> = db.prisma.aiPage.create({
      data: {
        spaceId: params.spaceId,
        name: aiPageNode.name,
        description: aiPageNode.description,
        templateId: aiPageNode.templateId,
        createdBy: userId,
        updatedBy: userId,
        bo: AiPageNodeBOSchema.parse(aiPageNode),
        node: {
          create: {
            id,
            icon: aiPageNode.icon,
            templateId: aiPageNode.templateId,
            name: aiPageNode.name,
            description: aiPageNode.description,
            type: $Enums.NodeResourceType.PAGE,
            createdBy: userId,
            updatedBy: userId,
            spaceId: params.spaceId,
            parentId: params.parentId,
            preNodeId: params.preNodeId,
            unitId: params.unitId,
          },
        },
      },
      include: {
        node: true,
      },
    });
    return { id, operation };
  }
}
