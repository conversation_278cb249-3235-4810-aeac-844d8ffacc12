import fs from 'fs';
import os from 'os';
import path from 'path';
import { pipeline, Readable } from 'stream';
import { promisify } from 'util';
import { generateNanoID } from 'basenext/utils/nano-id';
import { HTTPException } from 'hono/http-exception';
import { DatabaseSO } from '@bika/domains/database/server/database-so';

const pipe = promisify(pipeline);

export class OpenAPIUtil {
  static async checkAndGetDatabase(databaseId: string) {
    try {
      // 此处需要保留 `await`，因为需要覆盖 `DatabaseSO.init()` 的异常
      return await DatabaseSO.init(databaseId);
    } catch (_) {
      throw new HTTPException(301, { message: 'Database not found' });
    }
  }

  /**
   * 创建临时文件，返回路径和清理函数
   *
   * @param file File
   * @returns path and clean()
   */
  static async createTmpByFile(file: File) {
    const tmpPath = path.join(os.tmpdir(), generateNanoID(''));
    const tmpFilePath = path.join(tmpPath, file.name);

    // 清理函数，清理临时创建的目录
    const clean = async () => fs.promises.rm(tmpPath, { recursive: true, force: true });

    // 创建临时目录和文件
    try {
      await fs.promises.mkdir(tmpPath, { recursive: true });

      const fileStream = Readable.from(file.stream() as unknown as Iterable<Uint8Array>);
      const tmpFileStream = fs.createWriteStream(tmpFilePath);
      await pipe(fileStream, tmpFileStream);

      return {
        path: tmpFilePath,
        clean,
      };
    } catch (e) {
      await clean();
      throw e;
    }
  }
}
