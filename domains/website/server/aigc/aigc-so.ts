import assert from 'assert';
import axios from 'axios';
import axiosRetry from 'axios-retry';
import matter from 'gray-matter';
import _ from 'lodash';
import React from 'react';
import ReactMarkdown from 'react-markdown';
import rehypeHighlight from 'rehype-highlight';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';
import { z } from 'zod';
import type { Locale } from '@bika/contents/i18n/config';
import { MarkdownRenderer } from '@bika/server-orm/content/types';
import { getAppEnv } from 'sharelib/app-env';

axiosRetry(axios, { retries: 3 });

export interface AINewsList {
  name: string;
  description: string;
  path: string;
  origin: string;
  slug: string;
  lang: string;
}
let AINEWS_CACHE_DATA: Record<string, AINewsList[]>;
const AINEWS_CACHE_DATE = 0;

export const AigcBlogSchema = z.object({
  title: z.string(),
  date: z.string(),
  author: z.string(),
  cover: z.string(),
  url: z.string(),
  // Hidden it on Blog List
  hidden: z.boolean().optional(),
});

export const AigcBlogListSchema = z.array(AigcBlogSchema);

export type AigcBlog = z.infer<typeof AigcBlogSchema>;
/**
 * 统一加载AI生成的内容，包括策略生成等
 *
 *
 * 至于AIGC的生成器(generator)，不放主工程里
 *
 */
export class AIGCSO {
  static AIGC_BLOG_LIST_CACHE: AigcBlog[];

  static AIGC_CACHE_DATE: number = 0;

  /**
   * 获取元信息，通常用于文章的related links
   *
   * @param take
   * @returns
   */
  // public static async getRandomBlogsLinks(locale: Locale, take: number = 5) {
  //   const blogs = await this.getRandomBlogs(locale, take);
  //   return blogs.map((it) => ({
  //     url: it.url,
  //     text: it.title,
  //   }));
  // }

  /**
   * 随机获取几篇AIGC Blog 的Links，用于文章的related links
   *
   * @param take
   * @returns
   */
  public static async getRandomBlogsLinks(locale: Locale, take: number = 5): Promise<{ url: string; text: string }[]> {
    const blogs = await this.getAigcBlogList();
    // 打乱，方便进行随机
    const shuffleBlogs = _.shuffle(blogs);
    const links: { url: string; text: string }[] = [];
    for (const blog of shuffleBlogs) {
      const spilt = blog.url.split('/');
      const urlLocale = spilt[0];
      if (locale === urlLocale) {
        const others = spilt.slice(1).join('/');

        const url = `/${locale}/blog/${others}`;

        links.push({
          url,
          text: blog.title || 'Unammed Blog',
        });
      }

      if (links.length >= take) {
        break;
      }
    }

    return links;
  }

  private static async refreshAigcCache() {
    // 如果是自建环境，不加载AIGC Blog
    if (getAppEnv() === 'SELF-HOSTED') {
      // 自建环境不加载AIGC Blog
      this.AIGC_BLOG_LIST_CACHE = [];
      this.AIGC_CACHE_DATE = new Date().getTime();
      return;
    }
    // 从 https://book.bika.ai/bika-content/blog-aigc/sitemap.json 获取 AIGC Blog 列表
    const data = await axios.get('https://book.bika.ai/bika-content/blog-aigc/sitemap.json', { timeout: 240000 }); // 4mins, 240s

    const ret: AigcBlog[] = [];
    for (const item of data.data) {
      const parsed = AigcBlogSchema.safeParse(item); //  as AigcBlog[]; // (await data.json()) as AigcBlog[];
      if (!parsed.success) {
        console.warn('[WARNING] AIGCSO.getAigcBlogList: Invalid AIGC Blog data: ', item, parsed.error);
        continue;
      }
      ret.push(parsed.data);
    }

    this.AIGC_BLOG_LIST_CACHE = ret;
    this.AIGC_CACHE_DATE = new Date().getTime();
  }

  /**
   * AIGC Blog (bika-content) list
   * @returns
   */
  public static async getAigcBlogList(): Promise<AigcBlog[]> {
    // 10 分钟缓存
    if (Date.now() - this.AIGC_CACHE_DATE < 1000 * 60 * 10) {
      if (Date.now() - this.AIGC_CACHE_DATE > 1000 * 60 * 7) {
        // 已经超过 7 分钟没刷新了，异步刷新
        this.refreshAigcCache(); // 异步刷新
      }

      // cache 10 分钟
      return this.AIGC_BLOG_LIST_CACHE;
    }

    await this.refreshAigcCache(); //  等待刷新

    return this.AIGC_BLOG_LIST_CACHE;
  }

  public static async getAigcBlog(lang: string, slug: string): Promise<MarkdownRenderer> {
    try {
      const aigcBlogs = await this.getAigcBlogList();
      const url = `${lang}/${slug}`;
      const aigcBlog = aigcBlogs.find((it) => it.url === url);
      // console.log(aigcBlogs, slug);
      // fs.writeFileSync('aigcBlog.json', JSON.stringify(aigcBlogs, null, 2));
      assert(aigcBlog, `Not Found: ${url}`);
      const response = await axios.get(`https://book.bika.ai/bika-content/blog-aigc/${url}.mdx`, { timeout: 120000 });
      if (response.status !== 200) {
        throw new Error(
          `Error on: https://book.bika.ai/bika-content/blog-aigc/${url}.mdx , status: ${response.status}`,
        );
      }
      const markdown = matter(response.data);

      const ReactDOMServer = (await import('react-dom/server')).default;
      const html = ReactDOMServer.renderToStaticMarkup(
        React.createElement(
          ReactMarkdown,
          {
            rehypePlugins: [rehypeRaw, rehypeHighlight],
            remarkPlugins: [remarkGfm],
          },
          markdown.content,
        ),
      );

      return {
        slug: aigcBlog.url,
        meta: {
          date: markdown.data?.date || new Date().toISOString(),
          title: aigcBlog.title,
          description: aigcBlog.title,
        },
        content: html,
      };
    } catch (e) {
      console.error(e);
      throw e;
    }
  }

  public static async getNewsList(): Promise<Record<string, AINewsList[]>> {
    if (Date.now() - AINEWS_CACHE_DATE < 1000 * 60 * 5) {
      return AINEWS_CACHE_DATA;
    }

    if (getAppEnv() === 'SELF-HOSTED') {
      // 自建环境不加载AIGC Blog
      return AINEWS_CACHE_DATA;
    }

    // https://vikadata.github.io/apis/api.json
    const data = await fetch('https://vikadata.github.io/apis/api.json');
    const json = await data.json();
    const newsList: Record<string, AINewsList[]> = {
      'zh-CN': [],
      en: [],
      'zh-TW': [],
      ja: [],
    };
    const langMap: Record<string, string> = {
      cn: 'zh-CN',
      us: 'en',
      ca: 'en',
      hk: 'zh-TW',
    };
    json.forEach((item: any) => {
      const lang = langMap[item.language];
      if (lang && newsList[lang]) {
        // 从 /apis/us/xxx.md 取 xxx
        const slug = item.path
          .split('/')
          .pop()
          .replace('.md', '')
          .replace(/AITable.ai/gi, 'Bika.ai');
        newsList[lang].push({
          name: item.name.replace(/AITable.ai/gi, 'Bika.ai'),
          slug,
          description: item.description.replace(/<[^>]+>/g, '').replace(/AITable.ai/gi, 'Bika.ai'),
          // 去除 /apis/us/
          origin: item.path,
          path: item.path.replace(/AITable.ai/gi, 'Bika.ai'),
          lang,
        });
      }
    });
    AINEWS_CACHE_DATA = newsList;
    return newsList;
  }

  public static async getNewsFromPath(lang: string, slug: string): Promise<MarkdownRenderer> {
    const news = await this.getNewsList();
    const list = news[lang];
    const item = list.find((it) => it.slug === slug && it.lang === lang);

    if (item) {
      const data = await fetch(`https://vikadata.github.io${item.origin}`);
      const content = await data.text();

      const timeRegex = /-+\s*(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\s*-+/;
      const timeMatch = content.match(timeRegex);
      const time = timeMatch ? timeMatch[1] : null;

      // 删除时间和分隔线
      const updatedMarkdown = content.replace(timeRegex, '').trim();
      // 渲染成html 和 blog 保持一致
      const ReactDOMServer = (await import('react-dom/server')).default;

      const html = ReactDOMServer.renderToStaticMarkup(
        React.createElement(
          ReactMarkdown,
          {
            rehypePlugins: [rehypeRaw, rehypeHighlight],
            remarkPlugins: [remarkGfm],
          },
          updatedMarkdown,
        ),
      );
      return {
        slug: item.name.replace(/AITable.ai/gi, 'Bika.ai'),
        meta: {
          date: time || new Date().toISOString(),
          title: item.name.replace(/AItable.ai/gi, 'Bika.ai'),
          description: item.description.replace(/AItable.ai/gi, 'Bika.ai'),
        },
        content: html.replace(/AItable.ai/gi, 'Bika.ai'),
      };
    }
    throw new Error('Not Found');
  }
}
