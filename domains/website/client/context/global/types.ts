// import { APICaller, TRPCOriginClient } from '@bika/api-caller/context';
// import type { LocaleContextProps } from '@bika/contents/i18n/context';
// import type { ThemeContextProps, AuthContextProps } from '@bika/types/website/bo';

export interface Entry {
  pathname: string;
}

// export interface GlobalContextState {
//   /** 一些全局的地址栏参数 */
//   track: (_trackLog: TrackLog) => void;
//   // trpc: TRPCOriginClient;
//   // apiCaller: APICaller;
//   /** disabled Config user select */
//   theme: ThemeContextProps;
//   // locale: LocaleContextProps;
//   timezone: string;
//   setTimeZone: (timeZone: string) => void;
//   auth: AuthContextProps;
//   isMobile: boolean;
//   appEnv: {
//     /** 访问了CN的 Host */
//     isFromCNHost: boolean;
//     /** cloudflare 的地区标识 */
//     isFromCN: boolean;
//     // 程序运行环境
//     readonly appEnv: AppEnv;
//   };

//   // 获取版本号
//   readonly version: string;

//   showUIModal: (_params: GlobalModalConfig | null) => void;
//   genUIModalQueryString(config: GlobalModalConfig | null): string | undefined;
// }
