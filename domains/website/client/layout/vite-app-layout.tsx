'use client';

import React from 'react';
import { APICallerProvider } from '@bika/api-caller/context';
import { LocaleProvider } from '@bika/contents/i18n/context';
import { type Dictionary } from '@bika/contents/i18n/translate';
import { GlobalProvider } from '@bika/domains/website/client/context/global/provider';
import type { Locale } from '@bika/types/i18n/bo';
import { getAppEnv } from 'sharelib/app-env';
import { ViteUIFrameworkProvider } from '@bika/ui/framework/vite-framework-provider';
import '@bika/domains/shared/client/styles/globals.css';
import '@bika/domains/shared/client/styles/markdown.css';

/**
 * Vite SPA单页应用主layout
 *
 * @param param0
 * @returns
 */
export function ViteAppLayout({ dictionary, children }: { dictionary: Dictionary; children: React.ReactNode }) {
  const locale = 'zh-CN';
  return (
    <ViteUIFrameworkProvider>
      <LocaleProvider defaultLocale={locale as Locale} defaultDictionary={dictionary}>
        <APICallerProvider>
          <GlobalProvider
            initData={{
              hostname: '',
              servers: {
                docServerUrl: '',
                storagePublicUrl: '',
                formAppAIBaseUrl: '',
              },
              auth: null,
              locale,
              timeZone: undefined,
              themeMode: 'system',
              themeStyle: 'default',
              isFromCNHost: false,
              isFromCN: false,
              headers: null!,
              // 这些ENV是前端需要使用的，所以需要传递给前端
              // 在客户端组件中是不可以直接使用process的
              appEnv: getAppEnv(),
              version: 'vite',
              env: {
                // FIREBASE_API_KEY: process.env.FIREBASE_API_KEY,
                // FIREBASE_AUTH_DOMAIN: process.env.FIREBASE_AUTH_DOMAIN,
                // FIREBASE_PROJECT_ID: process.env.FIREBASE_PROJECT_ID,
                // FIREBASE_STORAGE_BUCKET: process.env.FIREBASE_STORAGE_BUCKET,
                // FIREBASE_MESSAGING_SENDER_ID: process.env.FIREBASE_MESSAGING_SENDER_ID,
                // FIREBASE_APP_ID: process.env.FIREBASE_APP_ID,
                // FIREBASE_PUBLIC_VAPID_KEY: process.env.FIREBASE_PUBLIC_VAPID_KEY,
              },
            }}
          >
            {/* TODO，如果传入Space ID，那么这里还有SpaceProvider */}
            {children}
          </GlobalProvider>
        </APICallerProvider>
      </LocaleProvider>
    </ViteUIFrameworkProvider>
  );
}
