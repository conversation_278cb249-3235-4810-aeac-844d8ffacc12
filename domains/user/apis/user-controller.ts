import { isEmail } from 'basenext/utils/string';
import { generateRandomString } from '@bika/domains/shared/server';
import { RemoteStorageHelper } from '@bika/domains/system/server/remote-storage/remote-storage-helper';
import { RemoteStorageSO } from '@bika/domains/system/server/remote-storage/remote-storage-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { TRPCError } from '@bika/server-orm/trpc';
import { RemoteStorageWeixinQrCodeTicketUserId } from '@bika/types/system/remote-storage';
import type { UserBO } from '@bika/types/user/bo';
import { ApiFetchRequestContext } from '@bika/types/user/vo';

type IBindingResult = boolean | { error: string; quickCode: string };

export class UserController {
  static async getMeSO(ctx: ApiFetchRequestContext): Promise<UserSO> {
    const user = await UserSO.init(ctx.session!.userId);
    return user;
  }

  static async bindWeixin(userId: string, ticket: string): Promise<IBindingResult> {
    const property = (await RemoteStorageSO.getProperty(
      'WEIXIN_QR_CODE_TICKET_USER_ID',
      ticket,
    )) as RemoteStorageWeixinQrCodeTicketUserId;
    if (!property) {
      return false;
    }
    if (userId !== property.userId) {
      throw new Error('Ticket not match');
    }
    return true;
  }

  static async bindEmail(userId: string, email: string, verificationCode: string): Promise<IBindingResult> {
    const user = await UserSO.init(userId);
    if (user.model.email && user.model.email === email) {
      return true;
    }
    if (!isEmail(email)) {
      throw new TRPCError({ code: 'BAD_REQUEST', message: 'Invalid email' });
    }
    // match verification code
    const type = 'MAIL_VERIFICATION_CODE';
    await RemoteStorageSO.matchVerificationCode(type, email, verificationCode);
    // check if email has been used
    const userSO = await UserSO.findByEmail(email);
    if (userSO) {
      await RemoteStorageSO.delete(type, email);
      return this.createVerificationCode(userSO.id);
    }
    const userBo: UserBO = { email };
    if (!user.name) {
      userBo.name = email.split('@')[0];
    }
    await user.updateUserInfo(userBo);
    await RemoteStorageSO.delete(type, email);
    return true;
  }

  static async bindPhone(userId: string, phone: string, verificationCode: string): Promise<IBindingResult> {
    const user = await UserSO.init(userId);
    if (user.model.phone && user.model.phone === phone) {
      return true;
    }
    // match verification code
    const type = 'SMS_VERIFICATION_CODE';
    await RemoteStorageSO.matchVerificationCode(type, phone, verificationCode);
    // check if phone has been used
    const userSO = await UserSO.findByPhone(phone);
    if (userSO) {
      await RemoteStorageSO.delete(type, phone);
      return this.createVerificationCode(userSO.id);
    }
    await user.updateUserInfo({ phone });
    await RemoteStorageSO.delete(type, phone);
    return true;
  }

  private static async createVerificationCode(userId: string) {
    const quickCode = generateRandomString(64);
    await RemoteStorageHelper.verificationCode.create('QUICK_LOGIN_VERIFICATION_CODE', userId, quickCode);
    return {
      error: 'ExternalAlreadyLinkedError',
      quickCode,
    };
  }
}
