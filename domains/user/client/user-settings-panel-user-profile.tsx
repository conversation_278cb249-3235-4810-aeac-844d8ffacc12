import assert from 'assert';
import _, { omit } from 'lodash';
import { useRouter } from 'next/navigation';
import type React from 'react';
import { useCallback, useMemo, useState } from 'react';
import { useTRPCQuery } from '@bika/api-caller/context';
import { getLocaleString } from '@bika/contents/config/client';
import { i18n } from '@bika/contents/i18n';
import { useLocale } from '@bika/contents/i18n/context';
import type { LocaleType } from '@bika/types/system';
import type { UpdateUserDTO } from '@bika/types/user/dto';
import { type ThemeStyle, ThemeModes, type ThemeMode } from '@bika/types/website/bo';
import { useGlobalContext } from '@bika/types/website/context';
import { Button } from '@bika/ui/button';
import { useAttachmentUpload } from '@bika/ui/components/image-crop-upload/index';
import { Box, Stack } from '@bika/ui/layouts';
import { AvatarLogoBOInput } from '@bika/ui/shared/types-form/avatar-logo-bo-input';
import { ColorInput } from '@bika/ui/shared/types-form/color-input';
import { SelectInput } from '@bika/ui/shared/types-form/select-input';
import { StringInput } from '@bika/ui/shared/types-form/string-input';
import { TimeZoneSelect } from '@bika/ui/shared/types-form/timezone-select';
import { useSnackBar } from '@bika/ui/snackbar';
import { Typography } from '@bika/ui/texts';
import { FooterButtons, NavHeader } from '@bika/ui/web-layout';
import { ReferralCode } from './referral-code';

interface IUserSettingsTabsProps {
  onClose: () => void;
}

export function UserSettingsTabs({ onClose }: IUserSettingsTabsProps) {
  const router = useRouter();
  const context = useGlobalContext();
  const trpcQuery = useTRPCQuery();
  const locale = useLocale();
  const { upload: doUploadFile, unsplashDownload } = useAttachmentUpload();
  const { t, lang: curLang } = useLocale();
  const isProduction = context.appEnv === 'PRODUCTION';
  const { authContext: auth } = context;
  const mutateUserInfo = trpcQuery.user.updateUserInfo.useMutation();
  const mutateUserSetting = trpcQuery.user.updateUserSettings.useMutation();
  const writeReferralCode = trpcQuery.user.writeReferralCode.useMutation();
  const [referralCode, setReferralCode] = useState<string | undefined>(undefined);

  const [value, setValue] = useState<UpdateUserDTO>({
    avatar: auth.me?.user.avatar,
    email: auth.me?.user.email,
    name: auth.me?.user.name,
    themeMode: context.theme.themeMode,
    themeStyle: auth.me?.user.settings?.themeStyle,
    customColors: auth.me?.user.settings?.customColors,
    lang: curLang,
    phone: auth.me?.user.phone ?? undefined,
    timeZone: auth.me?.user.timeZone,
  });

  const [colorType, setColorType] = useState<'CUSTOM' | 'DEFAULT'>(() => {
    if (typeof auth.me?.user.settings?.customColors === 'object') {
      return 'CUSTOM';
    }
    return 'DEFAULT';
  });

  // 获取主题的文字
  const getThemeLabel = useCallback(
    (theme: ThemeMode) => {
      switch (theme) {
        case 'system':
          return t.theme.system;
        case 'light':
          return t.theme.light;
        case 'dark':
          return t.theme.dark;
        default:
          throw new Error(`Unknown theme: ${theme}`);
      }
    },
    [t.theme],
  );

  const themeLists = useMemo(
    () =>
      ThemeModes.map((theme) => ({
        label: getThemeLabel(theme),
        value: theme,
      })),
    [getThemeLabel],
  );

  const { toast } = useSnackBar();
  const langsList = useMemo(
    () =>
      i18n.locales.map((lang) => ({
        label: getLocaleString(curLang, lang),
        value: lang,
      })),
    [curLang],
  );

  const writeCode = async (code?: string) => {
    if (!code) {
      return;
    }
    await writeReferralCode.mutate(
      {
        referralCode: code,
      },
      {
        onSuccess: () => {
          auth.refetchMe();
        },
      },
    );
  };

  const updateUserSettings = async (data: UpdateUserDTO) => {
    if (!data) {
      return;
    }
    await mutateUserSetting.mutate(
      {
        themeMode: data.themeMode,
        themeStyle: data.themeStyle,
        locale: data.lang,
      },
      {
        onSuccess: () => {
          context.theme.setTheme({ mode: data.themeMode || 'dark', style: data.themeStyle || 'default' });
          locale.setLang(data.lang as LocaleType);
        },
      },
    );
  };

  const onSubmit = async (data: UpdateUserDTO) => {
    if (!data) {
      return;
    }

    if (!data.name || /^\s+$/.test(data.name)) {
      toast(t.settings.nickname_modified_failed, {
        variant: 'error',
      });
      return;
    }

    const _data = omit(data, ['theme']);
    await mutateUserInfo.mutate(_data, {
      onSuccess: () => {
        updateUserSettings(_data);
        writeCode(referralCode);
        router.back();
        toast(t.user.updated);
        setTimeout(() => {
          // temporary solution
          window.location.reload();
        }, 1000);
      },
    });
  };

  return (
    <Stack height={'100%'} direction={'column'} sx={{ px: 2 }}>
      <NavHeader onClose={onClose}>{t.user.profile}</NavHeader>
      <Box flex={1} overflow={'hidden auto'}>
        <div className={'mb-4'}>
          <AvatarLogoBOInput
            name={value.name || ''}
            value={value.avatar}
            onChange={(newAvatar) => {
              setValue((prev) => ({ ...prev, avatar: newAvatar }));
            }}
            locale={locale}
            upload={async (file: File) => doUploadFile({ file, filePrefix: 'avatar' })}
            unsplashDownload={unsplashDownload}
            changeCoverText={t.avatar.change_avatar}
            aiConfig={{
              type: 'user-avatar' as const,
              placeholder: 'Please input your requirement to generate your user avatar by AI',
              defaultPrompt: `Please help me generate a user avatar. My username is ${auth.me?.user.name || ''}`,
            }}
          />
        </div>
        <StringInput
          label={t.user.name}
          value={value.name}
          onChange={(_name) => {
            setValue((prev) => ({ ...prev, name: _name }));
          }}
        />
        <Typography mt={0.5} textColor="var(--text-secondary)">
          ID: {auth.me?.user.id}
        </Typography>
        <Box display={'flex'} alignItems="center" justifyContent="space-between">
          <StringInput
            disabled
            label={t.user.email}
            value={auth.me?.user.email}
            onChange={(_email) => {
              setValue((prev) => ({ ...prev, email: _email }));
            }}
          />
          <Button
            sx={{
              alignSelf: 'center',
              mt: 4,
              ml: 2,
            }}
            onClick={() => {
              context.showUIModal({ name: 'USER_EMAIL_SETTING' });
            }}
          >
            {t.user.update_email}
          </Button>
        </Box>

        {/* Theme */}
        <SelectInput<ThemeStyle>
          label={t.user.theme_style.label}
          options={_.compact([
            { label: t.user.theme_style.dracula, value: 'dracula' },
            // TODO: 隐藏 Solarized
            // context.appEnv === 'INTEGRATION' && { label: t.user.theme_style.solarized, value: 'solarized' },
            { label: t.user.theme_style.bika, value: 'default' },
          ])}
          value={value.themeStyle || 'default'}
          onChange={(themeStyle) => {
            setValue((prev) => ({ ...prev, themeStyle: themeStyle! }));
          }}
        />
        <SelectInput<ThemeMode>
          label={t.user.theme}
          options={themeLists}
          value={value.themeMode || null}
          onChange={(themeMode) => {
            if (!themeMode) {
              return;
            }
            setValue((prev) => ({ ...prev, themeMode }));
          }}
        />
        {/* Color Styles(beta) */}
        {!isProduction && (
          <>
            {/* 切换 Colors */}
            <SelectInput
              label={t.user.custom_colors.label}
              options={[
                { label: t.user.custom_colors.default, value: 'DEFAULT' },
                { label: t.user.custom_colors.custom, value: 'CUSTOM' },
              ]}
              value={colorType}
              onChange={(newStyle) => {
                setColorType(newStyle!);
                if (newStyle === 'DEFAULT') {
                  setValue((prev) => ({ ...prev, colors: undefined }));
                } else {
                  setValue((prev) => ({ ...prev, colors: {} }));
                }
              }}
            />

            {colorType === 'CUSTOM' && value.customColors && typeof value.customColors === 'object' && (
              <>
                {/* Begin Custom Colors */}
                <ColorInput
                  label={'Sidebar Background Color'}
                  value={value.customColors?.sidebarBackgroundColor || ''}
                  onChange={(newColor) => {
                    assert(typeof value.customColors === 'object');
                    const oldColors = value.customColors;
                    setValue((prev) => ({
                      ...prev,
                      colors: {
                        ...oldColors,
                        sidebarBackgroundColor: newColor,
                      },
                    }));
                  }}
                />
                <ColorInput
                  label={'Content Background Color'}
                  value={value.customColors?.contentBackgroundColor || ''}
                  onChange={(newColor) => {
                    assert(typeof value.customColors === 'object');
                    const oldColors = value.customColors;
                    setValue((prev) => ({
                      ...prev,
                      colors: {
                        ...oldColors,
                        contentBackgroundColor: newColor,
                      },
                    }));
                  }}
                />
              </>
            )}
          </>
        )}

        {/* Timezone */}
        <TimeZoneSelect
          value={value.timeZone || ''}
          onChange={(newValue) => {
            setValue((prev) => ({ ...prev, timeZone: newValue }));
          }}
          placeholder={t.editor.please_input_data}
          noAuto
          label={t.user.timezone}
        />
        {/* Languages */}
        <SelectInput
          label={t.user.language}
          options={langsList}
          value={value.lang || null}
          onChange={(lang) => {
            if (!lang) {
              return;
            }
            setValue((prev) => ({ ...prev, lang }));
          }}
        />

        <ReferralCode
          code={auth.me?.user.metadata?.referralCode}
          disabled={Boolean(auth.me?.user.metadata?.referralCode)}
          setReferralCode={setReferralCode}
        />
      </Box>
      <FooterButtons onConfirm={() => onSubmit(value)} onCancel={onClose} confirmLoading={mutateUserInfo.isLoading} />
    </Stack>
  );
}
