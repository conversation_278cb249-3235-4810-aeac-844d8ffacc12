import _ from 'lodash';
import { describe, expect, test } from 'vitest';
import { MockContext, waitForMatchToBeMet } from '@bika/domains/__tests__/mock';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { FolderSO } from '@bika/domains/node/server/folder-so';
import { StoreTemplateSO } from '@bika/domains/store/server/store-template-so';
import { generateNanoID } from 'basenext/utils/nano-id';
import { DatabaseViewMirrorMetadata, NodeResourceMirrorMetadata, ViewMirrorMetadata } from '@bika/types/node/bo';
import { ViewMirrorVO } from '@bika/types/node/vo';
import { CustomTemplate } from '@bika/types/template/bo';
import { MirrorSO } from '../server/mirror-so';

describe('MirrorSO tests', () => {
  test('create database_view mirror', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const node = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'dst',
    });
    const database = await node.toResourceSO<DatabaseSO>();
    const mirrorNode = await rootFolder.createChildSimple(user, {
      resourceType: 'MIRROR',
      name: 'mirror',
      mirrorType: 'DATABASE_VIEW',
      databaseId: database.id,
      viewId: database.viewModels[0].id,
    });
    const mirror = await mirrorNode.toResourceSO<MirrorSO>();
    expect(mirror.name).to.equal('mirror');
    expect(mirror.mirrorType).to.equal('DATABASE_VIEW');
    const metadata = mirror.metadata as DatabaseViewMirrorMetadata;
    expect(metadata.mirrorType).to.equal('DATABASE_VIEW');
    expect(metadata.viewId).to.equal(database.viewModels[0].id);
    expect(metadata.databaseId).to.equal(database.id);
  });

  test('create node_resource mirror', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const node = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'dst',
    });
    const mirrorNode = await rootFolder.createChildSimple(user, {
      resourceType: 'MIRROR',
      name: 'mirror',
      mirrorType: 'NODE_RESOURCE',
      resourceId: node.id,
    });
    const mirror = await mirrorNode.toResourceSO<MirrorSO>();
    expect(mirror.name).to.equal('mirror');
    expect(mirror.mirrorType).to.equal('NODE_RESOURCE');
    const metadata = mirror.metadata as NodeResourceMirrorMetadata;
    expect(metadata.mirrorType).to.equal('NODE_RESOURCE');
    expect(metadata.resourceId).to.equal(node.id);
  });

  test('create view mirror', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const node = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'dst',
    });
    const mirrorNode = await rootFolder.createChildSimple(user, {
      resourceType: 'MIRROR',
      name: 'mirror',
      mirrorType: 'VIEW',
      databaseId: node.id,
      view: {
        name: 'test',
        type: 'TABLE',
        databaseId: node.id,
      },
    });
    const mirror = await mirrorNode.toResourceSO<MirrorSO>();
    const vo = await mirror.toVO();
    const viewMirrorVO = vo as ViewMirrorVO;
    expect(viewMirrorVO.databaseId).to.equal(node.id);
    expect(viewMirrorVO.viewId).not.toBe(undefined);
    expect(mirror.name).to.equal('mirror');
    expect(mirror.mirrorType).to.equal('VIEW');
    const metadata = mirror.metadata as ViewMirrorMetadata;
    expect(metadata.mirrorType).to.equal('VIEW');
    expect(metadata.databaseId).to.equal(node.id);
    expect(metadata.view.id).to.equal(undefined);
  });
});

describe('Mirror template tests', () => {
  const mockTemplate = async (userId: string, spaceId: string): Promise<CustomTemplate> => {
    const template: CustomTemplate = {
      schemaVersion: 'v1',
      visibility: 'PUBLIC',
      category: 'official',
      cover: {
        type: 'COLOR',
        color: 'BLUE',
      },
      templateId: generateNanoID('tpl'),
      name: 'mirror-test',
      version: '1.0.0',
      resources: [
        {
          resourceType: 'DATABASE',
          templateId: 'database-test',
          databaseType: 'DATUM',
          name: 'test-database',
          fields: [
            {
              templateId: 'test-database-field',
              type: 'SINGLE_TEXT',
              name: 'test-field',
            },
          ],
          views: [
            {
              templateId: 'test-database-view',
              name: 'test-view',
              type: 'TABLE',
            },
          ],
        },
        {
          resourceType: 'MIRROR',
          mirrorType: 'DATABASE_VIEW',
          templateId: 'test-database-view-mirror',
          databaseTemplateId: 'database-test',
          viewTemplateId: 'test-database-view',
          name: 'mirror-test',
        },
      ],
    };
    await StoreTemplateSO.upsertPayload(userId, spaceId, template);
    return template;
  };

  const upgradeTemplate = async (
    userId: string,
    spaceId: string,
    template: CustomTemplate,
  ): Promise<CustomTemplate> => {
    _.set(template, 'version', '1.1.0');
    _.set(template, 'resources[0].views[1]', {
      templateId: 'test-database-view-upgrade',
      name: 'test-view',
      type: 'TABLE',
    });
    // change to a new view, and change mirror name
    _.set(template, 'resources[1]', {
      resourceType: 'MIRROR',
      mirrorType: 'DATABASE_VIEW',
      templateId: 'test-database-view-mirror',
      databaseTemplateId: 'database-test',
      viewTemplateId: 'test-database-view-upgrade',
      name: 'mirror-test-upgrade',
    });
    await StoreTemplateSO.upsertPayload(userId, spaceId, template);
    return template;
  };

  test('install mirror from template', async () => {
    const { user, space } = await MockContext.initUserContext();
    const template = await mockTemplate(user.id, space.id);
    const folder = await space.installTemplateById(user, template.templateId!);
    await waitForMatchToBeMet(
      async () => {
        const node = await FolderSO.init(folder.id);
        const children = await node.getAllChildren();
        return children.length === 2 && children[1].type === 'MIRROR';
      },
      2000,
      100,
    ).catch((error: Error) => {
      throw new Error(error.message);
    });
  });

  test('upgrade mirror template', async () => {
    const { user, space } = await MockContext.initUserContext();
    const template = await mockTemplate(user.id, space.id);
    const folder = await space.installTemplateById(user, template.templateId!);
    // upgrade template
    await upgradeTemplate(user.id, space.id, template);
    const upgradeFolder = await folder.upgradeTemplate(user);
    await waitForMatchToBeMet(
      async () => {
        const node = await FolderSO.init(upgradeFolder.id);
        const children = await node.getAllChildren();
        return children.length === 2 && children[1].type === 'MIRROR' && children[1].name === 'mirror-test-upgrade';
      },
      2000,
      100,
    ).catch((error: Error) => {
      throw new Error(error.message);
    });
  });
});
