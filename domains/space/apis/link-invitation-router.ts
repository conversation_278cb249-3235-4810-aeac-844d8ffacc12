import { UserSO } from '@bika/domains/user/server/user-so';
import { protectedProcedure, publicProcedure, router } from '@bika/server-orm/trpc';
import {
  SpaceInvitationAcceptSchema,
  SpaceInvitationCreateSchema,
  SpaceInvitationDeleteSchema,
  SpaceInvitationInfoSchema,
  SpaceInvitationListSchema,
  SpaceInvitationRefreshSchema,
} from '@bika/types/space/dto';
import * as SpaceInvitationController from './invitation-controller';

/**
 * Space Link Invitation API
 */
export const linkInvitationRouter = router({
  /**
   * Get space link invitations
   */
  list: protectedProcedure.input(SpaceInvitationListSchema).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return SpaceInvitationController.getSpaceLinkInvitations(user, input);
  }),

  /**
   * Create space link invitation
   */
  create: protectedProcedure.input(SpaceInvitationCreateSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    return SpaceInvitationController.createSpaceLinkInvitation(ctx, input);
  }),

  /**
   * Refresh space link invitation
   */
  refresh: protectedProcedure.input(SpaceInvitationRefreshSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    return SpaceInvitationController.refreshSpaceLinkInvitation(ctx, input);
  }),

  /**
   * Delete space link invitation
   */
  delete: protectedProcedure.input(SpaceInvitationDeleteSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    return SpaceInvitationController.deleteSpaceLinkInvitation(ctx, input);
  }),

  /**
   * Get space link invitation detail
   */
  detail: publicProcedure.input(SpaceInvitationInfoSchema).query(async (opts) => {
    const { input } = opts;
    return SpaceInvitationController.getSpaceLinkInvitationDetail(input);
  }),

  /**
   * Accept invitation for user
   */
  accept: protectedProcedure.input(SpaceInvitationAcceptSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    return SpaceInvitationController.acceptSpaceLinkInvitation(ctx, input);
  }),
});
