import { UserSO } from '@bika/domains/user/server/user-so';
import { protectedProcedure, publicProcedure, router } from '@bika/server-orm/trpc';
import {
  EmailInvitationAcceptSchema,
  EmailInvitationInfoSchema,
  EmailInvitationSchema,
  EmailInviteListSchema,
} from '@bika/types/space/dto';
import * as SpaceInvitationController from './invitation-controller';

export const emailInvitationRouter = router({
  list: protectedProcedure.input(EmailInviteListSchema).query(async (opts) => {
    const { ctx, input } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return SpaceInvitationController.listEmailInvitations(user, input);
  }),
  /**
   * get email invitation info
   */
  info: publicProcedure.input(EmailInvitationInfoSchema).query(async (opts) => {
    const { input } = opts;
    return SpaceInvitationController.getEmailInvitationInfo(input);
  }),
  /**
   * resend invitation
   */
  resend: protectedProcedure.input(EmailInvitationInfoSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    return SpaceInvitationController.resendEmailInvite(ctx, input);
  }),
  /**
   * invite with emails
   */
  invite: protectedProcedure.input(EmailInvitationSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    return SpaceInvitationController.emailInvite(ctx, input);
  }),
  /**
   * delete email invitation
   */
  delete: protectedProcedure.input(EmailInvitationInfoSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    return SpaceInvitationController.deleteEmailInvitation(ctx, input);
  }),
  /**
   * accept invitation
   */
  accept: protectedProcedure.input(EmailInvitationAcceptSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    return SpaceInvitationController.acceptEmailInvitation(ctx, input);
  }),
  /**
   * reject invitation
   */
  reject: protectedProcedure.input(EmailInvitationAcceptSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return SpaceInvitationController.rejectEmailInvitation(user, input);
  }),
});
