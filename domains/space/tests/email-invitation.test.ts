import { describe, expect, test, vi } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock';
import { db, EmailInvitationModel } from '@bika/server-orm';
import { generateNanoID } from 'basenext/utils/nano-id';
import { EmailInvitationSO } from '../server/invitation/email-invitation-so';

describe('email invitation test', () => {
  test('invite single email as member', async () => {
    const { member, space } = await MockContext.initUserContext();

    const inviteEmail = '<EMAIL>';
    const inviteType = 'MEMBER';

    const model: EmailInvitationModel = {
      id: generateNanoID('ei'),
      spaceId: member.spaceId,
      email: inviteEmail,
      type: inviteType,
      status: 'PENDING',
      createdBy: member.userId,
      updatedBy: member.userId,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const invitations = [new EmailInvitationSO(model)];

    const spy = vi.spyOn(member, 'invite').mockImplementation(async () => {
      await db.mongo.emailInvitation.create(model);
      return invitations;
    });

    await member.invite([{ email: inviteEmail, type: inviteType }]);

    expect(spy).toHaveBeenCalled();

    const { list: emailInvitations } = await space.getEmailInvitations();
    expect(emailInvitations).toHaveLength(1);

    const emailInvitation = emailInvitations[0];
    expect(emailInvitation.model.email).toBe(inviteEmail);
    expect(emailInvitation.model.type).toBe(inviteType);
    expect(emailInvitation.model.status).toBe('PENDING');

    // accept invitation

    vi.restoreAllMocks();
  });
});
