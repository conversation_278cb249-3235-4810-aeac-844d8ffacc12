'use client';

import { useTheme } from '@mui/material/styles';
import type { Route } from 'next';
import React from 'react';
import { Helmet } from 'react-helmet-async';
import { useTRPCQuery } from '@bika/api-caller/context';
import { useLocale } from '@bika/contents/i18n/context';
import { NodeDetailVOProviderWithApi } from '@bika/domains/node/client/context/node-detail-vo-provider-with-api';
import { NodeDetailVORenderer } from '@bika/domains/node/client/node-detail-vo-renderer';
import { SpaceError } from '@bika/domains/space/client/error';
import { useRecently } from '@bika/domains/space/client/hooks/use-recently';
import { updateItemOnRootNodeTree } from '@bika/domains/space/client/sidebar/utils/match-template-util';
import { type DatabaseVO, CONST_RESOURCE_IDS } from '@bika/types/database/vo';
import type { NodeTreeVO, NodeDetailVO, MirrorVO } from '@bika/types/node/vo';
import { useSpace<PERSON><PERSON><PERSON>, useShareContext, useSpaceContextForce } from '@bika/types/space/context';
import { Button } from '@bika/ui/button';
import { Empty } from '@bika/ui/components/empty/index';
import { Box, Stack } from '@bika/ui/layouts';
import { Skeleton } from '@bika/ui/skeleton';

export default function NodeRedirect(props: { spaceId: string; nodeId: string }) {
  const params = props;
  const router = useSpaceRouter();
  const spaceContext = useSpaceContextForce();
  const { update: updateRecently } = useRecently();
  // const spaceId = spaceContext.data?.id;
  const { sharing } = useShareContext();
  const locale = useLocale();
  const { t } = locale;
  const trpcQuery = useTRPCQuery();
  const theme = useTheme();

  const [nodeDetail, setNodeDetail] = React.useState<NodeDetailVO | null>(null);

  const { refetch, isLoading, isError, error } = trpcQuery.node.talk.useQuery(
    {
      id: params.nodeId ?? '',
    },
    {
      // staleTime: consts.CONST_QUERY_STALE_TIME,
      retry: false,
      onSuccess: (data) => {
        const tData: NodeDetailVO = data as NodeDetailVO;

        setNodeDetail(tData);
        // recently 是纯客户端的
        updateRecently(tData);

        // TIPS: 移到服务端 trpc.node.talk 了，服务端来激活，手机端不用重复写
        // if (!sharing) {
        //   try {
        //     // 远程激活
        //     trpc.remoteStorage.setActiveSpaceNode.mutate({
        //       spaceId: params.spaceId,
        //       nodeId: params.nodeId,
        //     });
        //   } catch (e) {
        //     // 失败了不影响
        //     console.error(e);
        //   }
        // }
        // 超过 3 层的节点，需要更新树
        const rootTree = spaceContext.useRootNode(data.scope);
        if (rootTree.rootNode && rootTree.rootNode.id !== 'root') {
          const findNode = rootTree.findNode(data.id);
          if (!findNode) {
            rootTree.setSectionNodeTree(data.id);
          } else if (data.type === 'FOLDER' || data.type === 'TEMPLATE') {
            rootTree.setRootNode(updateItemOnRootNodeTree(data.resource as NodeTreeVO, rootTree.rootNode));
          }
        }

        if (data && data.type === 'DATABASE') {
          const { views } = data.resource as DatabaseVO;
          const viewId = views?.[0]?.id;
          router.push(`/space/${params.spaceId}/node/${data.id}/${viewId || null}` as Route);
        }
        // if (data && data.type === 'VIEW') {
        //   const { views } = data.resource as DatabaseVO;
        //   const viewId = views?.[0]?.id;
        //   router.push(`/space/${params.spaceId}/node/view/${viewId}` as Route);
        // }
        if (data && data.type === 'MIRROR') {
          const { viewId } = data.resource as MirrorVO;
          router.push(`/space/${params.spaceId}/node/${data.id}/${viewId || null}` as Route);
        }
      },
      onError: () => {
        // preventAccessToNode();
      },
    },
  );

  const currentNode = React.useMemo(() => {
    const rootTree = spaceContext.useRootNode();
    const rootNode = rootTree.rootNode;
    if (!rootNode) {
      return null;
    }
    const findNode = rootTree.findNode(params.nodeId);
    return findNode;
  }, [params.nodeId, spaceContext]);

  // 复用 space context root node 缓存数据，同步更新
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  React.useEffect(() => {
    if (currentNode) {
      // console.log('🚀 ~ currentNode ~ currentNode:', currentNode);
      // console.log('🚀 ~ currentNode ~ nodeDetail:', nodeDetail);
      // setNodeDetail({
      //   ...nodeDetail,
      //   resource: {
      //     ...nodeDetail?.resource,
      //     ...currentNode,
      //   },
      // } as NodeDetailVO);
      // refetch();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentNode]);

  if (isError) {
    const prefix = CONST_RESOURCE_IDS.find((prefix) => params.nodeId.startsWith(prefix));
    if (prefix === 'dsb') {
      const src = theme.palette.mode.includes('dark')
        ? '/assets/placeholders/report-dark.png'
        : '/assets/placeholders/report-light.png';
      return (
        <Stack width={'100%'} height={'100%'} justifyContent={'center'} alignItems={'center'}>
          <Empty
            src={src}
            text={t.error.page_error}
            description={error?.message}
            button={
              <Button
                sx={{ bgcolor: 'var(--brand)', width: '200px', height: '40px', borderRadius: '4px' }}
                onClick={(e: React.MouseEvent) => {
                  e.preventDefault();
                  router.push(`/space/${params.spaceId}` as Route);
                }}
              >
                {t.error.back_to_home}
              </Button>
            }
          />
        </Stack>
      );
    }
    return <SpaceError errorType={'NoAccess'} />;
  }

  const _isLoading = sharing ? false : isLoading;
  return (
    <Box display="flex" className={'h-full'} flex={1} flexDirection={'column'} overflow={'hidden'}>
      {(_isLoading || !nodeDetail || !nodeDetail.id || nodeDetail.type === 'DATABASE') && (
        <Skeleton pos="NODE_PAGE" type={params.nodeId} />
      )}

      {nodeDetail?.id && nodeDetail.type !== 'DATABASE' && (
        <NodeDetailVOProviderWithApi value={nodeDetail}>
          <>
            <Helmet>
              <title>{nodeDetail.name}</title>
            </Helmet>
            <NodeDetailVORenderer
              params={{
                spaceId: params.spaceId,
                // viewId: params.viewId,
              }}
              locale={locale}
              value={nodeDetail}
              refetch={refetch}
            />
          </>
        </NodeDetailVOProviderWithApi>
      )}
    </Box>
  );
}
