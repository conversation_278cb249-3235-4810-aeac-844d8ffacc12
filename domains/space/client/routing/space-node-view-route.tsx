'use client';

import React from 'react';
import { Helmet } from 'react-helmet-async';

// import { useSpaceRouter } from '@bika/types/space/context';
import { useTRPCQuery } from '@bika/api-caller/context';
import { useLocale } from '@bika/contents/i18n/context';
import { NodeDetailVOProviderWithApi } from '@bika/domains/node/client/context/node-detail-vo-provider-with-api';
import { NodeDetailVORenderer } from '@bika/domains/node/client/node-detail-vo-renderer';
import { updateItemOnRootNodeTree } from '@bika/domains/space/client/sidebar/utils/match-template-util';
import { updateViewIdInPathname } from '@bika/domains/website/server/utils';
import type { DatabaseVO } from '@bika/types/database/vo';
import type { NodeDetailVO } from '@bika/types/node/vo';
// import { useUIFrameworkContext } from '@bika/ui/framework/context';
import type { NodeTreeVO } from '@bika/types/node/vo';
import { useSpaceContextForce, useSpaceRouter } from '@bika/types/space/context';
import { Skeleton } from '@bika/ui/skeleton';

export default function DatabaseViewLayoutPage(props: {
  nodeId: string;
  spaceId: string;
  viewId: string;
  recordId?: string;
  children?: React.ReactNode;
}) {
  const spaceContext = useSpaceContextForce();
  const router = useSpaceRouter();
  const params = props;

  const trpcQuery = useTRPCQuery();
  const locale = useLocale();

  const { data: nodeVO, refetch } = trpcQuery.node.detail.useQuery(
    {
      id: params.nodeId,
    },

    {
      // staleTime: consts.CONST_QUERY_STALE_TIME,
      onError: () => {
        // preventAccessToNode();
      },
      onSuccess: (data) => {
        // 超过 3 层的节点，需要更新树
        const rootTree = spaceContext.useRootNode(data.scope);
        if (rootTree.rootNode) {
          const findNode = rootTree.findNode(data.id);
          if (!findNode) {
            rootTree.setSectionNodeTree(data.id);
          } else if (data.type === 'FOLDER' || data.type === 'TEMPLATE') {
            rootTree.setRootNode(updateItemOnRootNodeTree(data.resource as NodeTreeVO, rootTree.rootNode));
          }
        }
        return data;
      },
    },
  );

  // const router = useSpaceRouter();

  // const frameworkContext = useUIFrameworkContext();
  // const router = useSpaceRouter();

  if (!nodeVO) {
    return <Skeleton pos="NODE_PAGE" />;
  }
  const tNodeVO: NodeDetailVO = nodeVO as NodeDetailVO;

  const { views = [] } = tNodeVO.resource as DatabaseVO;

  const isWrongViewIdDetected = params.viewId != null && views?.every((view) => view.id !== params.viewId);

  if (views.length && isWrongViewIdDetected) {
    const pathname = window.location.pathname;

    const url = new URL(window.location.href);
    const shouldSkipRedirect = Boolean(url.searchParams.get('skipRedirect'));

    if (!shouldSkipRedirect) {
      // router.push(updateViewIdInPathname(pathname, views?.[0].id) + window.location.search);
      router.push(updateViewIdInPathname(pathname, views?.[0].id) + window.location.search);
      return null;
    }
  }

  return (
    <>
      <NodeDetailVOProviderWithApi value={tNodeVO} viewId={isWrongViewIdDetected ? views?.[0]?.id : params.viewId}>
        <>
          <Helmet>
            <title>{tNodeVO.name}</title>
          </Helmet>
          <NodeDetailVORenderer
            locale={locale}
            refetch={refetch}
            params={{
              spaceId: params.spaceId,
              viewId: params.viewId,
            }}
            value={tNodeVO}
          />
          {props.children}
        </>
      </NodeDetailVOProviderWithApi>
    </>
  );
}
