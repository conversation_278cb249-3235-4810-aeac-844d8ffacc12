import dayjs from 'dayjs';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import timezone from 'dayjs/plugin/timezone';
import { keyBy, compact } from 'lodash';
import { useMemo } from 'react';
import { useTRPCQuery } from '@bika/api-caller/context';
import { featuresGroupFormatterConfigs } from '@bika/contents/config/server/pricing/features-group';
import { useLocale } from '@bika/contents/i18n/context';
import { PureDashboardRenderer } from '@bika/domains/dashboard/client/pure-dashboard-renderer';
import { WidgetRenderVO } from '@bika/types/dashboard/vo';
import {
  SpacePlanType,
  BillingUsagePlanFeature,
  BillingUsagePlanFeatureSchema,
  BillingBooleanPlanFeatureSchema,
  BillingDatePlanFeatureSchema,
} from '@bika/types/pricing/bo';
import { UsageFeatureVO } from '@bika/types/pricing/vo';
import { useSpaceContextForce } from '@bika/types/space/context';
import { useGlobalContext } from '@bika/types/website/context';
import CheckOutlined from '@bika/ui/icons/components/check_outlined';
import CloseOutlined from '@bika/ui/icons/components/close_outlined';
import { Modal } from '@bika/ui/modal';
import { Skeleton } from '@bika/ui/skeleton';
import { useSnackBar } from '@bika/ui/snackbar';
import { NavHeader } from '@bika/ui/web-layout';
import { SpaceBillingStatusComponent, UpgradeButtonProps } from './space-billing-status-component';
import { useSubscribePlatform } from '../hooks/use-subscribe-platform';

dayjs.extend(localizedFormat);
dayjs.extend(timezone);

const subscribeProcessColor: Record<SpacePlanType, string> = {
  FREE: '--rainbow-brown2',
  STARTER: '--rainbow-indigo2',
  PLUS: '--rainbow-indigo2',
  PRO: '--rainbow-purple2',
  TEAM: '--rainbow-yellow1',
  BUSINESS: '--rainbow-indigo4',
  COMMUNITY: '--rainbow-brown2',
  ENTERPRISE: '--rainbow-indigo2',
  ENTERPRISE_PRIVATE_CLOUD: '--rainbow-purple2',
  ENTERPRISE_SELF_HOSTED: '--rainbow-yellow1',
};

export const subscribeButtonColor: Record<SpacePlanType, string> = {
  FREE: '--rainbow-brown5',
  STARTER: '--rainbow-indigo5',
  PLUS: '--rainbow-indigo5',
  PRO: '--rainbow-purple5',
  TEAM: '--rainbow-tangerine5',
  BUSINESS: '--rainbow-indigo5',
  COMMUNITY: '--rainbow-brown5',
  ENTERPRISE: '--rainbow-indigo5',
  ENTERPRISE_PRIVATE_CLOUD: '--rainbow-purple5',
  ENTERPRISE_SELF_HOSTED: '--rainbow-tangerine5',
};

// 新增升级按钮文字颜色映射
const upgradeButtonTextColor: Record<SpacePlanType, string> = {
  FREE: '--rainbow-brown5',
  STARTER: '--rainbow-indigo5',
  PLUS: '--rainbow-indigo5',
  PRO: '--rainbow-purple5',
  TEAM: '--rainbow-tangerine5',
  BUSINESS: '--rainbow-indigo5',
  COMMUNITY: '--rainbow-brown5',
  ENTERPRISE: '--rainbow-indigo5',
  ENTERPRISE_PRIVATE_CLOUD: '--rainbow-purple5',
  ENTERPRISE_SELF_HOSTED: '--rainbow-tangerine5',
};

export function SpaceSettingsPanelBilling() {
  const spaceContext = useSpaceContextForce();
  const { timezone: tz, appEnv } = useGlobalContext();
  const spaceId = spaceContext.data.id;
  const { toast } = useSnackBar();
  // 当前空间站的订阅信息
  const subscription = spaceContext.data?.subscription;
  const locale = useLocale();
  const { t } = useLocale();
  const trpcQuery = useTRPCQuery();
  // const { data: usages, refetch: refetchUsage, isLoading } = trpcQuery.billing.usage.useQuery({ spaceId });
  const { data: entitlements, isLoading } = trpcQuery.billing.entitlements.useQuery({ spaceId });
  const { mutate: cancelSubscription } = trpcQuery.billing.cancelSubscription.useMutation();
  const { mutate: getCustomerPortal, isLoading: loadingPortal } = trpcQuery.billing.customerPortal.useMutation();
  const { mutate: resumeSubscription } = trpcQuery.billing.resumeSubscription.useMutation();
  const featureGroup = useMemo(
    () =>
      featuresGroupFormatterConfigs(locale).reduce((acc, curr) => {
        const _acc = [...acc, ...curr.features];
        return _acc;
      }, [] as any),
    [locale],
  );

  const { isFromThirdPartyPlatform, platform } = useSubscribePlatform();

  // BillingUsageType => t.xxx 映射
  const billingUsageTypeMap: Partial<Record<BillingUsagePlanFeature, string>> = useMemo(
    () => ({
      GUESTS: t.settings.billing.usages.guests,
      SEATS: t.settings.billing.usages.seats,
      STORAGES: t.settings.billing.usages.storages,
      RESOURCES: t.settings.billing.usages.resources,
      MISSIONS: t.settings.billing.usages.missions,
      REPORTS: t.settings.billing.usages.reports,
      MANAGED_EMAILS: t.settings.billing.usages.emails,
      RECORDS_PER_SPACE: t.settings.billing.usages.records,
      AUTOMATION_RUNS: t.settings.billing.usages.automation_runs,
      SPACE_INTEGRATIONS: t.settings.billing.usages.space_integrations,
    }),
    [t],
  );

  const formatBytes = useMemo(
    () =>
      (bytes: number): string => {
        if (bytes === -1 || bytes === -2) {
          return t.pricing.features.unlimited;
        }
        if (bytes < 1024) {
          return `${bytes} B`;
        }
        const k = 1024;
        const dm = 2;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return `${parseFloat((bytes / k ** i).toFixed(dm))} ${sizes[i]}`;
      },
    [t],
  );

  const buttonGroup: UpgradeButtonProps[] = useMemo(() => {
    if (appEnv === 'SELF-HOSTED') return [];
    if (!subscription || subscription.plan === 'FREE') {
      return [
        {
          name: t.settings.space.upgrade,
          onClick: () => {
            spaceContext.showUIModal({ type: 'space-settings', tab: { type: 'SPACE_UPGRADE' } });
            localStorage.setItem('FROM_BILLING', 'true');
          },
          sx: {
            borderRadius: '50px',
            color: `var(${upgradeButtonTextColor[spaceContext.data?.subscription?.plan || 'FREE']}) !important`,
            // backgroundColor: `var(${subscribeButtonColor[spaceContext.data?.subscription?.plan || 'FREE']}) !important`,
            backgroundColor: `var(--bgStaticLightDefault) !important`,
            '&:hover': {
              backgroundColor: `var(--bgStaticLightHover) !important`,
            },
            '&:active': {
              backgroundColor: `var(--bgStaticLightActive) !important`,
            },
          },
        },
      ];
    }

    if (isFromThirdPartyPlatform) {
      return [
        {
          name: t.settings.billing.change_plan,
          sx: {
            borderRadius: '50px',
            color: `var(${upgradeButtonTextColor[spaceContext.data?.subscription?.plan || 'FREE']}) !important`,
            // backgroundColor: `var(${subscribeButtonColor[spaceContext.data?.subscription?.plan || 'FREE']}) !important`,
            backgroundColor: `var(--bgStaticLightDefault) !important`,
            '&:hover': {
              backgroundColor: `var(--bgStaticLightHover) !important`,
            },
            '&:active': {
              backgroundColor: `var(--bgStaticLightActive) !important`,
            },
          },
          onClick: () => {
            if (platform === 'APPSUMO') {
              window.open('https://appsumo.com/account/products/', '_blank');
            }
          },
        },
      ];
    }

    if (subscription.interval === 'once') {
      // Bika官方兑换码换取的一次性付款的订阅，没有续费按钮
      return [];
    }

    return [
      {
        name: t.settings.billing.change_payment_method,
        sx: {
          borderRadius: '50px',
        },
        color: 'neutral',
        variant: 'outlined',
        loading: loadingPortal,
        onClick: () => {
          // 这里应该是跳转到 Stripe 的页面
          getCustomerPortal(
            { spaceId },
            {
              onSuccess: (url) => {
                window.open(url, '_blank');
              },
            },
          );
        },
      },
      {
        name: t.settings.billing.change_plan,
        sx: {
          borderRadius: '50px',
          color: `var(${upgradeButtonTextColor[spaceContext.data?.subscription?.plan || 'FREE']}) !important`,
          // backgroundColor: `var(${subscribeButtonColor[spaceContext.data?.subscription?.plan || 'FREE']}) !important`,
          backgroundColor: `var(--bgStaticLightDefault) !important`,
          '&:hover': {
            backgroundColor: `var(--bgStaticLightHover) !important`,
          },
          '&:active': {
            backgroundColor: `var(--bgStaticLightActive) !important`,
          },
        },
        onClick: () => {
          spaceContext.showUIModal({ type: 'space-settings', tab: { type: 'SPACE_UPGRADE' } });
          localStorage.setItem('FROM_BILLING', 'true');
        },
      },
    ];
  }, []);

  const widgets: WidgetRenderVO[] = useMemo(() => {
    const retWidgets: WidgetRenderVO[] = [];
    if (entitlements) {
      // console.log('🚀 ~ constwidgets:WidgetVO[]=useMemo ~ entitlements:', entitlements);
      for (const entitlement of entitlements) {
        if (BillingUsagePlanFeatureSchema.safeParse(entitlement.feature).success) {
          const usageFeature = entitlement as UsageFeatureVO;
          retWidgets.push({
            id: usageFeature.feature,
            templateId: usageFeature.feature,
            width: '50%',
            type: 'PROGRESS_BAR',
            name: billingUsageTypeMap[usageFeature.feature] || usageFeature.feature,
            title: usageFeature.feature,
            total: usageFeature.max,
            used: usageFeature.current,
            usedLabel: t.settings.billing.usages.used,
            unusedLabel: t.settings.billing.usages.unused,
            color: subscribeProcessColor[spaceContext.data?.subscription?.plan || 'FREE'],
            // trpc transformer will remove this 2 fields.
            formatter: usageFeature.feature === 'STORAGES' ? formatBytes : undefined,
            buttons: compact([
              !isFromThirdPartyPlatform && {
                name: t.settings.space.upgrade,
                onClick: () => {
                  if (platform === 'APPSUMO') {
                    window.open('https://appsumo.com/account/products/', '_blank');
                    return;
                  }
                  spaceContext.showUIModal({ type: 'space-settings', tab: { type: 'SPACE_UPGRADE' } });
                  localStorage.setItem('FROM_BILLING', 'true');
                },
              },
            ]),
          });
        }
        if (
          BillingBooleanPlanFeatureSchema.safeParse(entitlement.feature).success ||
          BillingDatePlanFeatureSchema.safeParse(entitlement.feature).success
        ) {
          const featureMap = keyBy(featureGroup, 'key');
          const usageFeature = entitlement as UsageFeatureVO;
          retWidgets.push({
            type: 'SUBSCRIBE_INFO',
            id: usageFeature.feature,
            templateId: usageFeature.feature,
            width: '50%',
            title: usageFeature.feature,
            name: featureMap[usageFeature.feature].name,
            tips: featureMap[usageFeature.feature].tips,
            content: () => (
              <div className="text-b3 text-color h-[54px] flex items-start">
                {/* eslint-disable-next-line no-nested-ternary */}
                {'value' in usageFeature ? (
                  featureMap[usageFeature.feature].customFormat(usageFeature.value)
                ) : 'active' in usageFeature && usageFeature.active === true ? (
                  <>
                    <span className="mr-2 pt-[2px]">
                      <CheckOutlined
                        color={`var(${subscribeProcessColor[spaceContext.data?.subscription?.plan || 'FREE']})`}
                      />
                    </span>
                    {t.settings.billing.already_support}
                  </>
                ) : (
                  <>
                    <span className="mr-2 pt-[2px]">
                      <CloseOutlined
                        color={`var(${subscribeProcessColor[spaceContext.data?.subscription?.plan || 'FREE']})`}
                      />
                    </span>
                    {t.settings.billing.not_support}
                  </>
                )}
              </div>
            ),
            buttons: compact([
              !isFromThirdPartyPlatform &&
                appEnv !== 'SELF-HOSTED' && {
                  name: t.settings.space.upgrade,
                  onClick: () => {
                    if (platform === 'APPSUMO') {
                      window.open('https://appsumo.com/account/products/', '_blank');
                      return;
                    }
                    spaceContext.showUIModal({ type: 'space-settings', tab: { type: 'SPACE_UPGRADE' } });
                    localStorage.setItem('FROM_BILLING', 'true');
                  },
                },
            ]),
          });
        }
      }
    }
    return retWidgets;
  }, [
    entitlements,
    billingUsageTypeMap,
    t,
    appEnv,
    featureGroup,
    spaceContext,
    platform,
    formatBytes,
    isFromThirdPartyPlatform,
  ]);

  if (isLoading) {
    return (
      <>
        <NavHeader onClose={() => spaceContext.showUIModal(null)}>{t.settings.space.usage}</NavHeader>
        <Skeleton pos="SPACE_SETTING_PANEL"></Skeleton>
      </>
    );
  }

  const onClickCancelSubscription = () => {
    Modal.show({
      type: 'warning',
      title: t.settings.billing.cancel_subscription_title,
      content: t.settings.billing.cancel_subscription_content,
      onOk: async () => {
        cancelSubscription(
          { spaceId },
          {
            onSuccess: () => {
              toast(t.settings.billing.cancel_subscription_tips, {
                variant: 'success',
              });
              setTimeout(() => {
                spaceContext.refetch();
              }, 3000);
            },
          },
        );
      },
    });
  };

  const formatDate = (isoDate?: string) => {
    if (!isoDate) {
      return t.settings.billing.no_billing_day;
    }
    return dayjs(isoDate).tz(tz).format('L LT');
  };

  const renderCancelBlock = () => {
    if (subscription && subscription.cancelAtPeriodEnd) {
      return (
        <div>
          {formatDate(subscription.expireAt)} {t.settings.billing.will_cancel_subscribe}{' '}
          <span
            className="underline decoration-solid underline-offset-[2px] cursor-pointer text-[--textWarnDefault]"
            onClick={() => {
              resumeSubscription(
                { spaceId },
                {
                  onSuccess: () => {
                    toast('Resume successfully', {
                      variant: 'success',
                    });
                    spaceContext.refetch();
                  },
                },
              );
            }}
          >
            {t.settings.billing.resume_subscription}
          </span>
        </div>
      );
    }
    return (
      <p className="text-b3 text-[--text-secondary]">
        {t.settings.billing.you_will_lose_all_privileges}{' '}
        <span
          className="underline decoration-solid underline-offset-[2px] cursor-pointer"
          onClick={onClickCancelSubscription}
        >
          {t.settings.billing.cancel_subscribe}
        </span>
      </p>
    );
  };

  return (
    <>
      <NavHeader onClose={() => spaceContext.showUIModal(null)}>{t.settings.space.usage}</NavHeader>
      <div className="px-[27px]">
        <SpaceBillingStatusComponent
          spaceId={spaceId}
          subscription={subscription}
          variant="space"
          onSubscriptionChange={() => {
            spaceContext.refetch();
          }}
          buttons={buttonGroup}
        />
        {subscription && subscription.plan !== 'FREE' && !isFromThirdPartyPlatform && appEnv !== 'SELF-HOSTED' && (
          <div className="mt-10">
            <div className="text-h7 text-[--text-secondary] mb-2">{t.settings.billing.cancel_subscribe}</div>
            {renderCancelBlock()}
          </div>
        )}
        <div className="mt-10">
          <div className="text-h7 text-[--text-secondary] mb-2">{t.settings.billing.usage_detail}</div>
          <PureDashboardRenderer locale={locale} widgets={widgets} />
        </div>
      </div>
    </>
  );
}
