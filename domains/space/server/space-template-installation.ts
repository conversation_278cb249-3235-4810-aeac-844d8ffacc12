import { generateNanoID } from 'basenext/utils/nano-id';
import { TemplateFolderSO } from '@bika/domains/node/server/folder-so';
import { NodeSO } from '@bika/domains/node/server/node-so';
import { TemplateComparator, TemplateRepoSO } from '@bika/domains/template/server';
import { db, PrismaPromise, TemplateApplyType, TemplateSourceType } from '@bika/server-orm';
import { CustomTemplate } from '@bika/types/template/bo';
import { TemplateApplyModel } from './types';

/**
 * 空间站模板安装记录对象 (SO)
 */
export class SpaceTemplateInstallation {
  private readonly _model: TemplateApplyModel;

  private readonly _currentTemplate: CustomTemplate;

  private constructor(model: TemplateApplyModel, currentTemplate: CustomTemplate) {
    this._model = model;
    this._currentTemplate = currentTemplate;
  }

  get id() {
    return this._model.id;
  }

  get referenceNodeId() {
    return this._model.nodeId;
  }

  get templateId() {
    return this._model.templateId;
  }

  get templateVersion() {
    return this._model.templateVersion;
  }

  /**
   * 安装时候对应的模板对象,可能非最新模板内容
   */
  get template(): CustomTemplate {
    return this._currentTemplate;
  }

  /**
   * this method will update the template
   */
  get upgradeTemplate(): CustomTemplate {
    return this._currentTemplate;
  }

  static async init(spaceId: string, templateNodeId: string): Promise<SpaceTemplateInstallation | null> {
    const templateApply = await this.queryOneBySpaceIdAndNodeId(spaceId, templateNodeId);
    if (!templateApply) {
      return null;
    }
    const templateSO = await TemplateRepoSO.init(templateApply.templateId);
    return new SpaceTemplateInstallation(templateApply, templateSO.currentTemplate);
  }

  static async initWithModel(model: TemplateApplyModel): Promise<SpaceTemplateInstallation> {
    const templateSO = await TemplateRepoSO.init(model.templateId);
    return new SpaceTemplateInstallation(model, templateSO.currentTemplate);
  }

  canUpgrade(): boolean {
    if (!this.templateVersion) {
      // 旧数据没有保存版本,直接升级
      return true;
    }
    const comparator = TemplateComparator.init(this.templateVersion);
    const latestVersion = this.template.version;
    return comparator.lessThan(latestVersion);
  }

  static queryBySpaceId(spaceId: string) {
    return db.prisma.templateApply.findMany({
      where: {
        spaceId,
      },
    });
  }

  static queryOneBySpaceIdAndNodeId(spaceId: string, nodeId: string): PrismaPromise<TemplateApplyModel | null> {
    return db.prisma.templateApply.findFirst({
      where: {
        spaceId,
        nodeId,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  static queryBySpaceIdAndTemplateId(spaceId: string, templateId: string): PrismaPromise<TemplateApplyModel[]> {
    return db.prisma.templateApply.findMany({
      where: {
        spaceId,
        templateId,
      },
    });
  }

  static create(
    userId: string,
    spaceId: string,
    referenceNodeId: string,
    template: CustomTemplate,
  ): PrismaPromise<TemplateApplyModel> {
    return db.prisma.templateApply.create({
      data: {
        id: generateNanoID('tpa'),
        templateId: template.templateId,
        templateVersion: template.version,
        spaceId,
        nodeId: referenceNodeId,
        source: TemplateSourceType.LOCAL,
        type: TemplateApplyType.INSTALL,
        createdBy: userId,
        updatedBy: userId,
      },
    });
  }

  updateOperation(userId: string, template: CustomTemplate): PrismaPromise<TemplateApplyModel> {
    return db.prisma.templateApply.update({
      where: {
        id: this.id,
      },
      data: {
        templateVersion: template.version,
        updatedBy: userId,
      },
    });
  }

  /**
   * 获取安装后的模板的文件夹节点 get template node folder
   */
  async getTemplateFolder() {
    const templateInstallNode = await NodeSO.init(this.referenceNodeId);
    return TemplateFolderSO.initWithModel(templateInstallNode.model);
  }
}
