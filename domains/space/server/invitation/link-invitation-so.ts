import { generateNanoID } from 'basenext/utils/nano-id';
import { errors, ServerError } from '@bika/contents/config/server/error';
import { EventSO } from '@bika/domains/event/server/event/event-so';
import { MemberSO } from '@bika/domains/unit/server/member-so';
import { RoleSO } from '@bika/domains/unit/server/role-so';
import { TeamSO } from '@bika/domains/unit/server/team-so';
import { Prisma, SpaceLinkInvitation, db } from '@bika/server-orm';
import { InvitationType, InvitationTypeSchema } from '@bika/types/space/bo';
import { InvitationRenderOpts, SpaceLinkInvitationVO } from '@bika/types/space/vo';
import { RoleVO } from '@bika/types/unit/vo';
import { SpaceSO } from '../space-so';

/**
 * 空间站(公开)邀请链接
 */
export class LinkInvitationSO {
  private _model: SpaceLinkInvitation;

  private _cacheMember: MemberSO | null = null;

  private _cacheTeam: TeamSO | null = null;

  private constructor(model: SpaceLinkInvitation) {
    this._model = model;
  }

  get id() {
    return this._model.id;
  }

  get model() {
    return this._model;
  }

  get type(): InvitationType {
    return this._model.type ?? InvitationTypeSchema.enum.MEMBER;
  }

  get roleIds(): string[] | undefined {
    return this.model.roleIds?.split(',');
  }

  isGuestInvitation(): boolean {
    return this.type === InvitationTypeSchema.enum.GUEST;
  }

  async getSpace(): Promise<SpaceSO> {
    return SpaceSO.init(this.model.spaceId);
  }

  async getMember(): Promise<MemberSO> {
    if (!this._cacheMember) {
      this._cacheMember = await MemberSO.init(this.model.memberId);
    }
    return this._cacheMember;
  }

  async getTeam(): Promise<TeamSO> {
    if (!this._cacheTeam) {
      this._cacheTeam = await TeamSO.init(this.model.teamId);
    }
    return this._cacheTeam;
  }

  async toVO(opts?: InvitationRenderOpts): Promise<SpaceLinkInvitationVO> {
    const [memberSO, teamSO] = await Promise.all([this.getMember(), this.getTeam()]);
    const [memberVO, teamVO] = await Promise.all([memberSO.toVO(), teamSO.toVO()]);
    let roles: RoleVO[] = [];
    if (this.roleIds) {
      const roleSOs = await RoleSO.findByIds(teamSO.spaceId, this.roleIds);
      roles = await Promise.all(roleSOs.map((roleSO) => roleSO.toVO({ locale: opts?.locale })));
    }
    return {
      token: this.id,
      type: this.type,
      team: teamVO,
      member: memberVO,
      roles,
    };
  }

  static async init(id: string): Promise<LinkInvitationSO> {
    const invitation = await this.initMaybeNull(id);
    if (!invitation) {
      throw new ServerError(errors.space.invitation_link_not_found);
    }
    return invitation;
  }

  static async initMaybeNull(id: string): Promise<LinkInvitationSO | null> {
    const record = await db.prisma.spaceLinkInvitation.findUnique({
      where: {
        id,
      },
    });
    return record && new LinkInvitationSO(record);
  }

  static async findBySpaceId(spaceId: string): Promise<LinkInvitationSO[]> {
    const records = await db.prisma.spaceLinkInvitation.findMany({
      where: { spaceId },
      orderBy: [{ createdAt: Prisma.SortOrder.desc }],
    });
    return records.map((record: SpaceLinkInvitation) => new LinkInvitationSO(record));
  }

  static async getByMemberId(memberId: string): Promise<LinkInvitationSO[]> {
    const records = await db.prisma.spaceLinkInvitation.findMany({
      where: { memberId },
      orderBy: [{ createdAt: Prisma.SortOrder.desc }],
    });
    return records.map((record: SpaceLinkInvitation) => new LinkInvitationSO(record));
  }

  static async create(
    member: MemberSO,
    opts: {
      invitationType: InvitationType;
      teamId: string;
      roleIds?: string[];
    },
  ): Promise<LinkInvitationSO> {
    const { invitationType, teamId, roleIds = [] } = opts || {};
    const space = await member.getSpace();
    const team = await space.getTeam(teamId);
    if (invitationType === InvitationTypeSchema.enum.MEMBER && team.isGuest) {
      throw new Error('Cannot invite member to guest team');
    }
    if (invitationType === InvitationTypeSchema.enum.GUEST && !team.isGuest) {
      throw new Error('Cannot invite guest to member team');
    }
    const inviteToken = generateNanoID();
    let data: Prisma.SpaceLinkInvitationCreateInput = {
      id: inviteToken,
      spaceId: member.spaceId,
      type: invitationType,
      member: { connect: { id: member.id } },
      teamId: team.id,
      createdBy: member.userId,
      updatedBy: member.userId,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    if (roleIds.length > 0) {
      // 校验角色是否存在同一空间
      const roles = await space.getRoles(roleIds);
      data = { ...data, roleIds: roles.map((role) => role.id).join(',') };
    }

    const record = await db.prisma.spaceLinkInvitation.create({ data });

    EventSO.space.onSpaceInviteCreated(member, inviteToken, roleIds);

    return new LinkInvitationSO(record);
  }

  async refresh(userId: string): Promise<LinkInvitationSO> {
    // 校验操作者权限（本人
    if (userId !== this.model.createdBy) {
      throw new Error('Only creator can refresh invitation');
    }
    const newInviteToken = generateNanoID();
    const invitationPO = await db.prisma.spaceLinkInvitation.update({
      data: {
        id: newInviteToken,
        updatedBy: userId,
        updatedAt: new Date(),
      },
      where: {
        id: this.id,
      },
    });
    return new LinkInvitationSO(invitationPO);
  }

  async delete(userId: string) {
    // 校验操作者权限（本人
    if (userId !== this.model.createdBy) {
      throw new Error('Only creator can delete invitation');
    }
    await db.prisma.spaceLinkInvitation.delete({
      where: {
        id: this.id,
      },
    });
  }
}
