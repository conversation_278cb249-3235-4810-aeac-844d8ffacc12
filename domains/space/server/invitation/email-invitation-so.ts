import { generateNanoID } from 'basenext/utils/nano-id';
import dayjs from 'dayjs';
import { InviteEmail } from '@bika/contents/email/InviteEmail';
import { EmailSO } from '@bika/domains/email/server/email-so';
import { pathHelper } from '@bika/domains/shared/server';
import { MemberSO } from '@bika/domains/unit/server/member-so';
import { UnitFactory } from '@bika/domains/unit/server/unit-factory';
import { db, EmailInvitationModel, mongoose } from '@bika/server-orm';
import { Pagination, PaginationInfo, PaginationSchema } from '@bika/types/shared';
import { InvitationStatus, InvitationTypeSchema } from '@bika/types/space/bo';
import { EmailInvite } from '@bika/types/space/dto';
import { SpaceEmailInvitationVO } from '@bika/types/space/vo';
import { SpaceSO } from '../space-so';
import { EmailInvitationListProps } from '../types';

export class EmailInvitationSO {
  private readonly _model: EmailInvitationModel;

  constructor(model: EmailInvitationModel) {
    this._model = model;
  }

  static async init(id: string) {
    const model = await db.mongo.emailInvitation.findOne({
      id,
    });
    if (!model) {
      throw new Error('Invitation not found');
    }
    return new EmailInvitationSO(model);
  }

  get model() {
    return this._model;
  }

  isGuestInvitation(): boolean {
    return this.model.type === InvitationTypeSchema.enum.GUEST;
  }

  async getInviter(): Promise<MemberSO> {
    const member = await UnitFactory.findMember(this.model.createdBy, this.model.spaceId);
    if (!member) {
      throw new Error('Inviter not found');
    }
    return member;
  }

  async getSpace(): Promise<SpaceSO> {
    return SpaceSO.init(this.model.spaceId);
  }

  public async update(params: { status: InvitationStatus; updatedBy: string }) {
    const { status, updatedBy } = params;
    await db.mongo.emailInvitation.updateOne({ id: this._model.id }, { status, updatedBy, updatedAt: new Date() });
  }

  async resend() {
    if (this.model.status !== 'PENDING') {
      throw new Error(`Can't Resend Invitation cause status is ${this.model.status}`);
    }
    const inviter = await this.getInviter();
    const space = await inviter.getSpace();
    const user = await inviter.getUser();
    const emailTemplate = new InviteEmail({
      locale: user.locale,
      inviter: user.name,
      inviterEmail: user.email,
      space: await space.toVO({ locale: user.locale }),
      url: pathHelper.getInviteUrl(this.model.id),
    });
    EmailSO.send({
      type: 'SYSTEM',
      to: this.model.email,
      subject: emailTemplate.subject(),
      react: emailTemplate.render(),
    });
  }

  async delete() {
    if (this.model.status !== 'PENDING') {
      throw new Error(`Can't Delete this invitation cause status is ${this.model.status}`);
    }
    await db.mongo.emailInvitation.deleteOne({ id: this._model.id });
  }

  async toVO(): Promise<SpaceEmailInvitationVO> {
    const inviter = await this.getInviter();
    const space = await inviter.getSpace();
    return {
      id: this._model.id,
      email: this._model.email,
      type: this._model.type,
      memberName: this._model.memberName ?? undefined,
      inviter: await inviter.toVO(),
      space: await space.toVO({ locale: inviter.locale }),
      status: this._model.status,
      createdAt: this._model.createdAt.toISOString(),
    };
  }

  static async create(member: MemberSO, invites: EmailInvite[]) {
    const space = await member.getSpace();
    // 如果邀请过的, 则不再邀请
    const invitedEmails = await db.mongo.emailInvitation.distinct('email', {
      spaceId: member.spaceId,
      email: { $in: invites.map((invite) => invite.email) },
    });
    const filterInvites = invites.filter((invite) => !invitedEmails.includes(invite.email));
    if (filterInvites.length === 0) {
      return [];
    }
    const models: EmailInvitationModel[] = filterInvites.map((invite) => ({
      id: generateNanoID('ei'),
      spaceId: member.spaceId,
      email: invite.email,
      memberName: invite.name,
      type: invite.type,
      status: 'PENDING',
      createdBy: member.userId,
      updatedBy: member.userId,
      createdAt: new Date(),
      updatedAt: new Date(),
    }));
    const inviteEmails: [string, InviteEmail][] = await Promise.all(
      models.map(async (model) => [
        model.email,
        new InviteEmail({
          locale: member.locale,
          inviter: member.getName(),
          inviterEmail: member.email,
          space: await space.toVO({ locale: member.locale }),
          url: pathHelper.getInviteUrl(model.id),
        }),
      ]),
    );
    await db.mongo.transaction(async (session) => {
      // 邀请记录
      await db.mongo.emailInvitation.insertMany(models, { session });
      // 发送邮件
      // TODO: 利用队列发送才避免第三方邮件服务触发限频
      await Promise.all(
        inviteEmails.map(([email, emailTemplate]) =>
          EmailSO.send({
            type: 'SYSTEM',
            to: email,
            subject: emailTemplate.subject(),
            react: emailTemplate.render(),
          }),
        ),
      );
    });

    return models.map((model) => new EmailInvitationSO(model));
  }

  static async list(
    q: EmailInvitationListProps,
    pagination?: Pagination,
  ): Promise<{ list: EmailInvitationSO[]; pagination: PaginationInfo }> {
    const { spaceId, email } = q;
    const { pageNo, pageSize } = PaginationSchema.parse(pagination ?? {});
    let filter: mongoose.FilterQuery<EmailInvitationModel> = { spaceId };
    if (email) {
      filter = { ...filter, email: { $regex: email, $options: 'i' } };
    }
    const [rows, total] = await Promise.all([
      db.mongo.emailInvitation
        .find(filter)
        .sort({ _id: -1 })
        .skip(pageNo > 0 ? (pageNo - 1) * pageSize : 0)
        .limit(pageSize),
      db.mongo.emailInvitation.countDocuments(filter),
    ]);
    return {
      list: rows.map((row) => new EmailInvitationSO(row)),
      pagination: { total, pageNo, pageSize },
    };
  }

  /**
   * Get the count of email invitations sent today for a specific space.
   */
  static async getTodayInvitationCount(spaceId: string, timeZone?: string): Promise<number> {
    const today = timeZone ? dayjs().tz(timeZone).startOf('day') : dayjs().startOf('day');
    const tomorrow = today.add(1, 'day').toDate();
    const count = await db.mongo.emailInvitation.countDocuments({
      spaceId,
      createdAt: {
        $gte: today.toDate(),
        $lt: tomorrow,
      },
    });
    return count;
  }
}
