import assert from 'assert';
import { generateNanoID } from 'basenext/utils/nano-id';
import dayjs from 'dayjs';
import { ignoreAICreditEnough, isInCI } from 'sharelib/app-env';
import { db, $Enums, CoinAccount as CoinAccountModel, Prisma } from '@bika/server-orm';
import { Pagination, PaginationSchema } from '@bika/types/shared/pagination';
import { CoinAccountStrategy, CoinTransactionReason } from '@bika/types/store/bo';
import { CreditTransactionPaginationVO, UserCoinTransactionVO } from '@bika/types/user/vo';
import { CoinTransactionSO } from './coin-transaction-so';
import { MemberSO } from '../../unit/server';

interface CoinsCost {
  virtual: bigint;
  credit: bigint;
  currency: bigint;
}
/**
 * Bika Coin
 */
export class CoinAccountSO {
  private _model: CoinAccountModel;

  // 外部传入的virtual 计算策略
  private _strategies: CoinAccountStrategy[];

  constructor(model: CoinAccountModel, strategies?: CoinAccountStrategy[]) {
    this._model = model;
    this._strategies = strategies || [];
  }

  get strategies() {
    return this._strategies;
  }

  async addStrategyUnique(strategy: CoinAccountStrategy) {
    // 确保 strategires，没有重复的 type
    const ss = this.strategies;
    const existingStrategyIndex = ss.findIndex((s) => s.type === strategy.type);
    if (existingStrategyIndex !== -1) {
      ss.splice(existingStrategyIndex, 1);
    }
    this._strategies.push(strategy);
  }

  private async saveStrategyUnique(strategy: CoinAccountStrategy) {
    const startDate = dayjs(strategy.startDate);
    const transactions = await CoinTransactionSO.findByCoinAccountId(this._model.id, {
      from: startDate.startOf('day').toDate(),
      to: startDate.endOf('day').toDate(),
      coinType: 'VIRTUAL',
      type: 'EARN',
      reason: {
        reason: 'strategy',
        strategyType: strategy.type,
        count: strategy.count,
      },
    });
    if (!transactions.length) {
      await this.addTransaction(BigInt(strategy.count), 'VIRTUAL', 'EARN', {
        reason: 'strategy',
        strategyType: strategy.type,
        count: strategy.count,
      });
    }
  }

  private async addStrategy(strategy: CoinAccountStrategy) {
    const ss = this.strategies;
    ss.push(strategy);
  }

  get model() {
    return this._model;
  }

  /**
   * Balance总余额 =Credit系统积分奖励 + Currency真金白银充值
   */
  get balance() {
    return this._model.balance;
  }

  get relationType(): $Enums.CoinAccountType {
    return this._model.relationType;
  }

  /**
   *  用于显示的余额 =  Virtual Credit 月度虚拟积分限制 + Balance(Credit + Currency)
   */
  async virtualBalance() {
    return this.balance + (await this.virtualCredit());
  }

  /**
   * 积分
   */
  get credit() {
    return this._model.credit;
  }

  /**
   * 虚拟积分，通常是一些"月度"派发的积分，月度增量
   */
  async virtualCredit() {
    let virtualCount = 0;

    let farestStartDate: dayjs.Dayjs | undefined;
    const now = dayjs();

    for (const strategy of this.strategies) {
      // 需要判定日期，是否在这个时间点内
      const startDate = dayjs(strategy.startDate);
      // 赋值最远
      if (!farestStartDate || (farestStartDate && startDate.isBefore(farestStartDate))) {
        farestStartDate = startDate;
      }

      if (strategy.type === 'monthly-credit') {
        // 判断 now 和 strategy.startDate，是否相差超过一个月(要少于一个月)
        const diff = now.diff(startDate, 'month');
        if (diff <= 1) {
          virtualCount += strategy.count;

          // 上面已知积分期内的积分，还要扣取积分期内发生的消费 virtual 积分
        }
      } else if (strategy.type === 'daily-credit') {
        // 判断 now 和 strategy.startDate，是否相差超过一天(要少于一天)
        const diff = now.diff(startDate, 'day');
        if (diff <= 1) {
          const todayUsed = await this.virtualTransactionAmount(
            startDate.startOf('day').toDate(),
            now.toDate(),
            'REDEEM',
          );
          if (todayUsed === 0) {
            virtualCount += strategy.count;
          }
        }
      } else if (strategy.type === 'yearly-credit') {
        // 判断 now 和 strategy.startDate，是否相差超过一年(要少于一年)
        const diff = now.diff(startDate, 'year');
        if (diff < 1) {
          virtualCount += strategy.count;
        }
      } else {
        // 其他类型不计算
        // valid = false;
      }
    }
    // 获取虚拟积分交易总额=非持久化赠送的积分 + 持久化的赠送积分 + 持久化的消耗积分(这里是负数)
    if (farestStartDate) {
      const virtualEarnAmount = await this.virtualTransactionAmount(
        farestStartDate.startOf('day').toDate(),
        now.toDate(),
        'EARN',
      );
      const virtualRedeemAmount = await this.virtualTransactionAmount(
        farestStartDate.startOf('day').toDate(),
        now.toDate(),
        'REDEEM',
      );
      virtualCount += virtualEarnAmount + virtualRedeemAmount;
    }
    return BigInt(virtualCount);
  }

  async virtualTransactionAmount(from: Date, to: Date, type?: $Enums.CoinTransactionType): Promise<number> {
    const amount = await CoinTransactionSO.getAmountByAccountId(this._model.id, {
      from,
      to,
      coinType: 'VIRTUAL',
      type,
    });
    return Number(amount);
  }

  /**
   * 真货币，通常由iOS充值
   */
  get currency() {
    return this._model.currency;
  }

  /**
   * 交易清算，确保余额准确
   */
  async clearing() {
    const balanceResult = await db.prisma.coinTransaction.aggregate({
      where: { accountId: this._model.id },
      _sum: { amount: true },
    });

    const realBalance = balanceResult._sum!.amount || BigInt(0);
    if (realBalance !== this._model.balance) {
      // eslint-disable-next-line no-console
      console.error(
        '清算发现Coin账号余额不一致 Balance not match, clearing',
        this._model.id,
        this._model.balance,
        realBalance,
      );
      await db.prisma.coinAccount.update({
        where: { id: this._model.id },
        data: { balance: realBalance },
      });
    }

    const creditResult = await db.prisma.coinTransaction.aggregate({
      where: {
        accountId: this._model.id,
        coinType: 'CREDIT',
      },
      _sum: { amount: true },
    });
    const realCredit = creditResult._sum!.amount || BigInt(0);
    if (realCredit !== this._model.credit) {
      // eslint-disable-next-line no-console
      console.error(
        '清算发现Coin账号Credit不一致 Credit not match, clearing',
        this._model.id,
        this._model.credit,
        realCredit,
      );
      await db.prisma.coinAccount.update({
        where: { id: this._model.id },
        data: { credit: realCredit },
      });
    }

    const currencyResult = await db.prisma.coinTransaction.aggregate({
      where: {
        accountId: this._model.id,
        coinType: 'CURRENCY',
      },
      _sum: { amount: true },
    });
    const realCurrency = currencyResult._sum!.amount || BigInt(0);
    if (realCurrency !== this._model.currency) {
      // eslint-disable-next-line no-console
      console.error(
        '清算发现Coin账号Currency不一致 Currency not match, clearing',
        this._model.id,
        this._model.currency,
        realCurrency,
      );
      await db.prisma.coinAccount.update({
        where: { id: this._model.id },
        data: { currency: realCurrency! },
      });
    }

    return { balance: realBalance, credit: realCredit, currency: realCurrency };
  }

  /**
   *  是否够钱？
   *
   * @param amount
   * @returns  返回 false 或分别扣哪些账户
   */
  public async enough(amount: bigint | number, forceCoinType?: $Enums.CoinType): Promise<boolean> {
    const virtual = await this.virtualCredit();
    const total = this.credit + virtual + this.currency;
    // 是否忽略 enough 检查，直接返回扣多少
    const ignoreEnough = ignoreAICreditEnough();

    if (!ignoreEnough && total < amount) {
      return false;
    }

    if (forceCoinType) {
      if (!ignoreEnough && forceCoinType === 'VIRTUAL') {
        if (virtual < BigInt(amount)) {
          return false;
        }
      }
      if (!ignoreEnough && forceCoinType === 'CREDIT') {
        if (this.credit < BigInt(amount)) {
          return false;
        }
      }
      if (!ignoreEnough && forceCoinType === 'CURRENCY') {
        if (this.currency < BigInt(amount)) {
          return false;
        }
      }
    }
    return true;
  }

  public async coinsCost(amount: bigint | number, coinType?: $Enums.CoinType) {
    if (coinType) {
      return {
        virtual: coinType === 'VIRTUAL' ? BigInt(amount) : BigInt(0),
        credit: coinType === 'CREDIT' ? BigInt(amount) : BigInt(0),
        currency: coinType === 'CURRENCY' ? BigInt(amount) : BigInt(0),
      };
    }
    const result: CoinsCost = {
      virtual: BigInt(0),
      credit: BigInt(0),
      currency: BigInt(0),
    };

    let remainingAmount = BigInt(amount);
    const virtual = await this.virtualCredit();
    // First try to use virtual credit
    if (virtual > 0) {
      const virtualToUse = virtual > remainingAmount ? remainingAmount : virtual;
      result.virtual = virtualToUse;
      remainingAmount -= virtualToUse;
    }

    // Then try to use credit if still needed
    if (remainingAmount > 0 && this.credit > 0) {
      const creditToUse = this.credit > remainingAmount ? remainingAmount : this.credit;
      result.credit = creditToUse;
      remainingAmount -= creditToUse;
    }

    // Finally use currency if still needed
    if (remainingAmount > 0 && this.currency > 0) {
      const currencyToUse = this.currency > remainingAmount ? remainingAmount : this.currency;
      result.currency = currencyToUse;
      remainingAmount -= currencyToUse;
    }
    // 上面的都扣完了, 都还有没有扣完的, 那么全部累积到virtual, 允许virtual 扣负数
    if (remainingAmount > 0) {
      result.virtual += remainingAmount;
    }

    return result;
  }

  public async redeem(
    amount: bigint | number,
    reason?: CoinTransactionReason,
    description?: string,
    //  如果不声明，则自动 判断消费账户， VIRTUAL > CREDIT > CURRENCY
    coinType?: $Enums.CoinType,
    createdBy?: string,
  ) {
    const coinsCost: CoinsCost = await this.coinsCost(amount, coinType);

    if (coinsCost.virtual > 0) {
      await this.addTransaction(-coinsCost.virtual, 'VIRTUAL', 'REDEEM', reason, description, createdBy);
      // 消费的时候记录一下今日赠送的credit
      const shouldStoreStrategies = this.strategies.filter((s) => s.type === 'daily-credit');
      if (shouldStoreStrategies.length) {
        const key = `lock:coin_account_strategy:${this._model.id}:${dayjs().format('YYYY-MM-DD')}`;
        // 由于使用了vi.useFakeTimers, redis的命令会卡住，所以ci中跳过这个检查
        const result = isInCI() ? 'OK' : await db.redis.setNX(key, 1, 24 * 60 * 60);
        // 已经记录了，则不记录
        if (result) {
          for (const strategy of shouldStoreStrategies) {
            await this.saveStrategyUnique(strategy);
          }
        }
      }
    }
    if (coinsCost.currency > 0) {
      await this.addTransaction(-coinsCost.currency, 'CURRENCY', 'REDEEM', reason, description, createdBy);
    }
    if (coinsCost.credit > 0) {
      await this.addTransaction(-coinsCost.credit, 'CREDIT', 'REDEEM', reason, description, createdBy);
    }
  }

  public async earn(
    amount: bigint | number,
    coinType: Omit<$Enums.CoinType, 'VIRTUAL'>,
    reason?: CoinTransactionReason,
    description?: string,
    createdBy?: string,
  ) {
    assert(coinType !== 'VIRTUAL', 'VIRTUAL coin type is not allowed');

    return this.addTransaction(amount, coinType as $Enums.CoinType, 'EARN', reason, description, createdBy);
  }

  /**
   * 新增一笔交易，同时更新余额
   */
  private async addTransaction(
    amount: bigint | number,
    coinType: $Enums.CoinType,
    type: $Enums.CoinTransactionType,
    reason?: CoinTransactionReason,
    description?: string,
    createdBy?: string,
  ) {
    const createTrans = db.prisma.coinTransaction.create({
      data: {
        id: generateNanoID('cointrans'),
        accountId: this._model.id,
        amount,
        description,
        type,
        coinType,
        reason,
        createdBy,
        createdAt: new Date(),
      },
    });
    const updateBalance = db.prisma.coinAccount.update({
      where: { id: this._model.id },
      data: {
        balance: coinType !== 'VIRTUAL' ? { increment: amount } : undefined,
        credit: coinType === 'CREDIT' ? { increment: amount } : undefined,
        currency: coinType === 'CURRENCY' ? { increment: amount } : undefined,
      },
    });

    if (coinType !== 'VIRTUAL') {
      this._model.balance += BigInt(amount);
    }
    if (coinType === 'CREDIT') {
      this._model.credit += BigInt(amount);
    }
    if (coinType === 'CURRENCY') {
      this._model.currency += BigInt(amount);
    }

    const [newTransaction, updatedAccount] = await db.prisma.$transaction([createTrans, updateBalance]);

    return { newTransaction, updatedAccount };
  }

  async existTransaction(reason: Partial<CoinTransactionReason>): Promise<boolean> {
    const inputs: Prisma.CoinTransactionWhereInput[] = [];
    for (const [key, value] of Object.entries(reason)) {
      // reason目前无嵌套结构，所以只有一层
      if (typeof value === 'object' && value !== null) {
        // traverse(value, [...path, key]);
        // eslint-disable-next-line no-continue
        continue;
      }
      inputs.push({
        reason: {
          path: [key],
          equals: value,
        },
      });
    }
    const count = await db.prisma.coinTransaction.count({
      where: {
        accountId: this._model.id,
        AND: inputs,
      },
    });
    return count > 0;
  }

  async getTransactions(filter?: {
    from?: Date;
    to?: Date;
    coinType?: $Enums.CoinType;
  }): Promise<UserCoinTransactionVO[]> {
    const transactions = await db.prisma.coinTransaction.findMany({
      where: {
        accountId: this._model.id,

        createdAt:
          filter === undefined
            ? undefined
            : {
                gte: filter.from,
                lte: filter.to,
              },
        coinType: filter?.coinType,
      },
      orderBy: [{ createdAt: 'desc' }],
    });
    return transactions.map((row) => ({
      amount: Number(row.amount),
      description: row.description || '',
      type: row.type,
      coinType: row.coinType,
      reason: row.reason as CoinTransactionReason,
      createdAt: row.createdAt.toISOString(),
    }));
  }

  async getTransactionsWithPagination(filter?: {
    from?: Date;
    to?: Date;
    coinType?: $Enums.CoinType;
    page?: Pagination;
  }): Promise<CreditTransactionPaginationVO> {
    const { pageNo, pageSize } = PaginationSchema.parse(filter?.page || {});
    const transPOs = await CoinTransactionSO.findByCoinAccountId(this._model.id, filter, { pageNo, pageSize });
    const total = await CoinTransactionSO.countByCoinAccountId(this._model.id, filter);
    const userIds: string[] = transPOs.map((t) => t.model.createdBy).filter((id) => id) as string[];
    const spaceId = this.relationType === 'SPACE' ? this._model.relationId : undefined;
    // userId => member
    const memberMap = new Map<string, MemberSO>();
    if (spaceId) {
      const members = await MemberSO.findByUserIds(spaceId, userIds);
      members.forEach((m) => {
        memberMap.set(m.userId, m);
      });
    }
    return {
      data: await Promise.all(
        transPOs.map((t) => t.toVO({ member: t.model.createdBy ? memberMap.get(t.model.createdBy) : undefined })),
      ),
      pagination: {
        pageNo,
        pageSize,
        total,
      },
    };
  }

  static async dbUpsertOperation(coinAccountRelation: $Enums.CoinAccountType, coinAccountRelationId: string) {
    const account = await db.prisma.coinAccount.upsert({
      where: {
        relationType_relationId: {
          relationType: coinAccountRelation,
          relationId: coinAccountRelationId,
        },
      },
      update: {},
      create: {
        relationType: coinAccountRelation,
        relationId: coinAccountRelationId,
        id: generateNanoID('coinacc'),
        balance: BigInt(0),
      },
    });
    return account;
  }

  /**
   * 刷新，重新从数据库抓取
   */
  async refetch() {
    const refreshModel = await CoinAccountSO.dbUpsertOperation(this._model.relationType, this._model.relationId);
    this._model = refreshModel!;
  }

  /**
   *  这是一个lazy init get，如果没有则创建账户
   *
   * @param coinAccountRelation
   * @param coinAccountRelationId
   * @returns
   */
  static async get(
    coinAccountRelation: $Enums.CoinAccountType,
    coinAccountRelationId: string,
    strategies?: CoinAccountStrategy[],
  ) {
    const account = await this.dbUpsertOperation(coinAccountRelation, coinAccountRelationId);
    return new CoinAccountSO(account!, strategies);
  }

  createTransaction(
    amount: bigint | number,
    coinType: $Enums.CoinType,
    type: $Enums.CoinTransactionType,
    reason?: CoinTransactionReason,
    description?: string,
    createdBy?: string,
  ) {
    return [
      db.prisma.coinTransaction.create({
        data: {
          id: generateNanoID('cointrans'),
          accountId: this._model.id,
          amount,
          description,
          type,
          coinType,
          reason,
          createdBy,
          createdAt: new Date(),
        },
      }),
      db.prisma.coinAccount.update({
        where: { id: this._model.id },
        data: {
          balance: coinType !== 'VIRTUAL' ? { increment: amount } : undefined,
          credit: coinType === 'CREDIT' ? { increment: amount } : undefined,
          currency: coinType === 'CURRENCY' ? { increment: amount } : undefined,
        },
      }),
    ];
  }
}
