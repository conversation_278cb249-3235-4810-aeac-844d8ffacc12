import { Body, Head, Html, Text } from '@react-email/components';
import { generateNanoID } from 'basenext/utils/nano-id';
import React from 'react';
import sharp from 'sharp';
import { AwsSes } from '@bika/domains/email-providers/aws-ses/server';
import { ResendProvider } from '@bika/domains/email-providers/resend/server';
import { SMTPProvider } from '@bika/domains/email-providers/smtp/server';
import { ISendMailParams, SendResult } from '@bika/domains/email-providers/types';
import { db, EmailModel } from '@bika/server-orm';
import { SendEmailType } from '@bika/types/automation/bo';
import { EmailProviderEnvVarSchema, SendEmail, type EmailProviderEnvVar } from '@bika/types/email/bo';
import { EmailTracker } from './email-tracker';

type EmailProviderType = 'resend' | 'smtp' | 'aws-ses';

const defaultSenderName: string = 'Bika.ai';
// const defaultSenderEmail: string = '<<EMAIL>>';
// const defaultFrom = `${defaultSenderName} ${defaultSenderEmail}`;

interface IEmailOptions {
  type?: SendEmailType;
  spaceId?: string;
  relationId?: string;
  metadata?: object;
}

export class EmailSO {
  private _model: EmailModel;

  private constructor(po: EmailModel) {
    this._model = po;
  }

  get model() {
    return this._model;
  }

  get id() {
    return this._model.id;
  }

  static async init(emailId: string) {
    const emailPO = await db.mongo.email.findOne({
      id: emailId,
    });

    return new EmailSO(emailPO!);
  }

  /**
   * 纯粹使用sharp生成一个1x1的无色无味像素图片buffer，用于邮件追踪
   */
  static async getTrackerPixelImage() {
    const image = sharp({
      create: {
        width: 1,
        height: 1,
        channels: 4,
        background: { r: 0, g: 0, b: 0, alpha: 0 },
      },
    });

    const buffer = await image.png().toBuffer();
    return this.toArrayBuffer(buffer);
  }

  private static toArrayBuffer(buffer: Buffer) {
    const arrayBuffer = new ArrayBuffer(buffer.length);
    const view = new Uint8Array(arrayBuffer);
    // eslint-disable-next-line no-plusplus
    for (let i = 0; i < buffer.length; ++i) {
      view[i] = buffer[i];
    }
    return arrayBuffer;
  }

  static async findByRelationId(relationId: string): Promise<{ success: number; track: number }[]> {
    return db.mongo.email.find({ relationId }, { success: 1, track: 1 });
  }

  /**
   * 记录一次邮件发送记录到数据库
   */
  static async createPO(sendResult: SendResult, opts?: IEmailOptions) {
    const { type, spaceId, relationId } = opts || {};
    const emailPO = await db.mongo.email.create({
      id: sendResult.id,
      subject: sendResult.subject,
      body: sendResult.body,
      to: sendResult.to,
      success: sendResult.success,
      messageId: sendResult.messageId,
      type,
      spaceId,
      relationId,
      metadata: sendResult.metadata,
      track: 0,
    });
    return emailPO;
  }

  /**
   * 找到数据库记录，并track计数+1
   * @param emailId
   */
  static async track(emailId: string) {
    return db.mongo.email.findOneAndUpdate(
      { id: emailId },
      {
        $inc: { track: 1 },
      },
    );
  }

  /**
   * 发送邮件
   *
   * @param sendEmailBO
   * @param metadata 存入数据库的附加数据，方便之后寻找
   * @returns
   */
  static async send(sendEmailBO: SendEmail, opts?: IEmailOptions): Promise<EmailSO> {
    const { type: mailType, subject, to, senderName } = sendEmailBO;
    const { metadata } = opts || {};

    const toReactElement = (html: string) => (
      <Html>
        <Head />
        <Body>
          <Text dangerouslySetInnerHTML={{ __html: html }} />
        </Body>
      </Html>
    );

    let sendResult;
    switch (mailType) {
      case 'SYSTEM': {
        const systemEmailProviderEnv = process.env.SYSTEM_EMAIL_PROVIDER;
        const systemEmailProvider = systemEmailProviderEnv
          ? EmailProviderEnvVarSchema.parse(JSON.parse(systemEmailProviderEnv))
          : null;
        sendResult = await this.doSendEmailByService(systemEmailProvider, {
          subject,
          text: sendEmailBO.react,
          to,
          metadata,
        });
        break;
      }
      case 'SERVICE': {
        const serviceEmailProviderEnv = process.env.SERVICE_EMAIL_PROVIDER || process.env.SYSTEM_EMAIL_PROVIDER;
        const serviceEmailProvider = serviceEmailProviderEnv
          ? EmailProviderEnvVarSchema.parse(JSON.parse(serviceEmailProviderEnv))
          : null;
        const { body, cc, bcc, replyTo } = sendEmailBO;
        const bodyReact = typeof body === 'string' ? toReactElement(body) : body;
        sendResult = await this.doSendEmailByService(serviceEmailProvider, {
          subject,
          text: bodyReact,
          to,
          metadata,
          senderName,
          cc,
          bcc,
          replyTo,
        });
        break;
      }
      // system和service也支持smtp，但是是用系统SMTP，这里的SMTP，指用户自己的SMTP
      case 'SMTP': {
        const { host, port, auth, from, body, cc, bcc, replyTo } = sendEmailBO;
        const fromEmail = from || auth?.user;
        let fromStr;
        if (fromEmail) {
          fromStr = senderName ? `${senderName} <${fromEmail}>` : fromEmail;
        }

        const smtpSender = new SMTPProvider({
          provider: {
            provider: 'smtp',
            host,
            port,
            username: auth?.user,
            password: auth?.pass,
          },
          from: fromStr,
        });
        const emailId = generateNanoID('ema'); // 区别'eml'和'ema'，两种不同的发送方式不同的id标记
        sendResult = await smtpSender.sendEmail({
          emailId,
          subject,
          text: <EmailTracker emailId={emailId}>{toReactElement(body)}</EmailTracker>,
          to,
          metadata,
          senderName,
          cc,
          bcc,
          replyTo,
        });
        break;
      }
      default:
        throw new Error(`Unsupported email BO type: ${mailType}`);
    }
    const po = await this.createPO(sendResult, opts);
    return new EmailSO(po);
  }

  /**
   * Marketing Email是一种特有的email，有较高的到达率，但是价格也较贵，使用第三方特别服务
   */
  // private static async doSendMarketingEmail(): Promise<SendResult> {
  //   // process.env.MARKETING_EMAIL_PROVIDER
  //   // TODO: marketing email未接入
  //   throw new Error('Unsupported marketing email yet');
  // }

  /**
   * 使用官方的TransactionEmail Provider进行邮件发送
   */
  private static async doSendEmailByService(
    provider: EmailProviderEnvVar | null,
    omitParams: Omit<ISendMailParams, 'emailId'>,
  ): Promise<SendResult> {
    const emailId = generateNanoID('eml');
    const { subject, text, senderName = defaultSenderName, to } = omitParams;

    if (!provider) {
      // 邮箱服务未配置
      if (!process.env.CI) {
        console.error(`Email service is not configured`);
      }

      return {
        id: emailId,
        subject,
        body: '',
        to,
        success: false,
        messageId: undefined,
        metadata: 'Email service is not configured',
      };
    }

    const emailType = provider.provider;
    const trackedContent = <EmailTracker emailId={emailId}>{text}</EmailTracker>;
    const params = {
      ...omitParams,
      emailId,
      text: trackedContent,
      senderName,
    };

    switch (emailType) {
      case 'resend': {
        const sender = new ResendProvider(provider);
        return sender.sendEmail(params);
      }
      case 'smtp': {
        const sender = new SMTPProvider({
          provider,
          // host: process.env.SMTP_HOST!,
          // port: process.env.SMTP_PORT ? Number(process.env.SMTP_PORT) : 465,
          // auth: {
          //   user: process.env.SMTP_USERNAME!,
          //   pass: process.env.SMTP_PASSWORD!,
          // },
          from: `${senderName} <${process.env.EMAIL_FROM || provider.username!}>`,
        });
        return sender.sendEmail(params);
      }
      case 'aws-ses': {
        const sender = new AwsSes(provider);
        return sender.sendEmail(params);
      }
      default:
        throw new Error(`Email service is not configured, email type: ${emailType}`);
    }
  }
}
