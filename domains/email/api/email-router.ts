import { z } from 'zod';
import { UserSO } from '@bika/domains/user/server';
import { protectedProcedure, publicProcedure, router } from '@bika/server-orm/trpc';
import { EmailBoxesDTOSchema } from '@bika/types/email/dto';
import { EmailSendVerifyCodeVOSchema } from '@bika/types/email/vo';
import * as EmailController from './email-controller';

export const emailRouter = router({
  sendVerificationMail: publicProcedure
    .input(
      z.object({
        email: z.string(),
        check: z.boolean().optional().describe('是否检查邮箱'),
      }),
    )
    .output(EmailSendVerifyCodeVOSchema)
    .mutation(async (opts) => {
      const { input } = opts;
      const { email, check } = input;
      const emailSO = await EmailController.sendMailVerificationCode(email);
      const result = { sent: !!emailSO };
      if (check) {
        const user = await UserSO.findByEmail(email);
        const spaceCount = user ? await user.getSpaceCount() : 0;
        return {
          ...result,
          userExist: !!user,
          spaceCount,
        };
      }
      return result;
    }),

  fetchBoxes: protectedProcedure.input(EmailBoxesDTOSchema).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return EmailController.fetchBoxes(user, input);
  }),
});
