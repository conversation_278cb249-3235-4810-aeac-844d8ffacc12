import { isEmail } from 'basenext/utils/string';
import { MailBoxes } from 'safe-imap';
import { VerifyCodeEmail } from '@bika/contents/email/Verify';
import { ImapSO } from '@bika/domains/email/server/imap';
import { IntegrationSO } from '@bika/domains/integration/server/integration-so';
import { getDefaultLocale, randomNumberString } from '@bika/domains/shared/server';
import { RemoteStorageHelper } from '@bika/domains/system/server/remote-storage/remote-storage-helper';
import { UserSO } from '@bika/domains/user/server/user-so';
import { TRPCError } from '@bika/server-orm/trpc';
import { EmailBoxesDTO } from '@bika/types/email/dto';
import { IMAPEmailIntegration, SpaceIntegrationTypeSchema } from '@bika/types/integration/bo';
import { IMAPInputSchema } from '@bika/types/shared/input';
import { EmailSO } from '../server/email-so';

export async function sendMailVerificationCode(email: string) {
  if (!isEmail(email)) {
    throw new TRPCError({ code: 'BAD_REQUEST', message: `Invalid email ${email}` });
  }
  const code = randomNumberString(6);
  await RemoteStorageHelper.verificationCode.create('MAIL_VERIFICATION_CODE', email, code);

  // send email
  const user = await UserSO.findByEmail(email);
  const { title, content } = VerifyCodeEmail({
    locale: user?.locale || getDefaultLocale(),
    verificationCode: code,
    recipient: user?.model.name || undefined,
  });

  return EmailSO.send({
    type: 'SYSTEM',
    subject: title,
    react: content,
    to: email,
  });
}

export async function fetchBoxes(user: UserSO, dto: EmailBoxesDTO) {
  const fetchImapInput = async () => {
    // 直接传入IMAP配置，无需鉴权
    const { success, data } = IMAPInputSchema.safeParse(dto);
    if (success) {
      return data;
    }
    // 传入集成ID，需要鉴权
    const integrationId = 'integrationId' in dto ? dto.integrationId : '';
    const integration = await IntegrationSO.init(integrationId);
    // 检查是否是IMAP集成
    if (integration.type !== SpaceIntegrationTypeSchema.enum.IMAP_EMAIL_ACCOUNT) {
      throw new TRPCError({ code: 'BAD_REQUEST', message: 'Invalid integration type' });
    }
    // 检查用户是否是集成空间的成员
    await user.checkExistSpace(integration.relationId);
    return integration.getBO<IMAPEmailIntegration>();
  };
  const imapInput = await fetchImapInput();

  // imap connection
  const imap = ImapSO.init(imapInput);
  try {
    await imap.connect();
  } catch (_e) {
    throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: 'IMAP connection error' });
  }
  // get boxes
  const boxes = await imap.getBoxes();

  const fetchSelectedBoxes = (mailBoxes: MailBoxes, prefix?: string) => {
    const acc: string[] = [];
    for (const [key, value] of Object.entries(mailBoxes)) {
      const mailBox = prefix ? `${prefix}${key}` : key;
      const { attribs, delimiter, children } = value;
      const isNoSelect = attribs.some((attr) => attr.toUpperCase().includes('NOSELECT'));
      if (!isNoSelect) {
        acc.push(mailBox);
      }
      // 递归查找子文件夹的信箱
      if (children && typeof children === 'object' && Object.keys(children).length > 0) {
        acc.push(...fetchSelectedBoxes(children, mailBox + delimiter));
      }
    }
    return acc;
  };
  return fetchSelectedBoxes(boxes);
}
