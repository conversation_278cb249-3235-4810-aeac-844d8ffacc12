import { z } from 'zod';
import { SseSO } from '@bika/domains/event/server/sse/sse-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { protectedProcedure, router } from '@bika/server-orm/trpc';
import { TalkMutateDTOSchema } from '@bika/types/space/dto';
import { TalkDetailVOSchema } from '@bika/types/space/vo';
import { TalkSO } from '../server/talk-so';

export const talkTrpcRouter = router({
  /**
   * Search for space home agents, org charts, and more smartly
   */
  searchTalks: protectedProcedure
    .input(
      z.discriminatedUnion('mode', [
        z.object({
          // For Space Home Agents
          mode: z.literal('recently-chats'),
          spaceId: z.string().describe('The space ID to search for recent chats'),
        }),
        z.object({
          // For Space Home Agents
          mode: z.literal('space-home'),
          spaceId: z.string().describe('The space ID'),
        }),
        z.object({
          // For Space Home Agents
          mode: z.literal('org-chart'),
          unitIds: z.array(z.string()).describe('List of unit IDs to include in the org chart'),
        }),
        z.object({
          // Specify one ai agent node member
          mode: z.literal('unit'),
          unitId: z.string().describe('The unit ID to search for'),
        }),
      ]),
    )
    .output(z.array(TalkDetailVOSchema))
    .query(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      if (input.mode === 'recently-chats') {
        return TalkSO.recently(userId, input.spaceId);
      }
      if (input.mode === 'space-home') {
        return TalkSO.spaceHome(userId, input.spaceId);
      }
      if (input.mode === 'org-chart') {
        return TalkSO.orgChart(userId, input.unitIds);
      }
      if (input.mode === 'unit') {
        return TalkSO.orgChart(userId, [input.unitId]);
      }

      return [];
    }),

  /**
   *
   */
  createTalk: protectedProcedure
    .input(
      TalkMutateDTOSchema.and(
        z.object({
          spaceId: z.string(),
        }),
      ),
    )
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      const member = await user.getMember(input.spaceId);
      const talkSO = await TalkSO.upsert(opts.input, member.id);
      const talkVO = await talkSO.toVO();
      SseSO.emit(user.id, { name: 'talk', talk: talkVO!, memberId: member.id });
      return talkVO;
    }),

  /**
   * 置顶或取消置顶对话
   */
  pinTalk: protectedProcedure
    .input(
      z.object({
        spaceId: z.string(),
        talkId: z.string(),
        isPinned: z.boolean(),
      }),
    )
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      const { spaceId, talkId, isPinned } = input;

      const user = await UserSO.init(userId);
      const member = await user.getMember(spaceId);

      const recipient = { recipientType: 'MEMBER' as const, recipientId: member.id };
      const talkSO = await TalkSO.updatePinnedById(talkId, recipient, isPinned, userId);

      if (!talkSO) {
        throw new Error(`Talk with id ${talkId} not found or you don't have permission to modify it`);
      }

      return talkSO.toVO();
    }),

  /**
   * 删除对话
   */
  removeTalk: protectedProcedure
    .input(
      z.object({
        spaceId: z.string(),
        talkId: z.string(),
      }),
    )
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      const { spaceId, talkId } = input;

      const user = await UserSO.init(userId);
      const member = await user.getMember(spaceId);

      const recipient = { recipientType: 'MEMBER' as const, recipientId: member.id };
      const isRemoved = await TalkSO.removeById(talkId, recipient, userId);

      if (!isRemoved) {
        throw new Error(`Talk with id ${talkId} not found or you don't have permission to delete it`);
      }

      return { success: true, talkId };
    }),
});
