import _ from 'lodash';
import { describe, expect, test } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock';
import { AutomationSO } from '@bika/domains/automation/server/automation-so';
import { StoreTemplateSO } from '@bika/domains/store/server/store-template-so';
import { generateNanoID } from 'basenext/utils/nano-id';
import { CustomTemplate } from '@bika/types/template/bo';

describe('Automation property ignore tests', () => {
  const mockTemplate = async (userId: string, spaceId: string): Promise<CustomTemplate> => {
    const template: CustomTemplate = {
      schemaVersion: 'v1',
      visibility: 'PUBLIC',
      category: 'official',
      cover: {
        type: 'COLOR',
        color: 'BLUE',
      },
      templateId: generateNanoID('tpl'),
      name: 'test',
      version: '1.0.0',
      ignoreChanged: ['resources[0].triggers[0].input.scheduler.repeat.every.interval'],
      resources: [
        {
          resourceType: 'AUTOMATION',
          templateId: 'test-automation',
          name: 'automation-test',
          triggers: [
            {
              triggerType: 'SCHEDULER',
              input: {
                type: 'SCHEDULER',
                scheduler: {
                  repeat: {
                    every: {
                      type: 'DAY',
                      interval: 1,
                    },
                  },
                  datetime: '2024-11-03T19:38:34.203Z',
                },
              },
            },
          ],
          actions: [
            {
              templateId: 'test_action_1',
              actionType: 'WEBHOOK',
              input: {
                type: 'WEBHOOK',
                method: 'GET',
                url: 'https://test.bika.ai',
                headers: [],
              },
            },
          ],
        },
      ],
    };
    await StoreTemplateSO.upsertPayload(userId, spaceId, template);
    return template;
  };

  const upgradeTemplate = async (
    userId: string,
    spaceId: string,
    template: CustomTemplate,
  ): Promise<CustomTemplate> => {
    _.set(template, 'version', '1.1.0');
    // add a new actions
    _.set(template, 'resources[0].triggers[0].input.scheduler.repeat.every.interval', 2);
    _.set(template, 'resources[0].actions[1]', {
      actionType: 'WECOM_WEBHOOK',
      input: {
        templateId: 'test_action_1',
        type: 'WECOM_WEBHOOK',
        urlType: 'URL',
        url: 'wecom webhook url',
        data: {
          msgtype: 'markdown',
          markdown: {
            content: '## This ia a markdown message\n **Hello**, Bika!',
          },
        },
      },
    });
    // add ignore change for new actions
    _.set(template, 'ignoreChanged[1]', 'resources[0].actions[1].input');
    await StoreTemplateSO.upsertPayload(userId, spaceId, template);
    return template;
  };

  test('install automation from template', async () => {
    const { user, space } = await MockContext.initUserContext();
    const template = await mockTemplate(user.id, space.id);
    const folder = await space.installTemplateById(user, template.templateId!);
    const automationNode = await folder.findChildNodeByTemplateId('test-automation');
    expect(automationNode).not.toBeNull();
    const automationSO = await automationNode?.toResourceSO<AutomationSO>();
    const triggers = (await automationSO?.getTriggers())!;
    expect(triggers[0].toBO().input).toStrictEqual({
      type: 'SCHEDULER',
      scheduler: {
        repeat: {
          every: {
            type: 'DAY',
            interval: 1,
          },
        },
        datetime: '2024-11-03T19:38:34.203Z',
      },
    });
    const actions = (await automationSO?.getActions())!;
    expect((await actions[0].toBO()).input).toStrictEqual({
      type: 'WEBHOOK',
      method: 'GET',
      url: 'https://test.bika.ai',
      headers: [],
    });
  });

  test('upgrade automation template', async () => {
    const { user, space } = await MockContext.initUserContext();
    const template = await mockTemplate(user.id, space.id);
    const folder = await space.installTemplateById(user, template.templateId!);
    // upgrade template
    await upgradeTemplate(user.id, space.id, template);
    const upgradeFolder = await folder.upgradeTemplate(user);
    const automationNode = await upgradeFolder.findChildNodeByTemplateId('test-automation');
    expect(automationNode).not.toBeNull();
    const automationSO = await automationNode?.toResourceSO<AutomationSO>();
    const triggers = (await automationSO?.getTriggers())!;
    expect(triggers[0].toBO().input).toStrictEqual({
      type: 'SCHEDULER',
      scheduler: {
        repeat: {
          every: {
            type: 'DAY',
            interval: 1,
          },
        },
        datetime: '2024-11-03T19:38:34.203Z',
      },
    });
    const actions = (await automationSO?.getActions())!;
    expect((await actions[0].toBO()).input).toStrictEqual({
      type: 'WEBHOOK',
      method: 'GET',
      url: 'https://test.bika.ai',
      headers: [],
    });
    expect((await actions[1].toBO()).input).toStrictEqual({
      type: 'WECOM_WEBHOOK',
      urlType: 'URL',
      url: 'wecom webhook url',
      data: {
        msgtype: 'markdown',
        markdown: {
          content: '## This ia a markdown message\n **Hello**, Bika!',
        },
      },
    });
  });
});
