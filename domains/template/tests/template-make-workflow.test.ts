import { generateNanoID } from 'basenext/utils/nano-id';
import { expect, test } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock';
import { AutomationSO } from '../../automation/server/automation-so';
import { DatabaseSO } from '../../database/server/database-so';
import { FolderSO } from '../../node/server/folder-so';
import { StoreTemplateSO } from '../../store/server/store-template-so';

test('Template Make Workflow', async () => {
  const { user, space, rootFolder } = await MockContext.initUserContext();

  // 我是一个内部模板制作人员，我要制作一个模板！

  // =========================草草制作阶段

  // 我创建了一些资源；
  const folderNode = await rootFolder.createChildSimple(user, {
    resourceType: 'FOLDER',
    name: 'new template folder',
  });
  const folderSO = await folderNode.toResourceSO<FolderSO>();
  // will add three default fields and one default view
  const databaseNode = await folderSO.createChildSimple(user, {
    resourceType: 'DATABASE',
    name: 'new template database',
  });
  const databaseSO = await databaseNode.toResourceSO<DatabaseSO>();
  const createdByField = await databaseSO.createField(user, {
    type: 'CREATED_BY',
    name: 'creattor',
  });
  const automationNode = await folderSO.createChildSimple(user, {
    resourceType: 'AUTOMATION',
    name: 'new template automation',
  });
  const automationSO = await automationNode.toResourceSO<AutomationSO>();
  await automationSO.addTrigger(user, {
    triggerType: 'SCHEDULER',
    input: {
      scheduler: {
        datetime: {
          type: 'TODAY',
          hour: 12,
          minute: 0,
        },
      },
      type: 'SCHEDULER',
    },
  });
  await automationSO.addAction(user.id, {
    actionType: 'FIND_RECORDS',
    input: {
      type: 'DATABASE_WITH_FILTER',
      databaseId: databaseSO.id,
      filters: {
        conjunction: 'And',
        conditions: [],
        conds: [
          {
            fieldId: createdByField.id,
            fieldType: 'CREATED_BY',
            clause: {
              operator: 'Is',
              value: 'SELF',
            },
          },
        ],
      },
    },
  });
  await automationSO.addAction(user.id, {
    actionType: 'SEND_REPORT',
    input: {
      type: 'MARKDOWN',
      subject: 'subject',
      markdown: '# markdown',
      to: [
        {
          type: 'USER',
          userId: user.id,
        },
      ],
    },
  });
  // finish create rsource, then reload
  const folderResource = await FolderSO.init(folderNode.id);

  // 1. 数据表，日志
  // 创建一个数据库
  // (可选)导入Excel文件，创建新的database
  const databaseResource = null;

  // (可选)增量导入Excel文件

  // 2. automation，自动化，每周基于日志整理周报
  const automationResource = null;

  // =========================导出.bika资源文件后续备用，名字是 文件夹id.bika，姑且定义为A
  // 我先把资源导出成resources;
  // expect()  资源ID
  const bikafileResources1 = null;

  // =========================模板商城发布了
  // 我发布这个模板
  // 我手工自定义设置这个模板的ID 为 test-template-{genNanoId()}
  const templateId = await folderResource.publish(user, {
    type: 'TEMPLATE_CENTER',
    data: {
      templateId: `test-template-${generateNanoID()}`,
      category: 'project',
      version: '1.0.0',
      visibility: 'PUBLIC',
      detach: true,
    },
  });
  expect(templateId.startsWith('test-template-')).toBe(true);
  // 我发布后，修改模板的initMissions
  // storeTemplate.update()
  // storeTemplate.EditName
  const storeTemplate = await StoreTemplateSO.init(templateId);
  // await storeTemplate.update({
  //   name: 'test-template',
  // });
  const newStoreTemplate = await StoreTemplateSO.init(templateId);

  // =========================导出.bika模板

  // 我导出这个模板Bikafile
  const presignedPutUrl = await (await FolderSO.init(folderNode.id)).publish(user, { type: 'LOCAL' });
  // const filePath = download(presignedPutUrl);
  // .bika读取
  // const bikafile = new Bikafile({
  //   mode: 'file',
  //   filePath,
  // });
  // expect(bikafile.data.format).to.equal('TEMPLATE');
  // const bikafileTemplate = bikafile.data as BikafileTemplateData;
  // expect(bikafileTemplate.template.templateId).to.equal(templateId);

  // 导出的模板里，README跟storeTemplate的README一样
  // expect(bikafileTemplate.readme).toBeStritEqual(newStoreTemplate.readme);

  // 我把模板打散，再导出resources bikafile，取出template的bikafile，确保一模一样(JSON Strict Equal)
  // expect(bikafileTemplate.template.resources).toBeStritEqual(bikafileResources1);

  // templateFolder.detach(); // 变成folder了

  // 打散后，再导出resources bikafile，两次的resources，确保一模一样(JSON Strict Equal)
  const bikafileResources2 = null;
  // expect(bikafileResources1).toBeStritEqual(bikafileResources2);

  // =========================.bika模板不支持导入的！但支持放在git目录里
  // importBikafile(bikafileTemplate); // 会报错

  // =========================导入.bika资源文件
  // importBikafile(bikafileResources2); // 成功

  // =========================删掉templateFolder、Folder们

  // 从模板商城安装publish过的模板
  const templateFolder = await space.installTemplateById(user, templateId);

  // 导出模板.bika
  const bikafileTemplate2 = null;

  // releases一样的
  // expect(newStoreTemplate.releases).toBeStritEqual(bikafileTemplate2.releases);

  // 导出资源.bika
  const bikafileResources3 = null;

  // 确保两者一致 (BO比较)

  // expect(bikafileTemplate2.resources).toBeStritEqual(bikafileResources3);

  // 重新修改template folder，在重新发布

  // storeTemplate.publish() // 二次发布

  // 导出Excel文件，导出数据库

  // 模板发布了，但是我要让它下架啊，设置private，或下架删除

  // template.unpublish()

  // 导入defaultTemplate
  // TODO:...

  // 上传附件到这个表
  // TODO: ...

  // 导出.bikafile文件
  // TODO: 附件放到bika文件里了

  expect(1).toBe(1);
});

test('Excel to Bikafile', async () => {
  // Excel 转 Bikafile
  // Bikafile导入
});

test('Excel incremental import', async () => {
  // 新建一个database
  // 下载增量Excel导入模板（有原来代码）
  // 上传增量Excel文件
  // 导入增量Excel文件
  // Excel转成了Bikafile文件
});
