import assert from 'assert';
import _ from 'lodash';
import { generateNanoID } from 'basenext/utils/nano-id';
import { AutomationSO } from '@bika/domains/automation/server/automation-so';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { FormSO } from '@bika/domains/form/server/form-so';
import { MirrorSO } from '@bika/domains/mirror/server/mirror-so';
import { TemplateFolderSO } from '@bika/domains/node/server/folder-so';
import { NodeResourceAdapter } from '@bika/domains/node/server/node-resource-adapter';
import { RoleSO } from '@bika/domains/unit/server/role-so';
import { UnitFactory } from '@bika/domains/unit/server/unit-factory';
import { UserSO } from '@bika/domains/user/server';
import { db, MongoTransactionCB } from '@bika/server-orm';
import { Action, Automation, Trigger } from '@bika/types/automation/bo';
import { Database } from '@bika/types/database/bo';
import {
  CONST_PREFIX_ACTION,
  CONST_PREFIX_AUT,
  CONST_PREFIX_DAT,
  CONST_PREFIX_FIELD,
  CONST_PREFIX_MIRROR,
  CONST_PREFIX_TRIGGER,
  CONST_PREFIX_VIEW,
} from '@bika/types/database/vo';
import { FormBO } from '@bika/types/form/bo';
import { DatabaseViewMirrorBO, Folder, MirrorBO, MirrorTypeEnum, NodeResource } from '@bika/types/node/bo';
import { CustomTemplate, TemplateIgnoreChangedEnum, TemplateIgnoreChangedEnumSchema } from '@bika/types/template/bo';
import { Unit, UnitRoleSchema } from '@bika/types/unit/bo';
import { TemplateIdPathStore } from './template-id-path-store';
import { NodeOperations, PrismaOperations, UnitOperations } from './types';
import { filterUndefinedValue } from './util';

const { isEmpty, cloneDeep } = _;

type UpgradeContext = {
  user: UserSO;
};

/**
 * template migration.
 * 目前的模板升级只做增量升级,不做删除/替换操作
 */
export class TemplateUpgrader {
  private readonly _context: UpgradeContext;

  private readonly _templateFolder: TemplateFolderSO;

  /**
   * 这里存储，templateId对应的生成出来的instance id，便于后续关联真正的id
   */
  private _templateIdPathStore = new TemplateIdPathStore();

  private constructor(context: UpgradeContext, templateFolder: TemplateFolderSO) {
    this._context = context;
    this._templateFolder = templateFolder;
  }

  public static init(context: UpgradeContext, templateFolder: TemplateFolderSO) {
    return new TemplateUpgrader(context, templateFolder);
  }

  get templateId(): string {
    return this._templateFolder.templateId;
  }

  private initResourceRelation(templateId: string, resourceTemplate: Automation | Database) {
    const convertToInstanceId = (templateIdSuffix: string): string => {
      // 先查找当前模板下的实例id
      const instanceId = this._templateIdPathStore.getMayBeNull(`${templateId}:${templateIdSuffix}`);
      if (instanceId) {
        return instanceId;
      }
      // 指定跨模板引用的情况。如：foreignDatabaseTemplateId = 'other_template:databaseTemplateId'
      if (templateIdSuffix.includes(':')) {
        const id = this._templateIdPathStore.getMayBeNull(templateIdSuffix);
        if (id) {
          return id;
        }
        return templateIdSuffix;
      }
      throw new Error(`can not find instance id for ${templateIdSuffix}`);
    };

    const relation = () => {
      const resourceSO = NodeResourceAdapter.new(resourceTemplate.resourceType);
      resourceSO.relationInstanceId(resourceTemplate, { convertToInstanceId });
    };
    relation();
  }

  private async migrateDatabaseNode(
    upgradeDatabase: Database,
    preNodeId?: string,
  ): Promise<{ lastNodeId?: string; operations: NodeOperations }> {
    const { user } = this._context;
    let lastNodeId = preNodeId;
    const operations: NodeOperations = [];
    // 连接
    this.initResourceRelation(this.templateId, upgradeDatabase);
    // console.log(`连接后的模板数据: ${JSON.stringify(upgradeDatabase)}`);
    // database必须有templateId, 不然用户修改后,很难做差异对比
    if (upgradeDatabase.templateId) {
      // 存在templateId, 通过templateId和模板对应根节点来找到原来的节点
      const databaseNodeSO = await this._templateFolder.findChildNodeByTemplateId(upgradeDatabase.templateId);
      if (databaseNodeSO) {
        // 找到匹配的节点,比较差异
        const databaseSO = await databaseNodeSO.toResourceSO<DatabaseSO>();
        // 修改数据库节点的名称和描述
        const updateDatabaseOperation = databaseSO.updateOperation({
          name: upgradeDatabase.name,
          description: upgradeDatabase.description,
          updatedBy: user.id,
          node: {
            update: {
              name: upgradeDatabase.name,
              description: upgradeDatabase.description,
              updatedBy: user.id,
            },
          },
        });
        operations.push(updateDatabaseOperation);

        // ============== 视图变更 ===========
        // 需要升级的视图
        const upgradeViews = upgradeDatabase.views;
        // 过滤掉比较差异,视图
        if (upgradeViews) {
          // 升级视图操作
          operations.push(...(await databaseSO.upgradeDatabaseViewOperation(user.id, upgradeViews)));
        }

        // ============== 字段变更 ===========
        // 比较差异,字段
        const upgradeFields = upgradeDatabase.fields;
        if (!upgradeFields) {
          throw new Error('database template fields not found, every database must have one field at least');
        }
        operations.push(...databaseSO.upgradeFieldOperation(user.id, upgradeFields));
      } else {
        // 没有此对应的, 创建数据库类型节点
        const { id, operation: createDatabaseNodeOperation } =
          this._templateFolder.createDatabaseNodeOperationWithTemplate(user.id, {
            preNodeId: lastNodeId,
            databaseTemplate: upgradeDatabase,
          });
        lastNodeId = id;
        operations.push(createDatabaseNodeOperation);
      }
    } else {
      // 没有templateId, 创建数据库类型节点
      const { id, operation: createDatabaseNodeOperation } =
        this._templateFolder.createDatabaseNodeOperationWithTemplate(user.id, {
          preNodeId: lastNodeId,
          databaseTemplate: upgradeDatabase,
        });
      lastNodeId = id;
      operations.push(createDatabaseNodeOperation);
    }
    return { lastNodeId, operations };
  }

  private async migrateAutomationNode(
    upgradeAutomation: Automation,
    preNodeId?: string,
  ): Promise<{ lastNodeId?: string; operations: NodeOperations; callbackFns?: (() => Promise<void>)[] }> {
    const { user } = this._context;
    let lastNodeId = preNodeId;
    const operations: NodeOperations = [];
    // 连接
    this.initResourceRelation(this.templateId, upgradeAutomation);
    let callbackFns;
    // 变更automation类型节点
    if (upgradeAutomation.templateId) {
      // 存在templateId, 通过templateId和模板对应根节点来找到原来的节点
      const automationNodeSO = await this._templateFolder.findChildNodeByTemplateId(upgradeAutomation.templateId);
      if (automationNodeSO) {
        // 找到匹配的节点,比较差异
        const automationSO = await automationNodeSO.toResourceSO<AutomationSO>();
        // 修改自动化节点的名称和描述
        const updateAutomationOperation = automationSO.updateOperation({
          name: upgradeAutomation.name,
          description: upgradeAutomation.description,
          updatedBy: user.id,
          node: {
            update: {
              name: upgradeAutomation.name,
              description: upgradeAutomation.description,
              updatedBy: user.id,
            },
          },
        });
        operations.push(updateAutomationOperation);

        // trigger变更
        const { triggers } = upgradeAutomation;
        if (triggers) {
          const { triggerOperations, callbackFns: fns } = await automationSO.updateTriggersOperation(
            user.id,
            triggers,
            true,
          );
          operations.push(...triggerOperations);
          callbackFns = fns;
        }

        // actions 变更
        const { actions } = upgradeAutomation;
        if (actions) {
          const actionOperations = await automationSO.updateActionsOperation(user.id, actions);
          if (actionOperations.length > 0) {
            operations.push(...actionOperations);
          }
        }
      }
    } else {
      // 没有templateId，或者唯一键未匹配到，创建自动化类型节点
      const {
        automationId,
        operations: createAutomationNodeOperation,
        callbackFns: fns,
      } = await this._templateFolder.createAutomationNodeOperationWithTemplate(user.id, {
        preNodeId: lastNodeId,
        automationTemplate: upgradeAutomation,
        isTemplateOperation: true,
      });
      lastNodeId = automationId;
      operations.push(...createAutomationNodeOperation);
      callbackFns = fns;
    }
    return { lastNodeId, operations, callbackFns };
  }

  private async migrateFormNode(
    formBO: FormBO,
    preNodeId?: string,
  ): Promise<{ lastNodeId?: string; operations: NodeOperations }> {
    const { user } = this._context;
    const operations: NodeOperations = [];
    const upgradeForm = cloneDeep(formBO);
    assert(upgradeForm.formType === 'DATABASE' || upgradeForm.formType === undefined);

    if (upgradeForm.databaseTemplateId) {
      const databasePath = `${this.templateId}:${upgradeForm.databaseTemplateId}`;
      if (!this._templateIdPathStore.has(databasePath)) {
        throw new Error(`form database template id path ${databasePath} not found`);
      }
      upgradeForm.databaseId = this._templateIdPathStore.get(databasePath);
      if (upgradeForm.metadata?.type === 'VIEW' && upgradeForm.metadata.viewTemplateId) {
        const path = `${this.templateId}:${upgradeForm.databaseTemplateId}:${upgradeForm.metadata.viewTemplateId}`;
        if (!this._templateIdPathStore.has(path)) {
          throw new Error(`form view template id path ${path} not found`);
        }
        upgradeForm.metadata.viewId = this._templateIdPathStore.get(path);
      }
      if (upgradeForm.metadata?.type === 'FIELD') {
        upgradeForm.metadata.fields = upgradeForm.metadata.fields.map((field) => {
          if (!field.fieldTemplateId) {
            return field;
          }
          const path = `${this.templateId}:${upgradeForm.databaseTemplateId}:${field.fieldTemplateId}`;
          const fieldId = this._templateIdPathStore.get(path);
          return { ...field, fieldId };
        });
      }
    }
    const { id: formId, operation: createFormOperation } = FormSO.upsertWithNodeInput(
      user.id,
      { ...upgradeForm, id: upgradeForm.id! },
      {
        spaceId: this._templateFolder.spaceId,
        parentId: this._templateFolder.id,
        preNodeId,
      },
    );
    operations.push(createFormOperation);
    return { lastNodeId: formId, operations };
  }

  private async migrateMirrorNode(
    mirrorBO: DatabaseViewMirrorBO,
    preNodeId?: string,
  ): Promise<{ lastNodeId?: string; operations: NodeOperations }> {
    const { user } = this._context;
    const operations: NodeOperations = [];
    const upgradeMirror = cloneDeep(mirrorBO);
    if (upgradeMirror.mirrorType === MirrorTypeEnum.DATABASE_VIEW) {
      if (upgradeMirror.databaseTemplateId) {
        const databasePath = `${this.templateId}:${upgradeMirror.databaseTemplateId}`;
        if (!this._templateIdPathStore.has(databasePath)) {
          throw new Error(`mirror database template id path ${databasePath} not found`);
        }
        upgradeMirror.databaseId = this._templateIdPathStore.get(databasePath);
        if (upgradeMirror.viewTemplateId) {
          const viewTemplateIdPath = `${databasePath}:${upgradeMirror.viewTemplateId}`;
          if (!this._templateIdPathStore.has(viewTemplateIdPath)) {
            throw new Error(`mirror view template id path ${viewTemplateIdPath} not found`);
          }
          upgradeMirror.viewId = this._templateIdPathStore.get(viewTemplateIdPath);
        }
      }
    }
    // 变更mirror类型节点
    const { id: mirrorId, operation: createMirrorOperation } = MirrorSO.upsertWithNodeInput(
      user.id,
      { ...upgradeMirror, id: upgradeMirror.id! },
      {
        spaceId: this._templateFolder.spaceId,
        parentId: this._templateFolder.id,
        preNodeId,
      },
    );
    operations.push(createMirrorOperation);
    return { lastNodeId: mirrorId, operations };
  }

  private async migrateNodeResources(
    upgradeNodes: NodeResource[],
  ): Promise<{ nodeOperations: NodeOperations; callbackFns: (() => Promise<void>)[] }> {
    const operations: NodeOperations = [];
    const callbackFns: (() => Promise<void>)[] = [];

    // set last child node as previous node
    const lastChildNode = await this._templateFolder.lastChildNode();
    let preNodeId = lastChildNode?.id;
    // 遍历升级模板的节点
    // 比较每个节点的差异
    // 如果节点不存在，则创建,并且按序创建
    // 如果节点存在，则更新
    for (const upgradeNode of upgradeNodes) {
      const { resourceType: nodeType } = upgradeNode;
      switch (nodeType) {
        case 'DATABASE':
          {
            // 升级数据库类型节点
            const upgradeDatabase = upgradeNode as Database;
            if (!upgradeDatabase) {
              // database not found in database node type, skip
              break;
            }
            const { lastNodeId, operations: databaseOperations } = await this.migrateDatabaseNode(
              upgradeDatabase,
              preNodeId,
            );
            operations.push(...databaseOperations);
            preNodeId = lastNodeId;
          }
          break;
        case 'AUTOMATION':
          {
            // 升级自动化类型节点
            const upgradeAutomation = upgradeNode as Automation;
            if (!upgradeAutomation) {
              // automation not found in automation node type, skip
              break;
            }
            const {
              lastNodeId,
              operations: automationOperations,
              callbackFns: fns,
            } = await this.migrateAutomationNode(upgradeAutomation, preNodeId);
            operations.push(...automationOperations);
            preNodeId = lastNodeId;
            if (fns) {
              callbackFns.push(...fns);
            }
          }
          break;
        case 'FORM':
          {
            // 升级表单类型节点
            const upgradeForm = upgradeNode as FormBO;
            if (!upgradeForm) {
              // form not found in automation node type, skip
              break;
            }
            const { lastNodeId, operations: formOperations } = await this.migrateFormNode(upgradeForm, preNodeId);
            operations.push(...formOperations);
            preNodeId = lastNodeId;
          }
          break;
        case 'MIRROR':
          {
            const upgradeMirror = upgradeNode as DatabaseViewMirrorBO;
            if (!upgradeMirror) {
              // mirror not found in automation node type, skip
              break;
            }
            const { lastNodeId, operations: mirrorOperations } = await this.migrateMirrorNode(upgradeMirror, preNodeId);
            operations.push(...mirrorOperations);
            preNodeId = lastNodeId;
          }
          break;
        default:
          break;
      }
    }
    return { nodeOperations: operations, callbackFns };
  }

  private async migratePresetUnit(presetUnits?: Unit[]): Promise<UnitOperations> {
    if (!presetUnits || isEmpty(presetUnits)) {
      return [];
    }
    const { user } = this._context;
    const { spaceId } = this._templateFolder;
    const operations: UnitOperations = [];
    for (const presetUnit of presetUnits) {
      const roleUnit = UnitRoleSchema.parse(presetUnit);
      if (roleUnit.templateId) {
        // 有templateId设置,更新或创建
        const role = await RoleSO.findByTemplateId(spaceId, roleUnit.templateId);
        if (role) {
          // 更新role的属性
          operations.push(
            role.updateOperation({
              name: roleUnit.name,
              updatedBy: user.id,
            }),
          );
        } else {
          // 创建role
          operations.push(RoleSO.createRoleOperationWithBO(user.id, spaceId, roleUnit));
        }
      } else {
        // 没有templateId, 创建role
        operations.push(RoleSO.createRoleOperationWithBO(user.id, spaceId, roleUnit));
      }
    }
    return operations;
  }

  private async initTemplateState(template: CustomTemplate) {
    const ignoredTemplate = _.cloneDeep(template);
    if (template.ignoreChanged && template.ignoreChanged.length > 0) {
      for (const ignore of template.ignoreChanged) {
        if (!TemplateIgnoreChangedEnumSchema.enum[ignore as TemplateIgnoreChangedEnum]) {
          _.set(ignoredTemplate, ignore, undefined);
        }
      }
    }

    if (template.presetUnits) {
      for (const presetUnit of template.presetUnits) {
        const { type, templateId } = presetUnit;
        if (templateId) {
          if (type === 'ROLE') {
            const role = await RoleSO.findByTemplateId(this._templateFolder.spaceId, templateId);
            const unitId = role?.id || UnitFactory.generateUnitId(type);
            presetUnit.id = unitId;
            this._templateIdPathStore.set(`${template.templateId}:${type}:${presetUnit.templateId}`, unitId);
          }
        }
      }
    }

    const initDatabaseNodeState = (database: Database, so?: DatabaseSO) => {
      if (database.views) {
        for (const view of database.views) {
          if (view.templateId) {
            const viewTemplateId = view.templateId;
            const matched = so?.viewModels.find((v) => v.templateId === viewTemplateId);
            view.id = matched?.id || generateNanoID(CONST_PREFIX_VIEW);
            const path = `${template.templateId}:${database.templateId}:${viewTemplateId}`;
            this._templateIdPathStore.set(path, view.id);
            if (view.templateId.startsWith(CONST_PREFIX_VIEW)) {
              this._templateIdPathStore.set(view.templateId, view.id);
            }
          } else {
            view.id = generateNanoID(CONST_PREFIX_VIEW);
          }
        }
      }
      if (database.fields) {
        for (const field of database.fields) {
          if (field.templateId) {
            const fieldTemplateId = field.templateId;
            const matched = so?.fieldModels.find((f) => f.templateId === fieldTemplateId);
            field.id = matched?.id || generateNanoID(CONST_PREFIX_FIELD);
            const path = `${template.templateId}:${database.templateId}:${fieldTemplateId}`;
            this._templateIdPathStore.set(path, field.id);
            if (field.templateId.startsWith(CONST_PREFIX_FIELD)) {
              this._templateIdPathStore.set(field.templateId, field.id);
            }
          } else {
            field.id = generateNanoID(CONST_PREFIX_FIELD);
          }
        }
      }
    };

    const initAutomationNodeState = async (automation: Automation, so?: AutomationSO) => {
      const ignoredAutomation = ignoredTemplate.resources.find(
        (i) => i.templateId === automation.templateId,
      ) as Automation;
      if (automation.actions) {
        const actionMap = _.keyBy(await so?.getActions(), 'templateId');
        const setActionIdStore = (data: Action) => {
          const actionTemplateId = data.templateId;
          if (!actionTemplateId) {
            return;
          }
          const actionId =
            actionTemplateId in actionMap ? actionMap[actionTemplateId].id : generateNanoID(CONST_PREFIX_ACTION);
          this._templateIdPathStore.set(`${template.templateId}:${automation.templateId}:${data.templateId}`, actionId);
          // eslint-disable-next-line no-param-reassign
          data.id = actionId;
        };
        for (let action of automation.actions) {
          if (action.templateId) {
            const actionTemplateId = action.templateId;
            const ignoreAction = ignoredAutomation?.actions?.find((a) => a.templateId === actionTemplateId);
            if (ignoreAction) {
              action = filterUndefinedValue(ignoreAction, actionMap[actionTemplateId] as object) as Action;
            }
            setActionIdStore(action);
            // nested actions
            if ('actions' in action && action.actions) {
              for (const subAction of action.actions) {
                setActionIdStore(subAction);
              }
            }
          } else {
            action.id = generateNanoID(CONST_PREFIX_ACTION);
          }
        }
      }
      if (automation.triggers) {
        const triggerMap = _.keyBy(await so?.getTriggers(), 'templateId');
        for (let trigger of automation.triggers) {
          if (trigger.templateId) {
            const triggerTemplateId = trigger.templateId;
            const ignoreTrigger = ignoredAutomation?.triggers?.find((a) => a.templateId === triggerTemplateId);
            if (ignoreTrigger) {
              trigger = filterUndefinedValue(ignoreTrigger, triggerMap[triggerTemplateId] as object) as Trigger;
            } else {
              trigger.id =
                triggerTemplateId in triggerMap
                  ? triggerMap[triggerTemplateId].id
                  : generateNanoID(CONST_PREFIX_TRIGGER);
              const path = `${template.templateId}:${automation.templateId}:${triggerTemplateId}`;
              this._templateIdPathStore.set(path, trigger.id);
            }
          } else {
            trigger.id = generateNanoID(CONST_PREFIX_TRIGGER);
          }
        }
      }
    };

    const initNodeState = async (resource: NodeResource) => {
      if (resource.resourceType === 'DATABASE') {
        const database = resource as Database;
        if (database.templateId) {
          const databaseTemplateId = database.templateId;
          const databaseNodeSO = await this._templateFolder.findChildNodeByTemplateId(databaseTemplateId);
          database.id = databaseNodeSO?.id || generateNanoID(CONST_PREFIX_DAT);
          const path = `${template.templateId}:${databaseTemplateId}`;
          this._templateIdPathStore.set(path, database.id);
          const databaseSO = await databaseNodeSO?.toResourceSO<DatabaseSO>();
          initDatabaseNodeState(database, databaseSO);
          if (database.templateId.startsWith(CONST_PREFIX_DAT)) {
            this._templateIdPathStore.set(database.templateId, database.id);
          }
        }
      }
      if (resource.resourceType === 'FOLDER') {
        const children = (resource as Folder).children;
        if (!children) {
          return;
        }
        for (const child of children) {
          initNodeState(child as NodeResource);
        }
      }
      if (resource.resourceType === 'FORM') {
        const form = resource as FormBO;
        if (form.templateId) {
          const formNodeSO = await this._templateFolder.findChildNodeByTemplateId(form.templateId);
          form.id = formNodeSO?.id || generateNanoID(CONST_PREFIX_DAT);
          const path = `${template.templateId}:${form.templateId}`;
          this._templateIdPathStore.set(path, form.id);
        }
      }
      if (resource.resourceType === 'MIRROR') {
        const mirror = resource as MirrorBO;
        if (mirror.templateId) {
          const mirrorNode = await this._templateFolder.findChildNodeByTemplateId(mirror.templateId);
          mirror.id = mirrorNode?.id || generateNanoID(CONST_PREFIX_MIRROR);
          const path = `${template.templateId}:${mirror.templateId}`;
          this._templateIdPathStore.set(path, mirror.id);
        }
      }
      if (resource.resourceType === 'AUTOMATION') {
        const automation = resource as Automation;
        if (automation.templateId) {
          const automationNodeSO = await this._templateFolder.findChildNodeByTemplateId(automation.templateId);
          automation.id = automationNodeSO?.id || generateNanoID(CONST_PREFIX_AUT);
          const path = `${template.templateId}:${automation.templateId}`;
          this._templateIdPathStore.set(path, automation.id);
          const automationSO = await automationNodeSO?.toResourceSO<AutomationSO>();
          initAutomationNodeState(automation, automationSO);
        }
      }
    };

    for (const resource of template.resources) {
      if (resource.templateId) {
        await initNodeState(resource);
      }
    }
  }

  public async execute(skipVersionCompare?: boolean) {
    // query template id by template node id
    const templateInstallation = await this._templateFolder.getInstallation();
    // check whether this space has applied this template
    if (!templateInstallation) {
      throw new Error('are you sure you have installed this template? upgrade operation only for installed template');
    }
    // check whether template can upgrade
    const canUpgrade = skipVersionCompare || templateInstallation.canUpgrade();
    if (!canUpgrade) {
      // 是否需要不理睬?直接返回?
      throw new Error('this template can not upgrade because of no increment version found');
    }
    // 获取最新的模板内容
    const { upgradeTemplate: template } = templateInstallation;
    await this.initTemplateState(template);
    // console.log(`初始化的模板状态: ${JSON.stringify(this._templateIdPathStore)}`);
    // DB 操作集合
    const operations: PrismaOperations = [];
    // Mongo 操作集合
    const mongoCallbackSessions: MongoTransactionCB[] = [];
    const { user } = this._context;
    // 模板对应的节点名称变更
    const { operations: templateFolderOperations, mongoSessions } = await this._templateFolder.updateWithNodeInput(
      user,
      {
        resourceType: 'FOLDER',
        name: template.name,
        description: template.description,
        cover:
          typeof template.cover === 'string'
            ? {
                type: 'URL',
                url: template.cover,
              }
            : template.cover,
      },
    );
    operations.push(...templateFolderOperations);
    mongoCallbackSessions.push(...mongoSessions);
    // migrate 预设角色
    operations.push(...(await this.migratePresetUnit(template.presetUnits)));
    // migrate 节点资源
    const { nodeOperations, callbackFns } = await this.migrateNodeResources(template.resources);
    operations.push(...nodeOperations);
    // 更新模板和空间站的关系
    operations.push(templateInstallation.updateOperation(user.id, template));
    // 一起执行所有migrate操作, 保证事务性
    await db.mongo.transaction(async (session) => {
      for (const mongoSession of mongoCallbackSessions) {
        await mongoSession(session);
      }
      await db.prisma.$transaction(operations);
    });

    // 异步执行回调函数
    if (callbackFns) {
      Promise.allSettled(callbackFns.map((fn) => fn()));
    }
  }
}
