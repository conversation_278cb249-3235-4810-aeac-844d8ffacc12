import { z } from 'zod';
import { ReportSO } from '@bika/domains/report/server/report-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { protectedProcedure, router } from '@bika/server-orm/trpc';

export const reportRouter = router({
  fetchReport: protectedProcedure.input(z.object({ reportId: z.string() })).query(async (opts) => {
    const { input, ctx } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    const reportSO = await ReportSO.init(input.reportId);
    const member = await user.getMember(reportSO.spaceId);
    return reportSO.toDetailVO(member.id);
  }),

  /**
   * mark read all my reports
   */
  markReadAll: protectedProcedure.input(z.object({ spaceId: z.string() })).mutation(async (opts) => {
    const { input, ctx } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    const member = await user.getMember(input.spaceId);
    const reports = await ReportSO.getMemberReports(member.id);

    const promises = reports.map((report) =>
      report.read(undefined, {
        recipientType: 'MEMBER',
        recipientId: member.id,
      }),
    );

    return Promise.all(promises);
  }),

  markRead: protectedProcedure
    .input(
      z.object({
        reportId: z.string(),
        dwellTime: z.number(), // 停留多少秒
      }),
    )
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      const user = await UserSO.init(ctx.session!.userId);
      const reportSO = await ReportSO.init(input.reportId);
      const member = await user.getMember(reportSO.spaceId);
      return reportSO.read(input.dwellTime, {
        recipientType: 'MEMBER',
        recipientId: member.id,
      });
    }),
});
