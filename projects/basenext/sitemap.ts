import type { MetadataRoute } from 'next';

function getFileName(id?: number) {
  if (id == null) {
    return 'sitemap.xml';
  }
  /**
   * https://nextjs.org/docs/app/api-reference/file-conventions/metadata/sitemap#generating-multiple-sitemaps
   * In production, your generated sitemaps will be available at /.../sitemap/[id].xml. For example, /product/sitemap/1.xml.
   * In development, you can view the generated sitemap on /.../sitemap.xml/[id]. For example, /product/sitemap.xml/1. This difference is temporary and will follow the production format.
   * See the generateSitemaps API reference for more information.
   */
  // return getAppEnv() === 'LOCAL' ? `sitemap.xml/${id}` : `sitemap/${id}.xml`;
  return `sitemap/${id}.xml`;
}
// function getLoc(path: string, id?: number) {
//   return `${process.env.APP_HOSTNAME}/${path}-${getFileName(id)}`;
// }
function getSitemapIndex(sitemapIndex: ISitemapIndex) {
  return /* XML */ `<sitemap><loc>${sitemapIndex.url}</loc><lastmod>${sitemapIndex.date}</lastmod></sitemap>`;
}

export type ISitemapItem = {
  url: string;
  date: string;
};

export type ISitemapIndex = {
  url: string;
  date: string;
};

export async function sitemapIndexGET(sitemapIndexes: ISitemapIndex[], stylesheet?: string) {
  const xml = /* XML */ `<?xml version="1.0" encoding="UTF-8"?>
${stylesheet ? `<?xml-stylesheet type="text/xsl" href="${stylesheet}"?>` : ''}
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  ${sitemapIndexes.map((idx) => getSitemapIndex(idx))}
</sitemapindex>
  `;

  return new Response(xml, {
    headers: {
      'Content-Type': 'application/xml',
    },
  });
}

// stylesheet style，sitemap.xml，Reference: https://aitable.ai/sitemap.xml
export async function sitemapStyle(props: { copyrightName: string; copyrightUrl: string }) {
  const xsl = `<?xml version='1.0' encoding='UTF-8'?>
<xsl:stylesheet version='2.0'
	xmlns:html='http://www.w3.org/TR/REC-html40'
	xmlns:sitemap='http://www.sitemaps.org/schemas/sitemap/0.9'
	xmlns:xsl='http://www.w3.org/1999/XSL/Transform'>
<xsl:output method='html' version='1.0' encoding='UTF-8' indent='yes'/>
<xsl:template match="/">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>${props.copyrightName} XML Sitemap</title>
	<meta http-equiv='Content-Type' content='text/html; charset=utf-8'/>
	<style type='text/css'>
		body {
			font: 14px 'Open Sans', Helvetica, Arial, sans-serif;
			margin: 0;
		}

		a {
			color: #3498db;
			text-decoration: none;
		}

		h1 {
			margin: 0;
		}

		#description {
			background-color: #f0f2eb;
			color: #000;
			padding: 30px 30px 20px;
		}

		#description a {
			color: #008710;
		}

		#content {
			padding: 10px 30px 30px;
			background: #fff;
		}

		a:hover {
			border-bottom: 1px solid;
		}

		th, td {
			font-size: 12px;
		}

		th {
			text-align: left;
			border-bottom: 1px solid #ccc;
		}

		th, td {
			padding: 10px 15px;
		}

		.odd {
			background: linear-gradient( 159.87deg, #f6f6f4 7.24%, #f7f4ea 64.73%, #ddedd5 116.53% );
		}

		#footer {
			margin: 20px 30px;
			font-size: 12px;
			color: #999;
		}

		#footer a {
			color: inherit;
		}

		#description a, #footer a {
			border-bottom: 1px solid;
		}

		#description a:hover, #footer a:hover {
			border-bottom: none;
		}

		img {
			max-height: 100px;
			max-width: 100px;
		}
	</style>
</head>
<body>
	<div id='description'>
		<h1>${props.copyrightName} XML Sitemap</h1>
		<p>This is an XML Sitemap generated by <a href="${props.copyrightUrl}">${props.copyrightName} </a>, meant to be consumed by search engines like <a href="https://www.google.com/">Google</a> or <a href="https://www.bing.com/">Bing</a>.</p>
		<p>You can find more information on XML sitemaps at <a href="https://sitemaps.org">sitemaps.org</a></p>
	</div>
	<div id='content'>
		<!-- <xsl:value-of select="count(sitemap:urlset/sitemap:url)"/> -->
		<table>
			<tr>
				<th>#</th>
				<th>URL</th>
				<th>Last Modified</th>
			</tr>
			<xsl:for-each select="sitemap:urlset/sitemap:url">
				<tr>
					<xsl:choose>
						<xsl:when test='position() mod 2 != 1'>
							<xsl:attribute name="class">odd</xsl:attribute>
						</xsl:when>
					</xsl:choose>
					<td>
						<xsl:value-of select = "position()" />
					</td>
					<td>
						<xsl:variable name='itemURL'>
							<xsl:value-of select='sitemap:loc'/>
						</xsl:variable>
						<a href='{$itemURL}'>
							<xsl:value-of select='sitemap:loc'/>
						</a>
					</td>
					<td>
						<xsl:value-of select='sitemap:lastmod'/>
					</td>
				</tr>
			</xsl:for-each>
		</table>
	</div>
	<div id='footer'>
		<p>Generated by <a href="${props.copyrightUrl}">${props.copyrightName}</a></p>
	</div>
</body>
</html>
</xsl:template>
</xsl:stylesheet>
`;
  return new Response(xsl, {
    headers: {
      'Content-Type': 'application/xml',
    },
  });
}

//   ${getSitemap('cities')}
//   ${getSitemapsWithLangs(await generateHomeAndTemplatesSitemap(), 'template', i18n.locales)}
export async function sitemapIndexStyle(props: { copyrightName: string; copyrightUrl: string }) {
  // Stylesheet for sitemap.xml, Reference: https://aitable.ai/sitemap.xml

  const xsl = `<?xml version='1.0' encoding='UTF-8'?>
<xsl:stylesheet version='2.0'
	xmlns:html='http://www.w3.org/TR/REC-html40'
	xmlns:sitemap='http://www.sitemaps.org/schemas/sitemap/0.9'
	xmlns:xsl='http://www.w3.org/1999/XSL/Transform'>
<xsl:output method='html' version='1.0' encoding='UTF-8' indent='yes'/>
<xsl:template match="/">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>${props.copyrightName} XML Sitemap Index</title>
	<meta http-equiv='Content-Type' content='text/html; charset=utf-8'/>
	<style type='text/css'>
		body {
			font: 14px 'Open Sans', Helvetica, Arial, sans-serif;
			margin: 0;
		}

		a {
			color: #3498db;
			text-decoration: none;
		}

		h1 {
			margin: 0;
		}

		#description {
			background-color: #f0f2eb;
			color: #000;
			padding: 30px 30px 20px;
		}

		#description a {
			color: #008710;
		}

		#content {
			padding: 10px 30px 30px;
			background: #fff;
		}

		a:hover {
			border-bottom: 1px solid;
		}

		th, td {
			font-size: 12px;
		}

		th {
			text-align: left;
			border-bottom: 1px solid #ccc;
		}

		th, td {
			padding: 10px 15px;
		}

		.odd {
			background: linear-gradient( 159.87deg, #f6f6f4 7.24%, #f7f4ea 64.73%, #ddedd5 116.53% );
		}

		#footer {
			margin: 20px 30px;
			font-size: 12px;
			color: #999;
		}

		#footer a {
			color: inherit;
		}

		#description a, #footer a {
			border-bottom: 1px solid;
		}

		#description a:hover, #footer a:hover {
			border-bottom: none;
		}

		img {
			max-height: 100px;
			max-width: 100px;
		}
	</style>
</head>
<body>
	<div id='description'>
		<h1>${props.copyrightName} XML Sitemap Index</h1>
		<xsl:choose>
			<xsl:when test='not(sitemap:sitemapindex/sitemap:sitemap)'>
				<p><strong></strong></p>
			</xsl:when>
		</xsl:choose>
		<p>This is an XML Sitemap Index generated by <a target="_blank" href="${props.copyrightUrl}">${props.copyrightName} </a>, meant to be consumed by search engines like <a href="https://www.google.com/">Google</a> or <a href="https://www.bing.com/">Bing</a>.</p>
		<p>You can find more information on XML sitemaps at <a target="_blank" href="https://sitemaps.org">sitemaps.org</a></p>
	</div>
	<div id='content'>
		<table>
			<tr>
				<th>#</th>
				<th>Sitemap URL</th>
				<th>Last Modified</th>
			</tr>
			<xsl:for-each select='sitemap:sitemapindex/sitemap:sitemap'>
				<tr>
					<xsl:choose>
						<xsl:when test='position() mod 2 != 1'>
							<xsl:attribute name="class">odd</xsl:attribute>
						</xsl:when>
					</xsl:choose>
					<td>
						<xsl:value-of select = "position()" />
					</td>
					<td>
						<xsl:variable name='itemURL'>
							<xsl:value-of select='sitemap:loc'/>
						</xsl:variable>
						<a href='{$itemURL}'>
							<xsl:value-of select='sitemap:loc'/>
						</a>
					</td>
					<td>
						<xsl:value-of select='sitemap:lastmod'/>
					</td>
				</tr>
			</xsl:for-each>
		</table>
	</div>
	<div id='footer'>
		<p>Generated by <a target="_blank" href="${props.copyrightUrl} ">${props.copyrightName} </a></p>
	</div>
</body>
</html>
</xsl:template>
</xsl:stylesheet>
`;
  return new Response(xsl, {
    headers: {
      'Content-Type': 'application/xml',
    },
  });
}

export function genSitemapXml(sitemap: MetadataRoute.Sitemap): string {
  const urls: string[] = [];
  for (const url of sitemap) {
    urls.push(`<url>
  <loc>${url.url}</loc>
  <lastmod>${((url.lastModified as Date) || new Date()).toISOString()}</lastmod>
  <changefreq>${url.changeFrequency}</changefreq>
  <priority>${url.priority}</priority>${
    url.alternates?.languages
      ? Object.entries(url.alternates.languages).map(
          ([altLang, altLangUrl]) => `
  <xhtml:link rel="alternate" hreflang="${altLang}" href="${altLangUrl}" />`,
        )
      : ''
  }
</url>`);
  }
  return `<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="/sitemap/sitemap-stylesheet.xsl"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml">
${urls.join('\n')}
</urlset>
`;
}
