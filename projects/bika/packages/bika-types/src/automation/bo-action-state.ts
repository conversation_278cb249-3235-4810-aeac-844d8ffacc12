import { z } from 'zod';
import { ActionOutputSchema } from './bo-action-output';
import { BaseTestResultSchema } from './bo-trigger-state';

export const BaseActionTestResultSchema = BaseTestResultSchema(ActionOutputSchema);

const BaseActionStateSchema = z.object({
  testResult: BaseActionTestResultSchema.and(z.object({ runTime: z.string() })).optional(),
});

export const RandomActionStateSchema = BaseActionStateSchema.extend({
  // The flag value of the last selection. Usually it's some ID
  lastSelectedFlag: z.unknown().optional(),
}).default({});

export const RoundRobinActionStateSchema = RandomActionStateSchema;

export const ActionStateSchema = z.union([BaseActionStateSchema, RandomActionStateSchema, RoundRobinActionStateSchema]);
export type ActionState = z.infer<typeof ActionStateSchema>;
