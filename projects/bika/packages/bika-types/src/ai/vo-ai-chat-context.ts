import { z } from 'zod';
import { AttachmentVOSchema } from '../attachment/vo-attachment';
import { NodeTreeVOSchema } from '../node/vo-node';

export const AIChatContextVOSchema = z.discriminatedUnion('type', [
  z.object({
    type: z.literal('node'),
    node: NodeTreeVOSchema,
  }),
  z.object({
    type: z.literal('attachment'),
    attachment: AttachmentVOSchema,
  }),
]);

// Why it is VO?  Use by Chat View, fetch API and pass Value to UI Renderer
export type AIChatContextVO = z.infer<typeof AIChatContextVOSchema>;
