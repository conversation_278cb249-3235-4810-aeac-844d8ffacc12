import { z } from 'zod';

export const AIIntentTypes = [
  'ADMIN', // For Admin Chat
  'DEBUGGER',

  // Free Chat
  'CHAT', // 'Basic Gossip Chat'

  'SEARCH', // Basic AI Search

  // Build AI app
  'BUILDER',

  // Space Super Agent
  'SUPERVISOR',

  // For Node Detail Copilot
  'COPILOT',

  // AI Agent Node
  'AI_NODE',

  // AI Page
  'PAGE',

  'CREATE_RECORD', // 'Create Database Record'
  'CREATE_REMINDER', // 'Create Reminder'
  'IMPORT_VIKA_DATASOURCE',
  // 'ONBOARDING_INIT', // 'Onboarding Init, the 1st stage'
  // 'ONBOARDING_UI', // 'Onboarding Init, the 2st stage'
  // 'ONBOARDING_AUTH', // 'Onboarding Init, the 3st stage'
  // 'ONBOARDING_TRIAL', // 'Onboarding Init, the 4st stage'
  'CREATE_NODE_RESOURCE', // 'Create node source, database, automation, eg...'
  'STEP_WIZARD', // 'Step by Step wizard with easy human configuration'
  'SUPERVISOR', // 'Supervisor', super agent
] as const;

export const AIIntentTypeSchema = z.enum(AIIntentTypes);
export type AIIntentType = z.infer<typeof AIIntentTypeSchema>;
