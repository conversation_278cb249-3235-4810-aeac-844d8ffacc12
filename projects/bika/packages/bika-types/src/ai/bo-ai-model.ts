import { z } from 'zod';

// Hardcode supported model configurations for easy switching via a single variable
// Reference: https://openrouter.ai/models
export const PresetLanguageAIModelDefs = [
  // | 'llama3' // Facebook open source
  // | 'phi3' // Microsoft Open Source
  // | 'gemma' // Google Open Source
  // | 'mistral' // Mistral
  'mock',
  // 'gptproto',
  // 'gpt-3.5', // OpenAI,
  // 'gpt-4o-mini',
  // 'gpt-4o',
  // 'gpt-4.1',
  // 'gpt-4.1-mini',
  // 'gpt-image-1',
  // 'azure/gpt-image-1',
  // 'azure/gpt-4o-mini',
  // 'azure/gpt-4o',
  // 'azure/gpt-4.1',
  'openai/gpt-4o-mini',
  'openai/gpt-4o',
  'openai/gpt-4.1',
  'openai/gpt-4.1-mini',
  'openai/o4-mini',
  'openai/o3',

  'deepseek/deepseek-v3',
  // 'openai-o4-mini',
  // 'openai-o3',
  'deepseek/deepseek-r1',
  // 'siliconflow/DeepSeek-V3',
  // 'siliconflow/DeepSeek-R1-Distill-Qwen-7B',
  // 'siliconflow/DeepSeek-R1',
  'qwen/qwen3-coder',
  'qwen/qwen-plus',
  'qwen/qwen-turbo',
  'qwen/qwen-pro',
  'google/gemini-2.5-flash', // Google,
  'google/gemini-2.5-pro',
  'bytedance/doubao-lite-128k',
  // 'bytedance/doubao', // Bytedance,
  'bytedance/doubao-pro-32k',
  'bytedance/doubao-pro-256k',
  'anthropic/claude-sonnet-3.7',
  'anthropic/claude-haiku-3.5',
  'anthropic/claude-opus-4',
  'anthropic/claude-sonnet-4',
] as const;

export const PresetLanguageAIModelDefSchema = z.enum(PresetLanguageAIModelDefs);
export type PresetLanguageAIModelDef = z.infer<typeof PresetLanguageAIModelDefSchema>;
