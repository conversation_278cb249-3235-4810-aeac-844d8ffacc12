import { z } from 'zod';

export const AIImageModels = [
  'openai/dall-e-3',
  'openai/gpt-image-1',
  // 'azure/gpt-image-1',
  'flux/flux-kontext-pro',
] as const;
export const AIImageModelDefSchema = z.enum(AIImageModels);

export type AIImageModelDef = z.infer<typeof AIImageModelDefSchema>;

export const AIImageBOTypes = ['node-resource-icon', 'user-avatar', 'default'] as const;
export const AIImageBOTypeSchema = z.enum(AIImageBOTypes);

export type AIImageBOType = z.infer<typeof AIImageBOTypeSchema>;

// For Attachment Image Generation
export const AIImageBOSchema = z.discriminatedUnion('type', [
  z.object({
    type: z.literal(AIImageBOTypeSchema.enum['node-resource-icon']),
    // prompt: z.string().describe('The text user prompt to generate the image'),
  }),
  z.object({
    type: z.literal(AIImageBOTypeSchema.enum['user-avatar']),
  }),
  z.object({
    type: z.literal(AIImageBOTypeSchema.enum.default),
    imageModel: AIImageModelDefSchema,
  }),
]);
export type AIImageBO = z.infer<typeof AIImageBOSchema>;

export const AIGenerateImagePropsSchema = z.object({
  imageModel: AIImageModelDefSchema,
  prompt: z.string(),
  size: z.string().optional(), // `${number}x${number}` | undefined;
  n: z.number().optional(), // number | undefined;
});
export type AIGenerateImageProps = z.infer<typeof AIGenerateImagePropsSchema>;

// export type AIGenerateImageProps = {
//   imageModel: AIImageModelDef;
//   prompt: string;
//   size?: `${number}x${number}` | undefined;
//   n?: number | undefined;
// };
