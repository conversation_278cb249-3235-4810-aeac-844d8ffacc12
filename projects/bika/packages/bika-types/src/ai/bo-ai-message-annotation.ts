import { z } from 'zod';
import { AIChatContextVOSchema } from './vo-ai-chat-context';
import { SkillsetSelectDTOSchema } from '../skill/dto';
import { TalkDetailVOSchema } from '../space/vo-talk';
import { UserVOSchema } from '../user/vo-user';

export const AIMessageAnnotationSchema = z.discriminatedUnion('type', [
  z.object({
    type: z.literal('creator'),
    creator: UserVOSchema,
  }),
  z.object({
    type: z.literal('talk'),
    source: TalkDetailVOSchema, // TalkVO
  }),
  z.object({
    type: z.literal('prompts'),
    prompts: z.array(z.string()),
  }),
  z.object({
    type: z.literal('skillsets'),
    skillsets: z.array(SkillsetSelectDTOSchema),
  }),
  z.object({
    type: z.literal('artifact'),
    artifact: z.any(), // data
  }),
  z.object({
    type: z.literal('contexts'), // context can be: Attachment, Node Resource, URL and more...
    contexts: z.array(AIChatContextVOSchema),
  }),
]);
export type AIMessageAnnotation = z.infer<typeof AIMessageAnnotationSchema>;
